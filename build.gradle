plugins {
    id 'java-library'
    id 'eclipse'
    id 'org.springframework.boot' version '2.3.12.RELEASE'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id "com.palantir.docker" version "0.22.1"
    id "com.palantir.docker-run" version "0.22.1"
    //id "org.springframework.cloud" version "Hoxton.SR12"
}

group = 'com.yunhesoft.tm4'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'


ext {
    //set('springCloudVersion', "2020.0.0")
    set('springCloudVersion', "Hoxton.SR12")
}


def baseUrl = "http://developer.yunhesoft.net:8081/repository/"
def nexusUsername = 'tm4dev'
def nexusPassword = 'YH2019@bzhs!*6'
repositories {
    maven{
    	allowInsecureProtocol = true
		url baseUrl+"/tm4-group/"
		credentials {
			username nexusUsername
			password nexusPassword
		}
	}
    maven { url "https://maven.aliyun.com/repository/gradle-plugin"}
}

dependencies {

    // 引入 Spring Cloud Hoxton.SR12 的 BOM
    //implementation platform('org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR12')

    //eureka
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-server'
    //actuator
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    //admin
    implementation 'de.codecentric:spring-boot-admin-starter-client:2.3.1'

    // This dependency is exported to consumers, that is to say found on their compile classpath.
    api 'org.apache.commons:commons-math3:3.6.1'

    // This dependency is used internally, and not exposed to consumers on their own compile classpath.
    implementation 'com.google.guava:guava:29.0-jre'

    // Use JUnit Jupiter API for testing.
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.6.2'

    // Use JUnit Jupiter Engine for testing.
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.6.2'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    //implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client:2.2.9.RELEASE'


}

docker {
    name "${project.name}:${project.version}"
    // 需要打包的上下文
    files '.'
    tag 'DockerHub', "test/eureka-example:${project.version}"
}

dockerRun {
    name "${project.name}"
    image "${project.name}:${project.version}"
    ports '8761:8761'
    // 目录挂载点：
//    volumes '/Users/<USER>/logs': '/root/logs'
    clean true
}
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    // Use junit platform for unit tests
    useJUnitPlatform()
}

//读取git版本号
def getGitCommit = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}
//在可执行jar包中的mainifest.mf中增加git版本号
bootJar {
    manifest {
        attributes 'Manifest-Version': project.version, "Git-Commit": getGitCommit()
    }
    baseName = project.name + "-app"
}