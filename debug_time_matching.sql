-- 调试时间匹配问题
-- 参数：objid='ZQW9LIB5301C4LN5JG0207', datetime='2025-07-29 14:30:00'

-- 第1步：获取父级机构代码
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '1. 父级机构代码' as step_name,
    parent_orgcode
FROM parent_org;

-- 第2步：查看该objid的所有班次数据，分析时间格式
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '2. 班次时间格式分析' as step_name,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    sd.shiftclassname,
    LENGTH(sd.dbrq) as dbrq_length,
    LENGTH(sd.sbsj) as sbsj_length,
    LENGTH(sd.xbsj) as xbsj_length,
    CASE 
        WHEN sd.xbsj < sd.sbsj THEN '跨日班次'
        ELSE '同日班次'
    END as shift_type
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
ORDER BY sd.dbrq DESC, sd.sbsj DESC
LIMIT 10;

-- 第3步：详细的时间匹配分析
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '3. 详细时间匹配分析' as step_name,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    '2025-07-29' as target_date,
    '14:30:00' as target_time,
    -- 条件1：同一天的班次
    CASE WHEN sd.dbrq = '2025-07-29' THEN '日期匹配' ELSE '日期不匹配' END as date_match,
    CASE WHEN '14:30:00' BETWEEN sd.sbsj AND sd.xbsj THEN '时间在范围内' ELSE '时间不在范围内' END as time_in_range,
    -- 条件2：跨日班次前一天
    CASE WHEN sd.dbrq = '2025-07-28' THEN '前一天日期匹配' ELSE '前一天日期不匹配' END as prev_date_match,
    CASE WHEN sd.xbsj < sd.sbsj THEN '是跨日班次' ELSE '不是跨日班次' END as is_cross_day,
    CASE WHEN '14:30:00' >= sd.sbsj THEN '时间>=上班时间' ELSE '时间<上班时间' END as time_ge_start,
    -- 条件3：跨日班次当天
    CASE WHEN '14:30:00' <= sd.xbsj THEN '时间<=下班时间' ELSE '时间>下班时间' END as time_le_end,
    -- 最终匹配结果
    CASE 
        WHEN sd.dbrq = '2025-07-29' AND '14:30:00' BETWEEN sd.sbsj AND sd.xbsj THEN '匹配-同日'
        WHEN sd.dbrq = '2025-07-28' AND sd.xbsj < sd.sbsj AND '14:30:00' >= sd.sbsj THEN '匹配-跨日前'
        WHEN sd.dbrq = '2025-07-29' AND sd.xbsj < sd.sbsj AND '14:30:00' <= sd.xbsj THEN '匹配-跨日后'
        ELSE '不匹配'
    END as final_match
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
ORDER BY sd.dbrq DESC, sd.sbsj DESC
LIMIT 10;

-- 第4步：检查今天和昨天的班次数据
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '4. 今天和昨天的班次' as step_name,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    sd.shiftclassname
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
  AND sd.dbrq IN ('2025-07-29', '2025-07-28')
ORDER BY sd.dbrq DESC, sd.sbsj DESC;

-- 第5步：尝试不同的时间格式匹配
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '5. 不同时间格式测试' as step_name,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    -- 测试不同的时间格式
    CASE WHEN '14:30:00' BETWEEN sd.sbsj AND sd.xbsj THEN 'HH:MI:SS格式匹配' ELSE 'HH:MI:SS格式不匹配' END as format1,
    CASE WHEN '14:30' BETWEEN sd.sbsj AND sd.xbsj THEN 'HH:MI格式匹配' ELSE 'HH:MI格式不匹配' END as format2,
    CASE WHEN '1430' BETWEEN sd.sbsj AND sd.xbsj THEN 'HHMM格式匹配' ELSE 'HHMM格式不匹配' END as format3
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
  AND sd.dbrq = '2025-07-29'
ORDER BY sd.sbsj;

-- 第6步：检查是否有当前时间附近的班次
WITH parent_org AS (
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
    ) as parent_orgcode
)
SELECT 
    '6. 当前时间附近的班次' as step_name,
    sd.dbrq,
    sd.sbsj,
    sd.xbsj,
    sd.shiftclassid,
    sd.shiftclassname,
    -- 计算时间差
    CASE 
        WHEN sd.sbsj <= '14:30:00' AND sd.xbsj >= '14:30:00' THEN '时间在班次内'
        WHEN sd.sbsj > '14:30:00' THEN '班次还未开始，差' || (sd.sbsj::TIME - '14:30:00'::TIME)
        WHEN sd.xbsj < '14:30:00' THEN '班次已结束，差' || ('14:30:00'::TIME - sd.xbsj::TIME)
        ELSE '其他情况'
    END as time_relation
FROM shift_data sd, parent_org po
WHERE sd.orgcode = po.parent_orgcode
  AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
  AND sd.dbrq BETWEEN '2025-07-28' AND '2025-07-29'
ORDER BY sd.dbrq DESC, sd.sbsj DESC;
