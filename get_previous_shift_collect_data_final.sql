-- PostgreSQL 存储过程：获取上个班次的采集点数据（最终版）
-- 根据当班查询上个班次录入的采集点明细数据
-- 参数：
-- p_objid: 当前班次的机构编码（必填）
-- p_acctobj_id: 核算对象ID（必填）

CREATE OR REPLACE FUNCTION get_previous_shift_collect_data_final(
    p_objid VARCHAR(200),
    p_acctobj_id VARCHAR(200)
)
RETURNS SETOF RECORD AS $$
DECLARE
    now_dt TIMESTAMP;
    parent_orgcode VARCHAR(200);
    collect_points_cursor CURSOR FOR
        SELECT DISTINCT mx.collect_point
        FROM acctobj_inputmx mx
        WHERE mx.tmused = 1
          AND EXISTS (
              SELECT 1
              FROM acctobj_input ai
              INNER JOIN (
                  SELECT s.shiftclassid, s.sbsj, s.objid
                  FROM shift_data s
                  INNER JOIN (
                      SELECT sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
                      FROM shift_data sd
                      WHERE sd.orgcode = (
                          SELECT COALESCE(
                              (SELECT porgcode FROM sys_org_relation WHERE orgcode = p_objid AND used = 1),
                              (SELECT orgcode FROM shift_data WHERE objid = p_objid LIMIT 1)
                          )
                      )
                        AND sd.objid = p_objid
                        AND sd.sbsj <= CURRENT_TIMESTAMP
                        AND sd.xbsj > CURRENT_TIMESTAMP
                      ORDER BY sd.dbrq DESC, sd.sbsj DESC
                      LIMIT 1
                  ) cs ON s.orgcode = cs.orgcode
                  WHERE s.xbsj = cs.sbsj
                  ORDER BY s.dbrq DESC, s.sbsj DESC
                  LIMIT 1
              ) ps ON (
                  ai.acctobj_id = p_acctobj_id
                  AND ai.bcdm = ps.shiftclassid
                  AND ai.sbsj = ps.sbsj
                  AND ai.team_id = ps.objid
              )
              WHERE ai.tmused = 1
                AND ai.id = mx.ipt_id
          )
        ORDER BY mx.collect_point;

    collect_point_name VARCHAR(200);
    dynamic_sql TEXT;
    column_list TEXT := '';
    case_statements TEXT := '';
    first_column BOOLEAN := TRUE;
BEGIN
    -- 获取当前时间
    now_dt := CURRENT_TIMESTAMP;

    -- 获取父级机构代码
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = p_objid AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = p_objid LIMIT 1)
    ) INTO parent_orgcode;

    -- 动态构建列名和CASE语句
    FOR collect_point_name IN collect_points_cursor LOOP
        IF first_column THEN
            column_list := 'input_time VARCHAR, job_input_time VARCHAR, ipt_id VARCHAR, device_name VARCHAR';
            first_column := FALSE;
        END IF;

        -- 添加列定义（用于返回类型）
        column_list := column_list || ', "' || collect_point_name || '" VARCHAR';

        -- 添加CASE语句
        case_statements := case_statements ||
            ', MAX(CASE WHEN mx.collect_point = ''' || collect_point_name ||
            ''' THEN mx.collect_point_val END)::VARCHAR as "' || collect_point_name || '"';
    END LOOP;

    -- 如果没有找到采集点，返回空结果
    IF column_list = '' THEN
        RETURN;
    END IF;

    -- 构建动态SQL
    dynamic_sql := '
    WITH current_shift AS (
        SELECT
            sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
        FROM shift_data sd
        WHERE sd.orgcode = ''' || parent_orgcode || '''
          AND sd.objid = ''' || p_objid || '''
          AND sd.sbsj <= ''' || now_dt || '''
          AND sd.xbsj > ''' || now_dt || '''
        ORDER BY sd.dbrq DESC, sd.sbsj DESC
        LIMIT 1
    ),
    previous_shift AS (
        SELECT
            s.shiftclassid,
            s.sbsj,
            s.objid
        FROM shift_data s
        INNER JOIN current_shift cs ON s.orgcode = cs.orgcode
        WHERE s.xbsj = cs.sbsj
        ORDER BY s.dbrq DESC, s.sbsj DESC
        LIMIT 1
    )
    SELECT
        TO_CHAR(mx.input_time, ''YYYY-MM-DD HH24:MI:SS'')::VARCHAR as input_time,
        TO_CHAR(mx.job_input_time, ''YYYY-MM-DD HH24:MI:SS'')::VARCHAR as job_input_time,
        mx.ipt_id::VARCHAR as ipt_id,
        mx.collect_point_text::VARCHAR as device_name' ||
        case_statements || '
    FROM acctobj_inputmx mx
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT 1
          FROM acctobj_input ai
          INNER JOIN previous_shift ps ON (
              ai.acctobj_id = ''' || p_acctobj_id || '''
              AND ai.bcdm = ps.shiftclassid
              AND ai.sbsj = ps.sbsj
              AND ai.team_id = ps.objid
          )
          WHERE ai.tmused = 1
            AND ai.id = mx.ipt_id
      )
    GROUP BY mx.input_time, mx.job_input_time, mx.ipt_id, mx.collect_point_text
    ORDER BY mx.input_time';

    -- 执行动态SQL并返回结果
    RETURN QUERY EXECUTE dynamic_sql;

END;
$$ LANGUAGE plpgsql;

-- 使用示例：
-- 由于返回类型是动态的，需要先查询有哪些采集点，然后指定返回列结构
-- 方法1：如果知道具体的采集点名称
SELECT * FROM get_previous_shift_collect_data_final('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121')
AS result(
    input_time VARCHAR,
    job_input_time VARCHAR,
    ipt_id VARCHAR,
    device_name VARCHAR,
    "采集点1" VARCHAR,
    "采集点2" VARCHAR,
    "采集点3" VARCHAR,
    "采集点4" VARCHAR,
    "采集点5" VARCHAR,
    "采集点6" VARCHAR,
    "采集点7" VARCHAR
);

-- 方法2：先查询采集点列表，然后动态构建调用
-- 查询采集点列表的辅助SQL：
/*
SELECT DISTINCT mx.collect_point
FROM acctobj_inputmx mx
WHERE mx.tmused = 1
  AND EXISTS (
      SELECT 1
      FROM acctobj_input ai
      INNER JOIN (
          SELECT s.shiftclassid, s.sbsj, s.objid
          FROM shift_data s
          INNER JOIN (
              SELECT sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
              FROM shift_data sd
              WHERE sd.orgcode = (
                  SELECT COALESCE(
                      (SELECT porgcode FROM sys_org_relation WHERE orgcode = 'ZQW9LIB5301C4LN5JG0207' AND used = 1),
                      (SELECT orgcode FROM shift_data WHERE objid = 'ZQW9LIB5301C4LN5JG0207' LIMIT 1)
                  )
              )
                AND sd.objid = 'ZQW9LIB5301C4LN5JG0207'
                AND sd.sbsj <= CURRENT_TIMESTAMP
                AND sd.xbsj > CURRENT_TIMESTAMP
              ORDER BY sd.dbrq DESC, sd.sbsj DESC
              LIMIT 1
          ) cs ON s.orgcode = cs.orgcode
          WHERE s.xbsj = cs.sbsj
          ORDER BY s.dbrq DESC, s.sbsj DESC
          LIMIT 1
      ) ps ON (
          ai.acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
          AND ai.bcdm = ps.shiftclassid
          AND ai.sbsj = ps.sbsj
          AND ai.team_id = ps.objid
      )
      WHERE ai.tmused = 1
        AND ai.id = mx.ipt_id
  )
ORDER BY mx.collect_point;
*/

-- 辅助函数：获取采集点列表
CREATE OR REPLACE FUNCTION get_collect_points_list(
    p_objid VARCHAR(200),
    p_acctobj_id VARCHAR(200)
)
RETURNS TABLE (
    collect_point VARCHAR
) AS $$
DECLARE
    parent_orgcode VARCHAR(200);
BEGIN
    -- 获取父级机构代码
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = p_objid AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = p_objid LIMIT 1)
    ) INTO parent_orgcode;

    RETURN QUERY
    SELECT DISTINCT mx.collect_point
    FROM acctobj_inputmx mx
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT 1
          FROM acctobj_input ai
          INNER JOIN (
              SELECT s.shiftclassid, s.sbsj, s.objid
              FROM shift_data s
              INNER JOIN (
                  SELECT sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
                  FROM shift_data sd
                  WHERE sd.orgcode = parent_orgcode
                    AND sd.objid = p_objid
                    AND sd.sbsj <= CURRENT_TIMESTAMP
                    AND sd.xbsj > CURRENT_TIMESTAMP
                  ORDER BY sd.dbrq DESC, sd.sbsj DESC
                  LIMIT 1
              ) cs ON s.orgcode = cs.orgcode
              WHERE s.xbsj = cs.sbsj
              ORDER BY s.dbrq DESC, s.sbsj DESC
              LIMIT 1
          ) ps ON (
              ai.acctobj_id = p_acctobj_id
              AND ai.bcdm = ps.shiftclassid
              AND ai.sbsj = ps.sbsj
              AND ai.team_id = ps.objid
          )
          WHERE ai.tmused = 1
            AND ai.id = mx.ipt_id
      )
    ORDER BY mx.collect_point;
END;
$$ LANGUAGE plpgsql;

-- 删除函数（如果需要重新创建）：
DROP FUNCTION IF EXISTS get_previous_shift_collect_data_final(VARCHAR, VARCHAR);
DROP FUNCTION IF EXISTS get_collect_points_list(VARCHAR, VARCHAR);
