-- PostgreSQL 存储过程：获取上个班次的采集点数据（最终版）
-- 根据当班查询上个班次录入的采集点明细数据
-- 参数：
-- p_objid: 当前班次的机构编码（必填）
-- p_acctobj_id: 核算对象ID（必填）

CREATE OR REPLACE FUNCTION get_previous_shift_collect_data_final(
    p_objid VARCHAR(200),
    p_acctobj_id VARCHAR(200)
)
RETURNS TABLE (
    input_time VARCHAR,
    job_input_time VARCHAR,
    ipt_id VARCHAR,
    device_name VARCHAR,
    collect_point_1 VARCHAR,
    collect_point_2 VARCHAR,
    collect_point_3 VARCHAR,
    collect_point_4 VARCHAR,
    collect_point_5 VARCHAR,
    collect_point_6 VARCHAR,
    collect_point_7 VARCHAR
) AS $$
DECLARE
    now_dt TIMESTAMP;
    parent_orgcode VARCHAR(200);
BEGIN
    -- 获取当前时间
    now_dt := CURRENT_TIMESTAMP;

    -- 获取父级机构代码
    SELECT COALESCE(
        (SELECT porgcode FROM sys_org_relation WHERE orgcode = p_objid AND used = 1),
        (SELECT orgcode FROM shift_data WHERE objid = p_objid LIMIT 1)
    ) INTO parent_orgcode;

    -- 直接通过一个查询获取上个班次的采集点数据
    RETURN QUERY
    WITH current_shift AS (
        -- 第一步：获取当前班次信息
        -- 根据当前人的机构id（objid）和当前时间查询当班信息
        SELECT
            sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj
        FROM shift_data sd
        WHERE sd.orgcode = parent_orgcode
          AND sd.objid = p_objid
          AND sd.sbsj <= now_dt  -- 上班时间 <= 当前时间
          AND sd.xbsj > now_dt   -- 下班时间 > 当前时间
        ORDER BY sd.dbrq DESC, sd.sbsj DESC
        LIMIT 1
    ),
    previous_shift AS (
        -- 第二步：根据当前班次查找上个班次
        -- 当前班次上班时间是上一班次的下班时间
        SELECT
            s.shiftclassid,
            s.sbsj,
            s.objid
        FROM shift_data s
        INNER JOIN current_shift cs ON s.orgcode = cs.orgcode
        WHERE s.xbsj = cs.sbsj  -- 上个班次的下班时间 = 当前班次的上班时间
        ORDER BY s.dbrq DESC, s.sbsj DESC
        LIMIT 1
    )
    -- 第三步：根据上个班次信息查询采集点数据
    -- AND ACCTOBJ_ID = p_acctobj_id
    -- AND bcdm = previous_shift.shiftclassid (上个班次的 shift_class_id)
    -- AND sbsj = previous_shift.sbsj
    -- AND team_id = previous_shift.objid
    SELECT
        mx.collect_point_id::VARCHAR,
        mx.collect_point::VARCHAR,
        mx.ipt_id::VARCHAR,
        mx.collect_point_text::VARCHAR,
        mx.collect_point_val::VARCHAR,
        TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS')::VARCHAR,
        TO_CHAR(mx.job_input_time, 'YYYY-MM-DD HH24:MI:SS')::VARCHAR
    FROM acctobj_inputmx mx
    WHERE mx.tmused = 1
      AND EXISTS (
          SELECT 1
          FROM acctobj_input ai
          INNER JOIN previous_shift ps ON (
              ai.acctobj_id = p_acctobj_id
              AND ai.bcdm = ps.shiftclassid
              AND ai.sbsj = ps.sbsj
              AND ai.team_id = ps.objid
          )
          WHERE ai.tmused = 1
            AND ai.id = mx.ipt_id
      )
    ORDER BY mx.collect_point_id;

END;
$$ LANGUAGE plpgsql;

-- 使用示例：
SELECT * FROM get_previous_shift_collect_data_final('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121');

-- 删除函数（如果需要重新创建）：
DROP FUNCTION IF EXISTS get_previous_shift_collect_data_final(VARCHAR, VARCHAR);
