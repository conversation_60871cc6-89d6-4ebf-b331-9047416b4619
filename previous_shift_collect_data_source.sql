-- 数据源SQL：获取上个班次采集点数据（用于前端数据源）
-- 这个SQL可以直接在数据源配置中使用
-- 返回键值对格式的数据，前端可以根据需要进行行转列处理

-- 参数说明：
-- @objid: 当前班次的机构编码（必填）
-- @acctobj_id: 核算对象ID（必填）

SELECT * FROM get_previous_shift_collect_data_final(@objid, @acctobj_id);

-- 如果需要在数据源中直接进行行转列，可以使用以下SQL：
-- 注意：这个SQL需要根据实际的采集点名称进行调整

/*
WITH raw_data AS (
    SELECT * FROM get_previous_shift_collect_data_final(@objid, @acctobj_id)
),
pivot_data AS (
    SELECT 
        input_time,
        job_input_time,
        ipt_id,
        device_name,
        -- 动态生成的采集点列，需要根据实际情况调整
        MAX(CASE WHEN collect_point_name = '采集点1' THEN collect_point_value END) as "采集点1",
        MAX(CASE WHEN collect_point_name = '采集点2' THEN collect_point_value END) as "采集点2",
        MAX(CASE WHEN collect_point_name = '采集点3' THEN collect_point_value END) as "采集点3",
        MAX(CASE WHEN collect_point_name = '采集点4' THEN collect_point_value END) as "采集点4",
        MAX(CASE WHEN collect_point_name = '采集点5' THEN collect_point_value END) as "采集点5",
        MAX(CASE WHEN collect_point_name = '采集点6' THEN collect_point_value END) as "采集点6",
        MAX(CASE WHEN collect_point_name = '采集点7' THEN collect_point_value END) as "采集点7"
    FROM raw_data
    GROUP BY input_time, job_input_time, ipt_id, device_name
    ORDER BY input_time
)
SELECT * FROM pivot_data;
*/
