server:
  port: 7900
spring:
  application:
    name: tm4-cloud-gateway
  cloud:
    gateway:
      routes:
        #奖金模块
        - id: tm4-dsz-prize
          uri: lb://TM4-DSZ-PRIZE
          predicates:
            # 公共路径：
#            - Path=/tm4main/report/**,/tm4main/system/**,/tm4main/accountForm/**,/tm4main/accountSyn/**,/tm4main/auth/**,/tm4main/auth-test/**,/tm4main/auth-systemskin/**,/tm4main/, /**,/tm4main/menu/**,/tm4main/menuLib/**,/tm4main/redis-queue/**,/tm4main/tds/**,/tm4main/tdsBtn/**,/tm4main/tdsEdit/**,/tm4main/calendar/**,/tm4main/dbtools/**,/tm4main/diyPageAnalysis/**,/tm4main/files/**,/tm4main/Path/**,/tm4main/userOnline/**,/tm4main/tool/**,/tm4main/sms/**,/tm4main/iamgeGalleryService/**
            # 奖金模块路径：
#            - Path=/tm4main/itfc/**,/tm4main/prize/**
            # 公共+奖金模块路径：
            - Path=
                /tm4main/report/**,
                /tm4main/system/**,
                /tm4main/accountForm/**,
                /tm4main/accountSyn/**,
                /tm4main/auth/**,
                /tm4main/auth-test/**,
                /tm4main/auth-systemskin/**,
                /tm4main/menu/**,
                /tm4main/menuLib/**,
                /tm4main/redis-queue/**,
                /tm4main/tds/**,
                /tm4main/tdsBtn/**,
                /tm4main/tdsEdit/**,
                /tm4main/calendar/**,
                /tm4main/dbtools/**,
                /tm4main/diyPageAnalysis/**,
                /tm4main/files/**,
                /tm4main/Path/**,
                /tm4main/userOnline/**,
                /tm4main/tool/**,
                /tm4main/sms/**,
                /tm4main/iamgeGalleryService/**,
                /tm4main/itfc/**,
                /tm4main/prize/**
          filters:
#            - Striprefix=1
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR
                methods: GET,POST

        #TLM（目标传导、日考核）
        - id: tm4-dsz-pm
          uri: lb://TM4-DSZ-PM
          predicates:
            # 目标传导模块路径：
            - Path=
                /tm4main/regime/**,
                /tm4main/mtm/**,
                /tm4main/mtmScoreRuleSet/**,
                /tm4main/gdsh/**,
                /tm4main/jobCheckPlan/**,
                /tm4main/asscenter/**,
                /tm4main/flow/**,
                /tm4main/busi/**,
                /tm4main/tmsf/**,
                /tm4main/openserv/**,
                /tm4main/singleSignOn/**,
                /tm4main/wikin-workflow/**,
                /tm4main/datav/**,
                /tm4main/datavSetting/**,
                /tm4main/datavLoginJump/**,
                /tm4main/notice/**
          filters:
#            - Striprefix=1
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR
                methods: GET,POST

        - id: tm4-paas-joblist
          uri: lb://TM4-PAAS-JOBLIST
          predicates:
            # 岗位工作清单模块路径：
            - Path=

              /tm4main/accountTools/**,
              /tm4main/bzjs/**,
              /tm4main/joblist/**,
              /tm4main/leanCosting/**,
              /tm4main/CostBgcsszb/**,
              /tm4main/costInputData/**,
              /tm4main/costItemLedger/**,
              /tm4main/costQuery/**,
              /tm4main/dayReportLr/**,
              /tm4main/installationMonthReport/**,
              /tm4main/shiftInstrumentWrite/**,
              /tm4main/stepReport/**,
              /tm4main/cost/**,
              /tm4main/mobile/**,
              /tm4main/workbench/**,
              /tm4main/aip/**,
              /tm4main/OcrErrorList/**,
              /tm4main/ocrManager/**,
              /tm4main/ocr/**,
              /tm4main/tmtask/**,
              /tm4main/task/**,
              /tm4main/assesssource/**,
              /tm4main/shift/**,
              /tm4main/exportExcel/**,
              /tm4main/accountAbnormal/**,
              /tm4main/tm4CheckModel/**,
              /tm4main/checkAnalyData/**,
              /tm4main/checkFlowSetting/**,
              /tm4main/check/**
              
          filters:
            #            - Striprefix=1
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR
                methods: GET,POST
eureka:
  client:
    service-url:
      defaultZone: http://************:30553/eureka/
logging:
  level:
    org.springframework.cloud.gateway: DEBUG