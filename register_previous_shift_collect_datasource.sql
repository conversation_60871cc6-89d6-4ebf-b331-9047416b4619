-- 注册上个班次采集点数据源
-- 执行此脚本将在系统中注册一个新的数据源

-- 1. 删除已存在的数据源（如果有）
DELETE FROM tdsout_para WHERE tdsalias = 'PreviousShiftCollectData';
DELETE FROM tdsin_para WHERE tdsalias = 'PreviousShiftCollectData';
DELETE FROM tdata_source WHERE tdsalias = 'PreviousShiftCollectData';

-- 2. 插入数据源主表记录
INSERT INTO tdata_source (
    id,
    tdsalias,
    modulecode,
    tdsname,
    tdstype,
    tdsclassname,
    memo,
    allowtosave,
    tdsquerysql,
    used,
    autoload,
    tmsort,
    issys,
    regtime,
    createtime
) VALUES (
    'PreviousShiftCollectData',
    'PreviousShiftCollectData',
    'bzjs',
    '上个班次采集点数据',
    'SYS',
    'TDSPreviousShiftCollectData',
    '获取上个班次的采集点数据，支持动态列的行转列展示',
    0,
    '',
    1,
    0,
    999,
    0,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);

-- 3. 插入输入参数
INSERT INTO tdsin_para (
    id,
    tdsalias,
    paraid,
    paraname,
    paraalias,
    paratype,
    defaultvalue,
    memo,
    used,
    tmsort
) VALUES 
(
    'PreviousShiftCollectData_objid',
    'PreviousShiftCollectData',
    1,
    '当前班次机构编码',
    'objid',
    'String',
    '',
    '当前班次的机构编码（必填）',
    1,
    1
),
(
    'PreviousShiftCollectData_acctobj_id',
    'PreviousShiftCollectData',
    2,
    '核算对象ID',
    'acctobj_id',
    'String',
    '',
    '核算对象ID（必填）',
    1,
    2
);

-- 4. 输出参数将由Java代码动态创建，因为采集点是动态的

-- 5. 验证插入结果
SELECT 
    '数据源注册完成' as message,
    tdsalias,
    tdsname,
    tdsclassname
FROM tdata_source 
WHERE tdsalias = 'PreviousShiftCollectData';

SELECT 
    '输入参数' as type,
    paraname,
    paraalias,
    memo
FROM tdsin_para 
WHERE tdsalias = 'PreviousShiftCollectData'
ORDER BY tmsort;

-- 使用说明：
-- 1. 执行此脚本后，系统中将注册一个名为 'PreviousShiftCollectData' 的数据源
-- 2. 前端可以通过数据源管理界面找到此数据源
-- 3. 调用时需要提供两个参数：
--    - objid: 当前班次的机构编码
--    - acctobj_id: 核算对象ID
-- 4. 数据源会自动根据查询结果动态生成列结构
-- 5. 返回的数据已经是行转列格式，可以直接用于表格展示

-- API调用示例：
-- GET /api/previous-shift-collect/complete-data?objid=ZQW9LIB5301C4LN5JG0207&acctObjId=ZR880Q91V07EDEWGGZ1121

-- 数据源调用示例（在前端）：
-- 数据源别名：PreviousShiftCollectData
-- 输入参数：
--   objid: ZQW9LIB5301C4LN5JG0207
--   acctobj_id: ZR880Q91V07EDEWGGZ1121
