package com.yunhesoft.accountTools.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 台账异常信息数据子表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "ACCOUNT_ABNORMAL_DATA_ITEM")
public class AccountAbnormalDataItem extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 主数据标识 */
    @Column(name="INFO_ID", length=50)
    private String infoId;
    
    /** 异常信息表单表标识ACCOUNT_ABNORMAL_FORM */
    @Column(name="AB_FORM_ID", length=100)
    private String abFormId;
    
    /** 异常信息表ID */
    @Column(name="DATA_ID", length=50)
    private String dataId;
    
    /** 异常时间 */
    @Column(name="AB_TIME")
    private Date abTime;
    
    /** 异常时间字符串 */
    @Column(name="AB_TIME_STR", length=30)
    private String abTimeStr;
    
    /** 异常值 */
    @Column(name="AB_VAL", length=100)
    private String abVal;
    
    /** 异常上限 */
    @Column(name="AB_UPLIMIT")
    private Double abUplimit;
    
    /** 异常下限 */
    @Column(name="AB_LOWLIMIT")
    private Double abLowlimit;
    
    /** 范围字符串 */
    @Column(name="AB_LIMITSTR", length=100)
    private String abLimitstr;
    
    /** 异常点 */
    @Column(name="AB_POINT", length=1000)
    private String abPoint;
    
    /** 原因分析 */
    @Column(name="AB_MARKINFO", length=2000)
    private String abMarkinfo;
    
    /** 仪表ID（采集点ID） */
    @Column(name="TAG_ID", length=50)
    private String tagId;
    
    /** 仪表名称 */
    @Column(name="TAG_NAME", length=100)
    private String tagName;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

