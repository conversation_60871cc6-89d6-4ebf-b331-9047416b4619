package com.yunhesoft.accountTools.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 台账异常表单信息表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "ACCOUNT_ABNORMAL_FORM")
public class AccountAbnormalForm extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 父标识 */
    @Column(name="FID", length=50)
    private String fid;
    
    /** 日期 */
    @Column(name="RQ", length=50)
    private String rq;
    
    /** 核算对象代码 */
    @Column(name="UNIT_CODE", length=100)
    private String unitCode;
    
    /** 核算对象名称 */
    @Column(name="UNIT_NAME", length=100)
    private String unitName;
    
    /** 班次代码 */
    @Column(name="SHIFT_CODE", length=100)
    private String shiftCode;
    
    /** 班次名称 */
    @Column(name="SHIFT_NAME", length=100)
    private String shiftName;
    
    /** 上班时间字符串 */
    @Column(name="SBSJSTR", length=30)
    private String sbsjstr;
    
    /** 下班时间字符串 */
    @Column(name="XBSJSTR", length=30)
    private String xbsjstr;
    
    /** 机构代码 */
    @Column(name="ORG_CODE", length=50)
    private String orgCode;
    
    /** 机构名称 */
    @Column(name="ORG_NAME", length=100)
    private String orgName;
    
    /** 表单模板代码 */
    @Column(name="FORM_TPL_CODE", length=100)
    private String formTplCode;
    
    /** 自定义表单代码 */
    @Column(name="CUSTOM_CODE", length=100)
    private String customCode;
    
    /** 自定义表单名称 */
    @Column(name="CUSTOM_NAME", length=100)
    private String customName;
    
    /** 显示表单名称 */
    @Column(name="FORM_NAME", length=100)
    private String formName;
    
    /** 显示表单标识 */
    @Column(name="FORM_CODE", length=50)
    private String formCode;
    
    /** 表单数据ID */
    @Column(name="FORM_DATA_ID", length=50)
    private String formDataId;
    
    /** 最后统计时间 */
    @Column(name="LAST_COUNT_TIME")
    private Date lastCountTime;
    
    /** 统计截止时间（统计上个班次时，为本班次下班时间） */
    @Column(name="END_TIME")
    private Date endTime;
    
    /** 总数（暂无用） */
    @Column(name="TOTAL_NUM")
    private Integer totalNum;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

