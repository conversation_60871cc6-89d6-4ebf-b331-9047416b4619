package com.yunhesoft.accountTools.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 数字化台账Excel区域信息
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "DIGITAL_LEDGER_EXCEL_AREA")
public class DigitalLedgerExcelArea extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 所属ID */
    @Column(name="BELONG_ID", length=50)
    private String belongId;
    
    /** 父数据ID */
    @Column(name="EXCEL_ID", length=50)
    private String excelId;
    
    /** 区域ID */
    @Column(name="AREA_ID", length=50)
    private String areaId;
    
    /** 区域名称 */
    @Column(name="AREA_NAME", length=100)
    private String areaName;
    
    /** 结构信息 */
    @Column(name="STRUCTS_INFO", length=4000)
    private String structsInfo;
    
    /** 可用标识 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    

}
