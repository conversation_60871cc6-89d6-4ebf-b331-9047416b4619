package com.yunhesoft.accountTools.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 台账自定义表单启停数据
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_ACCOUNT_FORM_ST")
public class TdsAccountFormSt extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 自定义台账ID */
    @Column(name="ACCOUNTID", length=50)
    private String accountid;
    
    /** 停止时间 */
    @Column(name="STOP_TIME")
    private Date stopTime;
    
    /** 启动时间 */
    @Column(name="START_TIME")
    private Date startTime;
    
    /** 停止时间字符串 */
    @Column(name="STOP_TIME_STR", length=50)
    private String stopTimeStr;
    
    /** 启动时间字符串 */
    @Column(name="START_TIME_STR", length=50)
    private String startTimeStr;
    
    /** 停止人员id */
    @Column(name="STOP_USER_ID", length=50)
    private String stopUserId;
    
    /** 停止人员名称 */
    @Column(name="STOP_USER_NAME", length=50)
    private String stopUserName;
    
    /** 停止操作时间 */
    @Column(name="STOP_OP_TIME")
    private Date stopOpTime;
    
    /** 启动人员id */
    @Column(name="START_USER_ID", length=50)
    private String startUserId;
    
    /** 启动人员名称 */
    @Column(name="START_USER_NAME", length=50)
    private String startUserName;
    
    /** 启动操作时间 */
    @Column(name="START_OP_TIME")
    private Date startOpTime;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

