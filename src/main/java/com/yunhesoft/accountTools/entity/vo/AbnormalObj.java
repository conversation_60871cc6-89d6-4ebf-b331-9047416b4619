package com.yunhesoft.accountTools.entity.vo;

import java.util.List;
import java.util.Map;

import com.yunhesoft.accountTools.entity.dto.AbnormalParam;
import com.yunhesoft.system.kernel.service.model.Pagination;

import lombok.Data;

@Data
public class AbnormalObj {
	
	//表头信息
	List<Map<String, String>> titleList;
	//数据信息 行<列标识, 内容对象列表>，注意第一列是核算对象key unitId, 值只有一个数组 一个元素，核算对象名等
	List<Map<String, Object>> dataList;
}
