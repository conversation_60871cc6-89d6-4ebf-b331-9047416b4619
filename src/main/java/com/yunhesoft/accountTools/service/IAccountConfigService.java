package com.yunhesoft.accountTools.service;


import com.yunhesoft.accountTools.entity.dto.AccountConfigQueryDto;
import com.yunhesoft.accountTools.entity.dto.AccountConfigSaveDto;
import com.yunhesoft.accountTools.entity.dto.CheckDeletePermissionDto;
import com.yunhesoft.accountTools.entity.dto.ExcelParam;
import com.yunhesoft.accountTools.entity.dto.RtdbDataDto;
import com.yunhesoft.accountTools.entity.po.DigitalLedger;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerExtendRow;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerModule;
import com.yunhesoft.accountTools.entity.po.DigitalLedgerTime;
import com.yunhesoft.accountTools.entity.vo.ComboVo;
import com.yunhesoft.accountTools.entity.vo.LedgerTimeDataVo;
import com.yunhesoft.accountTools.entity.vo.PotVo;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;

import java.util.List;
import java.util.Map;


/**
 *	台账设置相关服务接口
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface IAccountConfigService {

	//-------------------------------- 表单台账设置 ↓ ----------------------------------

	/**
	 * 检查删除按钮权限
	 * @param checkDeletePermissionDto
	 * @return
	 */
	public boolean checkDeletePermission(CheckDeletePermissionDto checkDeletePermissionDto);

    public Map<String, Map<String, String>> getTagValues(RtdbDataDto rtdbDataDto);

//    public JSONArray queryLedgerTimeData(TdsQueryDto param);

	List<DigitalLedgerTime> getInitTimeList(Map<String, String> stringStringMap);

	/**
	 *	存储台账时间批次初始化信息
	 * @param lists
	 * @return
	 */
	public Res<?> insertLedgerTime(List<DigitalLedgerTime> lists);

	/**
	 *	存储台账时间批次初始化信息
	 * @param ledgerId
	 * @return
	 */
	public LedgerTimeDataVo queryLedgerTime(String ledgerId);

	/**
	 *	获取表单台账设置数据
	 * @param queryDto
	 * @return
	 */
	public DigitalLedger getFormAccountConfigObj(AccountConfigQueryDto queryDto);

    public List<DigitalLedger> getDataList(AccountConfigQueryDto queryDto);

    /**
	 *	保存表单台账设置数据
	 * @param saveDto
	 * @return
	 */
	public String saveFormAccountConfigData(AccountConfigSaveDto saveDto);

	/**
	 *	获取表单台账模型下拉框数据
	 * @param queryDto
	 * @return
	 */
	public List<ComboVo> getTaizModelCombList(AccountConfigQueryDto queryDto);

	//-------------------------------- 台账模型设置 ↓ ----------------------------------

	/**
	 *	根据ID获取台账模型数据
	 * @param id
	 * @return
	 */
	public DigitalLedgerModule getDigitalLedgerModuleById(String id);

	/**
	 *	获取台账模型数据
	 * @param queryDto
	 * @return
	 */
	public List<DigitalLedgerModule> getLedgerModuleList(AccountConfigQueryDto queryDto);

	/**
	 *	保存台账模型数据
	 * @param saveDto
	 * @return
	 */
	public String saveLedgerModuleData(AccountConfigSaveDto saveDto);


	//-------------------------------- Excel解析自动生成采集点 ↓ ----------------------------------

	/**
	 *	Excel解析自动生成采集点_采集点分类树形
	 * @param param
	 * @return
	 */
	public List<PotVo> getExcelSampleClassTreeList(ExcelParam param);

	/**
	 *	Excel解析自动生成采集点_保存采集点数据
	 * @param param
	 * @return
	 */
	public ExcelParam saveExcelSampleDot(ExcelParam param);

    String generaterClass(ExcelParam param, String defBegintime, String defClassName);



	//-------------------------------- 台账扩展行初始化 ↓ ----------------------------------

	/**
	 *	查询采集点作为表头数据列表
	 * @param ledgerId
	 * @return
	 */
	public List<CostitemVo> getDotTitleList(String ledgerId);

	/**
	 *	获取数据源下拉框列表
	 * @return
	 */
	public List<TdataSource> getTdsCombList();

	/**
	 *	获取数据源输入参数列表
	 * @param  tdsAlias
	 * @return
	 */
	public List<TdsinPara> getTdsInParamList(String tdsAlias);

	/**
	 *	获取数据源输出参数列表
	 * @param  tdsAlias
	 * @return
	 */
	public List<TdsoutPara> getTdsOutParamList(String tdsAlias);

	/**
     *	获取台账扩展行初始化数据
     * @param queryDto
     * @return
     */
    public List<DigitalLedgerExtendRow> getInitExtendRowList(AccountConfigQueryDto queryDto);

    /**
     *	保存台账扩展行初始化数据
     * @param saveDto
     * @return
     */
    public String saveInitExtendRowData(AccountConfigSaveDto saveDto);

}
