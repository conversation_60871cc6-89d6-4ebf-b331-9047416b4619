package com.yunhesoft.accountTools.service;


import java.util.List;

import com.yunhesoft.accountTools.entity.dto.OrgPostUserQueryDto;
import com.yunhesoft.accountTools.entity.dto.OrgPostUserSaveDto;
import com.yunhesoft.accountTools.entity.po.TdsAccountFormSfManage;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountForm;
import com.yunhesoft.system.applyConf.entity.po.TdsAccountFormManage;
import com.yunhesoft.system.applyConf.entity.vo.TdsAccountFormManageVo;


/**
 *	机构岗位人员配置相关服务接口
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface IOrgPostUserConfService {
	
	/**
	 *	获取台账自定义表单数据
	 * @param id
	 * @return
	 */
	public TdsAccountForm getTdsAccountFormObj(String id);
	
	/**
	 *	保存台账自定义表单数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataTdsAccountForm(List<TdsAccountForm> addList, List<TdsAccountForm> updList, List<TdsAccountForm> delList);
	
	/**
	 *	获取机构岗位人员配置（单表）
	 * @param queryDto
	 * @return
	 */
	public List<TdsAccountFormManage> getTdsAccountFormManageList(OrgPostUserQueryDto queryDto);
	
	/**
	 *	获取机构岗位人员配置（附加其他属性）
	 * @param queryDto
	 * @return
	 */
	public List<TdsAccountFormManageVo> loadDataTdsAccountFormManage(OrgPostUserQueryDto queryDto);

	/**
	 *	保存机构岗位人员配置数据
	 * @param saveDto
	 * @return
	 */
	public String saveTdsAccountFormManageData(OrgPostUserSaveDto saveDto);

	/**
	 *	设置主记录显示名
	 * @param accountid
	 * @return
	 */
	public String setMainShowName(String accountid);
	
	/**
	 *	保存机构岗位人员配置数据
	 * @param modeType
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveDataTdsAccountFormManage(String modeType, List<TdsAccountFormManage> addList, List<TdsAccountFormManage> updList, List<TdsAccountFormManage> delList);
	
	
	/**
	 *	获取机构岗位人员配置（附加其他属性）, 默认台账
	 * @param queryDto
	 * @return
	 */
	public List<TdsAccountFormSfManage> loadDataTdsAccountFormManageDefault(OrgPostUserQueryDto queryDto);
	
	/**
	 *	保存机构岗位人员配置数据, 默认台账
	 * @param saveDto
	 * @return
	 */
	public String saveTdsAccountFormManageDataDefault(OrgPostUserSaveDto saveDto);
}
