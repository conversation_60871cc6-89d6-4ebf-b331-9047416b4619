package com.yunhesoft.bzjs.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.bzjs.service.PreviousShiftCollectDataService;
import com.yunhesoft.core.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 上个班次采集点数据控制器
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/api/previous-shift-collect")
public class PreviousShiftCollectDataController {

    @Autowired
    private PreviousShiftCollectDataService previousShiftCollectDataService;

    /**
     * 获取上个班次采集点数据（行转列格式）
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 行转列后的数据
     */
    @GetMapping("/data")
    public Result<JSONArray> getPreviousShiftCollectData(
            @RequestParam String objid,
            @RequestParam String acctObjId) {
        
        try {
            JSONArray data = previousShiftCollectDataService.getPreviousShiftCollectData(objid, acctObjId);
            return Result.ok(data);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取上个班次采集点数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取采集点列表
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 采集点名称列表
     */
    @GetMapping("/collect-points")
    public Result<List<String>> getCollectPointsList(
            @RequestParam String objid,
            @RequestParam String acctObjId) {
        
        try {
            List<String> collectPoints = previousShiftCollectDataService.getCollectPointsList(objid, acctObjId);
            return Result.ok(collectPoints);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取采集点列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取表格列定义（用于前端动态生成表格）
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 列定义
     */
    @GetMapping("/table-columns")
    public Result<JSONArray> getTableColumns(
            @RequestParam String objid,
            @RequestParam String acctObjId) {
        
        try {
            JSONArray columns = previousShiftCollectDataService.getTableColumns(objid, acctObjId);
            return Result.ok(columns);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取表格列定义失败：" + e.getMessage());
        }
    }

    /**
     * 一次性获取所有数据（包括数据和列定义）
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 包含数据和列定义的完整响应
     */
    @GetMapping("/complete-data")
    public Result<JSONObject> getCompleteData(
            @RequestParam String objid,
            @RequestParam String acctObjId) {
        
        try {
            JSONObject result = new JSONObject();
            
            // 获取数据
            JSONArray data = previousShiftCollectDataService.getPreviousShiftCollectData(objid, acctObjId);
            result.put("data", data);
            
            // 获取列定义
            JSONArray columns = previousShiftCollectDataService.getTableColumns(objid, acctObjId);
            result.put("columns", columns);
            
            // 获取采集点列表
            List<String> collectPoints = previousShiftCollectDataService.getCollectPointsList(objid, acctObjId);
            result.put("collectPoints", collectPoints);
            
            return Result.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error("获取完整数据失败：" + e.getMessage());
        }
    }
}
