package com.yunhesoft.bzjs.entity.po;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Data
@ApiModel("班组记事录入采集点数据表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_INPUT_COLLECT_POINT")
public class BzjsInputCollectPoint extends BaseEntity {
    @ApiModelProperty(value = "行云流表单数据ID", example = "行云流表单数据ID1")
    @Column(name = "FORM_DATA_ID", length = 50)
    private String formDataId;

    @ApiModelProperty(value = "录入主数据ID", example = "录入主数据ID1")
    @Column(name = "IPT_DATA_ID", length = 50)
    private String iptDataId;

    @ApiModelProperty(value = "录入明细数据ID", example = "录入明细数据ID1")
    @Column(name = "IPT_DATAMX_ID", length = 50)
    private String iptDatamxId;

    //@ApiModelProperty(value = "指标名称", example = "指标名称1")
    //@Column(name = "ZBMC", length = 200)
    //private String zbmc;
    //
    //@ApiModelProperty(value = "指标编码", example = "指标编码1")
    //@Column(name = "ZBBM", length = 50)
    //private String zbbm;
    //

    //
    //@ApiModelProperty(value = "计量单位")
    //@Column(name = "ITEM_UNIT", length = 50)
    //private String itemUnit;

    //-----------------------------------------
    //↓↓↓ 采集点专用

    @ApiModelProperty(value="录入时间", example="2024-01-20 07:59:36")
    @Column(name="INPUT_TIME")
    private Date inputTime;

    @ApiModelProperty(value="采集点ID", example="采集点ID1")
    @Column(name="COLLECT_POINT_ID", length=50)
    private String collectPointId;

    @ApiModelProperty(value = "采集点", example = "采集点1")
    @Column(name = "COLLECT_POINT", length = 200)
    private String collectPoint;

    @ApiModelProperty(value = "采集点录入值", example = "123")
    @Column(name = "VALUE", length = 1000)
    private String value;

    @ApiModelProperty(value = "下拉框选择值对应的显示值", example = "")
    @Column(name = "VALUE_TEXT", length = 4000)
    private String valueText;

    //@ApiModelProperty(value = "采集点录入值", example = "")
    //@Column(name = "COLLECT_POINT_VAL", length = 4000)
    //private String collectPointVal;

    //@ApiModelProperty(value = "下拉框选择值对应的显示值", example = "")
    //@Column(name = "COLLECT_POINT_TEXT", length = 4000)
    //private String collectPointText;

    @ApiModelProperty(value = "录入组件类型", example = "空=文本输入框，textfield=文本输入框，numberfield=数值输入框，datetimefield=日期时间选择框，combobox=下拉选择框，checkbox=复选框")
    @Column(name = "INPUT_COMP_TYPE", length = 50)
    private String inputCompType;

    @ApiModelProperty(value = "实时位号", example = "")
    @Column(name = "DATASOURCE", length = 100)
    private String datasource;

    @ApiModelProperty(value = "录入组件备选数据", example = "下拉选择框的备选数据，格式为JSON数组：[{value:\"\",text:\"\"}]")
    @Column(name = "INPUT_OPTIONS", length = 4000)
    private String inputOptions;

    //JSON数组对象，eval(inputOptions)
    @Transient
    private JSONArray options;

    @ApiModelProperty(value = "采集点上传图片id", example = "")
    @Column(name = "COLLECT_POINT_IMG_ID", length = 100)
    private String collectPointImgId;

    @ApiModelProperty(value = "数据是否回写到influxdb：1、是（默认）；其他不是", example = "")
    @Column(name = "IS_WRITE_BACK_INFLUXDB")
    private Integer isWriteBackInfluxdb;

    @ApiModelProperty(value = "控件类型默认值：0、无；1、当前登录人；2、当前时间；", example = "")
    @Column(name = "DEFAULT_VAL")
    private Integer defaultVal;

    @ApiModelProperty(value = "自定义默认值", example = "")
    @Column(name = "DEVICEDEFAULTVAL", length = 500)
    private String devicedefaultval;

    @ApiModelProperty(value = "采集点单位", example = "")
    @Column(name = "SDUNIT", length = 200)
    private String sdunit;

    @ApiModelProperty(value = "显示位数", example = "")
    @Column(name = "POINTCOUNTLEDGER")
    private Integer pointcountledger;

    //采集点分类相关字段
    @ApiModelProperty(value = "采集点分类ID", example = "")
    @Column(name = "COLLECT_POINT_Fl_ID", length = 50)
    private String collectPointFlId;
    @ApiModelProperty(value = "采集点分类名称", example = "")
    @Column(name = "COLLECT_POINT_Fl_NAME", length = 200)
    private String collectPointFlName;
    @ApiModelProperty(value = "采集点分类序号", example = "")
    @Column(name = "COLLECT_POINT_FL_SN")
    private Integer collectPointFlSn;

    //↑↑↑ 采集点专用
    //-----------------------------------------

    @ApiModelProperty(value = "序号", example = "")
    @Column(name = "SN")
    private Integer sn;

    @ApiModelProperty(value = "记录标识", example = "123")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value="是否允许被修改标识", example="t 允许修改；f 不允许修改，默认是f")
    @Column(name="CAN_MODIFIED", length=50)
    private String canModified;
}