package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@ApiModel("班组记事录入明细数据表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_INPUT_DATAMX")
public class BzjsInputDatamx extends BaseEntity {
    @ApiModelProperty(value = "录入主数据ID", example = "录入主数据ID1")
    @Column(name = "IPT_DATA_ID", length = 50)
    private String iptDataId;

    @ApiModelProperty(value = "行云流表单数据ID", example = "行云流表单数据ID1")
    @Column(name = "FORM_DATA_ID", length = 50)
    private String formDataId;

    @ApiModelProperty(value = "班组记事模版ID", example = "班组记事模版ID1")
    @Column(name = "TPL_ID", length = 50)
    private String tplId;

    @ApiModelProperty(value = "班组记事模版栏目ID", example = "班组记事模版栏目ID1")
    @Column(name = "TPL_DETAIL_ID", length = 50)
    private String tplDetailId;

    @ApiModelProperty(value = "栏目标题", example = "栏目标题1")
    @Column(name = "TITLE", length = 200)
    private String title;

    @ApiModelProperty(value = "数据来源", example = "数据来源1")
    @Column(name = "DATA_SOURCE", length = 100)
    private String dataSource;

    @ApiModelProperty(value = "数据类型", example = "数据类型1")
    @Column(name = "DATA_TYPE", length = 100)
    private String dataType;

    @ApiModelProperty(value = "数据范围", example = "数据范围1")
    @Column(name = "DATA_RANGE", length = 100)
    private String dataRange;

    @ApiModelProperty(value = "表格最大列数", example = "")
    @Column(name = "MAX_COL_NUM")
    private Integer maxColNum;

    @ApiModelProperty(value = "是否可编辑", example = "123")
    @Column(name = "EDITABLE")
    private Integer editable;

    @ApiModelProperty(value = "使用状态", example = "123")
    @Column(name = "ENABLED")
    private Integer enabled;

    @ApiModelProperty(value = "序号", example = "123")
    @Column(name = "SN")
    private Integer sn;

    @ApiModelProperty(value = "记录标识", example = "123")
    @Column(name = "TMUSED")
    private Integer tmused;
}
