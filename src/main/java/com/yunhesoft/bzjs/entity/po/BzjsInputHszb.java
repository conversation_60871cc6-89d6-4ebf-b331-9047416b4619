package com.yunhesoft.bzjs.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Data
@ApiModel("班组记事录入核算指标数据表")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "BZJS_INPUT_HSZB")
public class BzjsInputHszb extends BaseEntity {
    @ApiModelProperty(value = "行云流表单数据ID", example = "行云流表单数据ID1")
    @Column(name = "FORM_DATA_ID", length = 50)
    private String formDataId;

    @ApiModelProperty(value = "录入主数据ID", example = "录入主数据ID1")
    @Column(name = "IPT_DATA_ID", length = 50)
    private String iptDataId;

    @ApiModelProperty(value = "录入明细数据ID", example = "录入明细数据ID1")
    @Column(name = "IPT_DATAMX_ID", length = 50)
    private String iptDatamxId;

    @ApiModelProperty(value = "指标名称", example = "指标名称1")
    @Column(name = "ZBMC", length = 200)
    private String zbmc;

    @ApiModelProperty(value = "指标编码", example = "指标编码1")
    @Column(name = "ZBBM", length = 50)
    private String zbbm;

    @ApiModelProperty(value = "录入值", example = "123")
    @Column(name = "VALUE", length = 1000)
    private String value;

    @ApiModelProperty(value = "计量单位")
    @Column(name = "ITEM_UNIT", length = 50)
    private String itemUnit;

    @ApiModelProperty(value = "序号", example = "")
    @Column(name = "SN")
    private Integer sn;

    @ApiModelProperty(value = "记录标识", example = "123")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value="是否允许被修改标识", example="t 允许修改；f 不允许修改，默认是f")
    @Column(name="CAN_MODIFIED", length=50)
    private String canModified;
}
