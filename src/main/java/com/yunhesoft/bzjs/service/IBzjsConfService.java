package com.yunhesoft.bzjs.service;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.bzjs.entity.dto.BzjsConfDto;
import com.yunhesoft.bzjs.entity.dto.SaveDto;
import com.yunhesoft.bzjs.entity.po.BzjsPermission;
import com.yunhesoft.bzjs.entity.po.BzjsTplDetail;
import com.yunhesoft.bzjs.entity.vo.*;

import java.util.List;

public interface IBzjsConfService {
    /**
     * 通过模版ID获取模版数据
     *
     * @param param
     * @return
     */
    List<BzjsTplVo> getBzjsTplData(BzjsConfDto param);

    /**
     * 通过模版、栏目ID获取权限数据
     *
     * @param param
     * @return
     */
    List<BzjsPermissionVo> getBzjsPermissionData(BzjsConfDto param);

    /**
     * 通过模版ID获取详细设置数据
     *
     * @param param
     * @return
     */
    List<BzjsTplDetailVo> getBzjsTplDetailData(BzjsConfDto param);

    /**
     * 获取模版数据
     *
     * @param param
     * @return
     */
    List<BzjsTplVo> queryTpl(BzjsConfDto param);

    /**
     * 获取详细设置数据
     *
     * @param param
     * @return
     */
    List<BzjsTplDetailVo> queryDetail(BzjsConfDto param);

    /**
     * 保存主细数据
     *
     * @param param
     * @return
     */
    String saveData(BzjsConfDto param) throws Exception;

    /**
     * 保存权限
     * <AUTHOR>
     * @return
     * @params
     */
    Boolean saveBzjsPermissons(SaveDto saveDto);

    /**
     * 获取班组记事模版栏目树数据
     */
    List<BzjsTplTreeVo> getTeamLogColTree();
}
