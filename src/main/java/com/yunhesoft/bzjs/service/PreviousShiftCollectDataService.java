package com.yunhesoft.bzjs.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 上个班次采集点数据服务
 * 用于处理采集点数据的行转列和JSON格式转换
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class PreviousShiftCollectDataService {

    /**
     * 获取上个班次采集点数据（行转列格式）
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 行转列后的JSON数据
     */
    public JSONArray getPreviousShiftCollectData(String objid, String acctObjId) {
        if (StringUtils.isEmpty(objid) || StringUtils.isEmpty(acctObjId)) {
            return new JSONArray();
        }

        // 1. 调用存储过程获取键值对格式的原始数据
        List<Map<String, Object>> rawData = getRawCollectData(objid, acctObjId);
        
        if (rawData.isEmpty()) {
            return new JSONArray();
        }

        // 2. 进行行转列处理
        return convertToPivotFormat(rawData);
    }

    /**
     * 调用存储过程获取原始数据
     */
    private List<Map<String, Object>> getRawCollectData(String objid, String acctObjId) {
        EntityService dao = SpringUtils.getBean(EntityService.class);
        String sql = "SELECT * FROM get_previous_shift_collect_data_final(?, ?)";
        
        try {
            SqlRowSet rs = dao.rawQuery(sql, objid, acctObjId);
            List<Map<String, Object>> result = new ArrayList<>();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                row.put("input_time", rs.getString("input_time"));
                row.put("job_input_time", rs.getString("job_input_time"));
                row.put("ipt_id", rs.getString("ipt_id"));
                row.put("device_name", rs.getString("device_name"));
                row.put("collect_point_name", rs.getString("collect_point_name"));
                row.put("collect_point_value", rs.getString("collect_point_value"));
                row.put("collect_point_text", rs.getString("collect_point_text"));
                result.add(row);
            }
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 将键值对格式的数据转换为行转列格式
     */
    private JSONArray convertToPivotFormat(List<Map<String, Object>> rawData) {
        // 1. 按照 input_time + ipt_id 分组
        Map<String, Map<String, Object>> groupedData = new LinkedHashMap<>();
        Set<String> allCollectPoints = new LinkedHashSet<>();
        
        for (Map<String, Object> row : rawData) {
            String inputTime = (String) row.get("input_time");
            String iptId = (String) row.get("ipt_id");
            String collectPointName = (String) row.get("collect_point_name");
            String collectPointValue = (String) row.get("collect_point_value");
            
            // 创建分组键
            String groupKey = inputTime + "_" + iptId;
            
            // 收集所有采集点名称
            allCollectPoints.add(collectPointName);
            
            // 获取或创建分组数据
            Map<String, Object> groupRow = groupedData.computeIfAbsent(groupKey, k -> {
                Map<String, Object> newRow = new HashMap<>();
                newRow.put("input_time", inputTime);
                newRow.put("job_input_time", row.get("job_input_time"));
                newRow.put("ipt_id", iptId);
                newRow.put("device_name", row.get("device_name"));
                return newRow;
            });
            
            // 添加采集点数据
            groupRow.put(collectPointName, collectPointValue);
        }

        // 2. 转换为JSONArray格式
        JSONArray result = new JSONArray();
        for (Map<String, Object> groupRow : groupedData.values()) {
            JSONObject jsonRow = new JSONObject();
            
            // 添加基础字段
            jsonRow.put("input_time", groupRow.get("input_time"));
            jsonRow.put("job_input_time", groupRow.get("job_input_time"));
            jsonRow.put("ipt_id", groupRow.get("ipt_id"));
            jsonRow.put("device_name", groupRow.get("device_name"));
            
            // 添加所有采集点字段（没有数据的设为null）
            for (String collectPoint : allCollectPoints) {
                jsonRow.put(collectPoint, groupRow.get(collectPoint));
            }
            
            result.add(jsonRow);
        }

        return result;
    }

    /**
     * 获取采集点列表
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 采集点名称列表
     */
    public List<String> getCollectPointsList(String objid, String acctObjId) {
        if (StringUtils.isEmpty(objid) || StringUtils.isEmpty(acctObjId)) {
            return new ArrayList<>();
        }

        EntityService dao = SpringUtils.getBean(EntityService.class);
        String sql = "SELECT * FROM get_collect_points_list(?, ?)";
        
        try {
            SqlRowSet rs = dao.rawQuery(sql, objid, acctObjId);
            List<String> result = new ArrayList<>();
            
            while (rs.next()) {
                result.add(rs.getString("collect_point"));
            }
            
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取表格列定义（用于前端动态生成表格）
     * 
     * @param objid 当前班次的机构编码
     * @param acctObjId 核算对象ID
     * @return 列定义JSON数组
     */
    public JSONArray getTableColumns(String objid, String acctObjId) {
        JSONArray columns = new JSONArray();
        
        // 添加固定列
        columns.add(createColumn("input_time", "时间", 150, "left"));
        columns.add(createColumn("device_name", "设备", 100, "left"));
        
        // 添加动态采集点列
        List<String> collectPoints = getCollectPointsList(objid, acctObjId);
        for (String collectPoint : collectPoints) {
            columns.add(createColumn(collectPoint, collectPoint, 120, "center"));
        }
        
        return columns;
    }

    /**
     * 创建列定义对象
     */
    private JSONObject createColumn(String field, String title, int width, String align) {
        JSONObject column = new JSONObject();
        column.put("field", field);
        column.put("title", title);
        column.put("width", width);
        column.put("align", align);
        return column;
    }
}
