package com.yunhesoft.bzjs.tds;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.bzjs.service.PreviousShiftCollectDataService;
import com.yunhesoft.core.common.utils.SpringUtils;
import com.yunhesoft.system.tds.model.ADataSource;
import com.yunhesoft.system.tds.model.TInPara;
import com.yunhesoft.system.tds.model.TOutPara;
import com.yunhesoft.system.tds.utils.DataType;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 上个班次采集点数据源
 * 支持动态列的行转列数据展示
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public class TDSPreviousShiftCollectData extends ADataSource {

    private PreviousShiftCollectDataService collectDataService;

    public TDSPreviousShiftCollectData() {
        super();
        this.collectDataService = SpringUtils.getBean(PreviousShiftCollectDataService.class);
    }

    @Override
    public void init(Object initInfoObj) {
        super.init(initInfoObj);
        
        // 添加输入参数
        TInPara objidPara = new TInPara(this);
        objidPara.setAlias("objid");
        objidPara.setName("当前班次机构编码");
        objidPara.setDataType(DataType.tdsString);
        objidPara.setDefaultValue("");
        objidPara.setMemo("当前班次的机构编码（必填）");
        addInPara(objidPara);

        TInPara acctObjIdPara = new TInPara(this);
        acctObjIdPara.setAlias("acctobj_id");
        acctObjIdPara.setName("核算对象ID");
        acctObjIdPara.setDataType(DataType.tdsString);
        acctObjIdPara.setDefaultValue("");
        acctObjIdPara.setMemo("核算对象ID（必填）");
        addInPara(acctObjIdPara);
    }

    @Override
    public void load() {
        try {
            // 获取输入参数
            String objid = getInParaValue("objid");
            String acctObjId = getInParaValue("acctobj_id");

            if (StringUtils.isEmpty(objid) || StringUtils.isEmpty(acctObjId)) {
                setErrorInfo("参数不能为空：objid=" + objid + ", acctobj_id=" + acctObjId);
                return;
            }

            // 清除之前的数据和输出参数
            clearData();
            clearOutParas();

            // 获取采集点列表，用于动态创建输出参数
            List<String> collectPoints = collectDataService.getCollectPointsList(objid, acctObjId);
            
            // 创建固定的输出参数
            createFixedOutputParams();
            
            // 创建动态的采集点输出参数
            createDynamicOutputParams(collectPoints);

            // 获取数据
            JSONArray data = collectDataService.getPreviousShiftCollectData(objid, acctObjId);
            
            // 将数据添加到数据源
            for (int i = 0; i < data.size(); i++) {
                JSONObject row = data.getJSONObject(i);
                Map<String, Object> rowMap = row.getInnerMap();
                addData(rowMap);
            }

        } catch (Exception e) {
            setErrorInfo("加载数据失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建固定的输出参数
     */
    private void createFixedOutputParams() {
        // 时间
        TOutPara inputTimePara = new TOutPara(this);
        inputTimePara.setAlias("input_time");
        inputTimePara.setName("时间");
        inputTimePara.setDataType(DataType.tdsString);
        inputTimePara.setWidth(150);
        inputTimePara.setID(getOutParaSize());
        addOutPara(inputTimePara);

        // 作业录入时间
        TOutPara jobInputTimePara = new TOutPara(this);
        jobInputTimePara.setAlias("job_input_time");
        jobInputTimePara.setName("作业录入时间");
        jobInputTimePara.setDataType(DataType.tdsString);
        jobInputTimePara.setWidth(150);
        jobInputTimePara.setID(getOutParaSize());
        addOutPara(jobInputTimePara);

        // 录入ID
        TOutPara iptIdPara = new TOutPara(this);
        iptIdPara.setAlias("ipt_id");
        iptIdPara.setName("录入ID");
        iptIdPara.setDataType(DataType.tdsString);
        iptIdPara.setWidth(100);
        iptIdPara.setID(getOutParaSize());
        iptIdPara.setVisible(false); // 隐藏此列
        addOutPara(iptIdPara);

        // 设备名称
        TOutPara deviceNamePara = new TOutPara(this);
        deviceNamePara.setAlias("device_name");
        deviceNamePara.setName("设备");
        deviceNamePara.setDataType(DataType.tdsString);
        deviceNamePara.setWidth(100);
        deviceNamePara.setID(getOutParaSize());
        addOutPara(deviceNamePara);
    }

    /**
     * 创建动态的采集点输出参数
     */
    private void createDynamicOutputParams(List<String> collectPoints) {
        for (String collectPoint : collectPoints) {
            TOutPara collectPointPara = new TOutPara(this);
            collectPointPara.setAlias(collectPoint);
            collectPointPara.setName(collectPoint);
            collectPointPara.setDataType(DataType.tdsString);
            collectPointPara.setWidth(120);
            collectPointPara.setID(getOutParaSize());
            collectPointPara.setAlign("center");
            addOutPara(collectPointPara);
        }
    }

    @Override
    public void load(int page, int pageSize) {
        // 这个数据源不支持分页，直接调用load()
        load();
    }

    /**
     * 获取输入参数值
     */
    private String getInParaValue(String alias) {
        TInPara para = getInPara(alias);
        if (para != null && para.getValue() != null) {
            return para.getValue().toString();
        }
        return "";
    }
}
