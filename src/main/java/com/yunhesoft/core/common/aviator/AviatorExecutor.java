package com.yunhesoft.core.common.aviator;

import java.math.BigInteger;
import java.math.MathContext;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.Options;
import com.googlecode.aviator.lexer.token.OperatorType;
import com.googlecode.aviator.runtime.type.AviatorFunction;
import com.yunhesoft.core.common.aviator.function.FunAbs;
import com.yunhesoft.core.common.aviator.function.FunAcos;
import com.yunhesoft.core.common.aviator.function.FunAsin;
import com.yunhesoft.core.common.aviator.function.FunAtan;
import com.yunhesoft.core.common.aviator.function.FunCos;
import com.yunhesoft.core.common.aviator.function.FunIf;
import com.yunhesoft.core.common.aviator.function.FunInt;
import com.yunhesoft.core.common.aviator.function.FunLog;
import com.yunhesoft.core.common.aviator.function.FunLog10;
import com.yunhesoft.core.common.aviator.function.FunMod;
import com.yunhesoft.core.common.aviator.function.FunPow;
import com.yunhesoft.core.common.aviator.function.FunRound;
import com.yunhesoft.core.common.aviator.function.FunSin;
import com.yunhesoft.core.common.aviator.function.FunSqrt;
import com.yunhesoft.core.common.aviator.function.FunTan;

import lombok.extern.log4j.Log4j2;

/**
 * 脚本执行器
 * 
 * <AUTHOR>
 * @since 2022-07-17
 * 
 */
@Log4j2
public final class AviatorExecutor {

	private static JSONArray functionList = null;

	private AviatorExecutor() {

	}

	static {
		// 文档地址：https://www.yuque.com/boyan-avfmj/aviatorscript
		// 对于货币计算，或者科学数值计算等场景，需要极高的精度，这种情况下你可以通过设置下列两个选项：
		// 强制将所有浮点数（包括科学计数法）都解析为decimal 类型。
		AviatorEvaluator.setOption(Options.ALWAYS_PARSE_INTEGRAL_NUMBER_INTO_DECIMAL, true);
		// 将所有整数解析为 decimal类型。
		AviatorEvaluator.setOption(Options.ALWAYS_PARSE_FLOATING_POINT_NUMBER_INTO_DECIMAL, true);
		AviatorEvaluator.setOption(Options.MATH_CONTEXT, MathContext.DECIMAL128);
		// 设置最大循环次数上限，避免死循环出现
		AviatorEvaluator.setOption(Options.MAX_LOOP_COUNT, 100);
		// 开始引入了 LRU 缓存，可指定缓存的表达式个数，比如设置为最大 10万个缓存结果：
		AviatorEvaluator.getInstance().useLRUExpressionCache(100000);
		// 操作符别名
		AviatorEvaluator.getInstance().aliasOperator(OperatorType.AND, "and");
		AviatorEvaluator.getInstance().aliasOperator(OperatorType.OR, "or");
		// AviatorEvaluator.getInstance().aliasOperator(OperatorType.NEQ, "<>");
		// AviatorEvaluator.getInstance().aliasOperator(OperatorType.EQ, "=");
		// 初始化自定义函数=================================================================
		functionList = new JSONArray(); // 函数列表
		addFunction();// 添加自定义函数
	}

	/**
	 * 注册自定义函数
	 */
	private static void addFunction() {
		// 自定义函数
		addFunction(new FunIf(), "if(condition,true,false)", "条件判断", "if(a>b,true,false)");
		addFunction(new FunRound(), "round(x,n)", "四舍五入", "round(3.145,2)");
		addFunction(new FunAbs(), "abs(x)", "取绝对值", "abs(-1)");
		addFunction(new FunInt(), "int(x)", "取整", "int(3.14)");
		addFunction(new FunMod(), "mod(x,y)", "取余", "mod(10,3)");
		addFunction(new FunPow(), "pow(x,y)", "获取x的y次方", "pow(10,2)");
		addFunction(new FunSqrt(), "sqrt(n)", "获取n的平方根", "sqrt(4)");
		// 解析器默认支持的函数
		addFunction(null, "max(x,y..z)", "获取最大值", "max(10,12,1)");
		addFunction(null, "min(x,y..z)", "获取最小值", "min(10,12,1)");

		addFunction(new FunLog(), "log(n)", "求 n的自然对数", "log(10)");
		addFunction(new FunLog10(), "log10(n)", "求 n以 10 为底的对数", "log10(4)");
		addFunction(new FunSin(), "sin(n)", "正弦函数", "sin(30)");
		addFunction(new FunCos(), "cos(n)", "余弦函数", "cos(60)");
		addFunction(new FunTan(), "tan(n)", "正切函数", "tan(90)");
		addFunction(new FunAsin(), "asin(n)", "反正切函数", "asin(30)");
		addFunction(new FunAcos(), "acos(n)", "反余弦函数", "acos(60)");
		addFunction(new FunAtan(), "atan(n)", "反正切函数", "atan(90)");
	}

	/**
	 * 公式校验
	 * 
	 * @param expression
	 * @return
	 */
	public static String validate(String expression) {
		String result = "";
		try {
			AviatorEvaluator.validate(expression);
		} catch (Exception e) {
			result = e.getMessage();
		}
		return result;
	}

	/**
	 * 执行结果
	 * 
	 * @param expression 计算公式
	 * @param env        执行参数
	 * @return
	 * @throws Exception
	 */
	public static Object execute(String expression, Map<String, Object> env) throws Exception {
		Object result = null;
		if (expression != null && expression.length() > 0) {
			String cacheKey = getMd5(expression);
			Expression exp = AviatorEvaluator.getInstance().compile(cacheKey, expression, true);
			if (env != null) {
				result = exp.execute(env);
			} else {
				result = exp.execute();
			}
		}
		return result;
	}

	/**
	 * 执行结果
	 * 
	 * @param context 上下文对象
	 * @return
	 */
	public static Object execute(AviatorContext context) throws Exception {
		Object result = AviatorEvaluator.execute(context.getExpression(), context.getEnv(), context.isCached());
//		log.info("Aviator执行器context={},result={}", JSONObject.toJSON(context),result);
		return result;
	}

	/**
	 * 获取函数列表
	 * 
	 * @return
	 */
	public static JSONArray getFunctionList() {
		return functionList;
	}

	/**
	 * md5加密
	 * 
	 * @param str
	 * @return
	 */
	public static String getMd5(String str) {
		if (str == null) {
			return null;
		}
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(str.getBytes());
			return (new BigInteger(1, md.digest()).toString(16)).toUpperCase();
		} catch (NoSuchAlgorithmException e) {
			log.error("MD5加密错误," + str, e);
		}
		return str.toUpperCase();
	}

	/**
	 * 添加自定义函数
	 * 
	 * @param aviatorFun 自定义函数
	 * @param funName    函数名
	 * @param funMemo    函数说明
	 * @param funExample 函数示例
	 */
	private static void addFunction(AviatorFunction aviatorFun, String funName, String funMemo, String funExample) {
		JSONObject obj = new JSONObject();
		if (aviatorFun != null) {
			AviatorEvaluator.addFunction(aviatorFun);
			obj.put("name", aviatorFun.getName()); // 函数名
		} else {
			int index = funName.indexOf("(");
			if (index >= 0) {
				String name = funName.substring(0, index);
				obj.put("name", name);
			} else {
				obj.put("name", funName);
			}
		}
		obj.put("funName", funName); // 函数名
		obj.put("funMemo", funMemo); // 函数说明
		obj.put("funExample", funExample); // 函数示例
		functionList.add(obj);
	}

}
