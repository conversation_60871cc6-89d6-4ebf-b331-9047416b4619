package com.yunhesoft.core.common.aviator;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.script.ScriptEngineUtils;
import com.yunhesoft.core.utils.StringUtils;

import lombok.extern.log4j.Log4j2;

/**
 * AviatorScript脚本执行
 * 
 * <AUTHOR>
 * @since 2022-07-17
 *
 */
@Log4j2
public class AviatorUtils {

	private static final String regStr = "['\"]?[0-9]*[#a-zA-Z_-]+[0-9]*[[.]*[0-9]*[#a-zA-Z_-]*[0-9]*]*['\"]?";// 获取公式中的变量
	private static final String tdsRex = "\\$[a-zA-Z_]+[\\w]*"; // 获取tds变量
	private static final String tdsFormula = "\\$[a-zA-Z_]+[\\w]*(\\.[^\\)\\$]+\\))+;?";// 所有数据源公式内容
	private static List<String> keyWords = null; // 变量中的关键字，如if,max,min,and,or,avg等
	private static List<String> operList = null; // 变操作符

	static {
		initKeyword(); // 初始化关键字
		operList = new ArrayList<String>();
		operList.add("\\+");
		operList.add("-");
		operList.add("\\*");
		operList.add("\\/");
		operList.add(">");
		operList.add("<");
		operList.add("=");
		operList.add("!");
		operList.add("\\(");
		operList.add("\\)");
		operList.add(",");
	}

	/**
	 * 计算表达式，并返回计算结果
	 * 
	 * @param expression 计算公式
	 * @param varParams  普通变量值
	 * @param tdsParams  数据源变量值
	 * @return
	 */
	public static AviatorResult execute(String expression, Map<String, Object> varParams,
			Map<String, Object> tdsParams) {
		AviatorResult result = new AviatorResult();
		if (expression != null && expression.length() > 0) {
			try {
				String script = expression;
				// 开始变量的替换
				Map<String, Object> map = new HashMap<String, Object>();
				if (varParams != null && varParams.size() > 0) {
					map.putAll(varParams);
				}
				if (tdsParams != null && tdsParams.size() > 0) {
					map.putAll(tdsParams);
				}
				for (String key : map.keySet()) {
					Object val = map.get(key);
					script = replaceString(script, key, String.valueOf(val));
				}
				result.setExpression(script);
				// 开始公式计算
				Object value = exec(expression, varParams, tdsParams);
				result.setResult(value);
			} catch (Exception e) {
				log.error("公式计算错误", e);
				result.setResult(0);
				result.setError(e.getMessage());
			}
		}
		return result;
	}

	/**
	 * 公式计算
	 * 
	 * @param expression
	 * @return
	 * @throws Exception
	 * 
	 */
	public static Object exec(String expression) throws Exception {
		return exec(expression, null, false);
	}

	/**
	 * 公式计算
	 * 
	 * @param expression 计算公式
	 * @param params     参数
	 * @return
	 * @throws Exception
	 */
	public static Object exec(String expression, Map<String, Object> params) throws Exception {
		return exec(expression, params, true);
	}

	/**
	 * 公式计算(含数据源公式)
	 * 
	 * @param expression 计算公式
	 * @param varParams  普通变量值
	 * @param tdsParams  数据源变量值
	 * @return
	 * @throws Exception
	 */
	public static Object exec(String expression, Map<String, Object> varParams, Map<String, Object> tdsParams)
			throws Exception {
		if (expression != null && expression.length() > 0) {
			Map<String, Object> params = new HashMap<String, Object>();
			if (varParams != null && varParams.size() > 0) {// 普通变量
				params.putAll(varParams);
			}
			if (tdsParams != null && tdsParams.size() > 0) {// 数据源变量
				for (String key : tdsParams.keySet()) {
					Object value = tdsParams.get(key);
					if (value == null) {
						value = "0";
					}
					String newParam = "TDS_" + AviatorExecutor.getMd5(key);
					expression = replaceString(expression, key, newParam);// 替换数据源公式为新变量
					params.put(newParam, value);
				}
			}
			return exec(expression, params, true);
		} else {
			return 0;
		}
	}

	/**
	 * 替换变量
	 * 
	 * @param expression
	 * @param oldstr
	 * @param newstr
	 * @return
	 */
	public static String replaceString(String expression, String oldstr, String newstr) {
		String s = expression;
		if (s != null && s.length() > 0 && oldstr != null && oldstr.length() > 0 && newstr != null) {
			while (s.indexOf(oldstr) >= 0) {
				int index = s.indexOf(oldstr);
				int end = index + oldstr.length();
				String s1 = s.substring(0, index);
				String s2 = s.substring(end);
				s = s1 + newstr + s2;
			}
		}
		return s;
	}

	/**
	 * 公式校验
	 * 
	 * @param expression
	 * @return 校验结果
	 */
	public static String validate(String expression) {
		if (expression != null && expression.length() > 0) {
			expression = replaceFunction(expression);// 格式化公式
			return AviatorExecutor.validate(expression);
		} else {
			return "公式为空";
		}
	}

	/**
	 * 公式计算
	 * 
	 * @param expression 计算公式
	 * @param params     参数
	 * @param _cached    是否缓存公式
	 * @return
	 * @throws Exception
	 */
	private static Object exec(String expression, Map<String, Object> params, boolean cached) throws Exception {
		Object value = null;
		if (expression != null && expression.length() > 0) {
			value = execConst(expression);// 常量计算
			if (value == null) {
				expression = replaceFunction(expression);// 替换 iif 为if
				if (expression.toLowerCase().indexOf("if") >= 0) {
					convertParams(params);
				}
				if (cached) {// 开启缓存模式
					value = AviatorExecutor.execute(expression, params);
				} else {
					AviatorContext bean = AviatorContext.builder().expression(expression).env(params).cached(cached)
							.build();
					value = AviatorExecutor.execute(bean);
				}
			}
		}
		return value == null ? 0.0 : value;
	}

	/**
	 * 参数转换为double 解决 if 判断不正确的问题
	 * 
	 * @param params
	 */
	private static void convertParams(Map<String, Object> params) {
		if (params != null && params.size() > 0) {
			for (String key : params.keySet()) {
				Object val = params.get(key);
				boolean isConvert = false;
				if (val == null) {
					val = 0.0;
					isConvert = true;
				} else {
					if (val instanceof Integer || val instanceof Long) {
						val = Double.parseDouble(String.valueOf(val));
						isConvert = true;
					} else if (val instanceof BigDecimal) {
						val = ((BigDecimal) val).doubleValue();
						isConvert = true;
					}
				}
				if (isConvert) {
					params.put(key, val);
				}
			}
		}
	}

	/**
	 * 替换公式中的特殊函数
	 * 
	 * @param fun
	 * @return
	 */
	private static String replaceFunction(String fun) {
		if (fun == null) {
			return "";
		}
		String s = fun.replaceAll("if\\s*\\(", "iif(");// 替换if 为 iif
		s = formatScript(s);
		return s;
	}

	/**
	 * 格式化操作符（用于解析公式中的变量）
	 * 
	 * @param script
	 * @return
	 */
	public static String formatOperator(String script) {
		if (script == null) {
			return "";
		}
		for (String op : operList) {
			Pattern p = Pattern.compile(op);
			Matcher m = p.matcher(script);
			script = m.replaceAll(" " + op + " ");
		}
		return script;
	}

	/**
	 * 公式格式化
	 * 
	 * @param script
	 * @return
	 */
	private static String formatScript(String script) {
		if (script == null) {
			script = "";
		}
		Pattern p = Pattern.compile("=");
		Matcher m = p.matcher(script);
		script = m.replaceAll("==");

		p = Pattern.compile("<==");
		m = p.matcher(script);
		script = m.replaceAll("<=");

		p = Pattern.compile(">==");
		m = p.matcher(script);
		script = m.replaceAll(">=");

		p = Pattern.compile("!==");
		m = p.matcher(script);
		script = m.replaceAll("!=");

		p = Pattern.compile("====");
		m = p.matcher(script);
		script = m.replaceAll("==");

		// p = Pattern.compile("\\band\\b", Pattern.CASE_INSENSITIVE);
		// m = p.matcher(script);
		// script = m.replaceAll("&&");

		// p = Pattern.compile("\\bor\\b", Pattern.CASE_INSENSITIVE);
		// m = p.matcher(script);
		// script = m.replaceAll("||");

		p = Pattern.compile("<>");
		m = p.matcher(script);
		script = m.replaceAll("!=");

		p = Pattern.compile("\\+");
		m = p.matcher(script);
		script = m.replaceAll(" + ");

		p = Pattern.compile("--");
		m = p.matcher(script);
		script = m.replaceAll(" - - ");
		return script;
	}

	/**
	 * 常量的计算
	 * 
	 * @param s
	 * @return
	 */
	private static Object execConst(String script) {
		Object obj = null;
		try {
			if (script != null && script.trim().length() > 0) {
				script = script.trim();
				if ("0.0".equals(script)) {
					return 0.0;
				}
				if ("0".equals(script)) {
					return 0;
				}
				if (ScriptEngineUtils.isInteger(script) || ScriptEngineUtils.isDouble(script)) {// 数字
					try {
						obj = new BigDecimal(script);
						return obj;
					} catch (Exception ex) {
						return script;
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return obj;
	}

	/**
	 * 获取自定义函数列表
	 * 
	 * @return
	 */
	public static JSONArray getFunctionList() {
		return AviatorExecutor.getFunctionList();
	}

	/**
	 * 获得匹配的变量
	 * @param expression
	 * @param rex
	 * @return
	 */
	public static List<String> getParams(String expression, String rex) {
		return getParams(expression, rex, true);
	}

	/**
	 * 获得匹配的变量
	 * @param expression 计算公式
	 * @param rex 正则
	 * @param isFormat 是否格式化操作符
	 * @return
	 */
	public static List<String> getParams(String expression, String rex, boolean isFormat) {
		List<String> list = new ArrayList<String>();
		if (expression != null && expression.length() > 0) {
			if (isFormat) {
				expression = formatOperator(expression);
			}
			if (rex == null || rex.trim().length() == 0) {
				rex = regStr;
			}
			Pattern p = Pattern.compile(rex);
			Matcher m = p.matcher(expression);
			while (m.find()) {
				String s = m.group();
				if (s.substring(0, 1).equals("-") && s.length() > 1) {
					s = s.substring(1, s.length());
				}
				if (!"-".equals(s) && !list.contains(s) && !isKeyWord(s) && !isChar(s)) {// 非关键字
					list.add(s);
				}
			}
		}
		return list;
	}

	/**
	 * 获取表达式中的所有变量，包含数据源别名
	 * 
	 * @param expression
	 * @return
	 */
	public static Map<String, List<String>> getAllParams(String expression) {
		List<String> paramList = getParams(expression);
		List<String> tdsList = getTdsParams(expression);
		List<String> tdsFormulaList = getParams(expression, tdsFormula, false);
		Map<String, List<String>> map = new HashMap<String, List<String>>();
		map.put("tds", tdsList);// 数据源别名
		map.put("var", paramList);// 变量
		map.put("tdsFormula", tdsFormulaList);// 数据源表达式
		return map;
	}

	/**
	 * 获取公式中的变量
	 * 
	 * @param expression
	 * @return
	 */
	public static List<String> getParams(String expression) {
		if (expression != null) {
			expression = expression.replaceAll(tdsFormula, " 0.0 ");
		}
		return getParams(expression, null);
	}

	/**
	 * 获取公式中的数据源别名
	 * 
	 * @param expression
	 * @return
	 */
	public static List<String> getTdsParams(String expression) {
		return getParams(expression, tdsRex);
	}

	/**
	 * 初始化关键字
	 */
	private static void initKeyword() {
		keyWords = new ArrayList<String>();
		keyWords.add("if");
		keyWords.add("or");
		keyWords.add("and");
		JSONArray ary = getFunctionList();// 自定义函数
		for (int i = 0; i < ary.size(); i++) {
			JSONObject obj = ary.getJSONObject(i);
			String funname = obj.getString("name");
			keyWords.add(funname.toLowerCase());
		}
	}

	/**
	 * 判断是否为字符串
	 * 
	 * @param key
	 * @return
	 */
	private static boolean isChar(String key) {
		boolean bln = false;
		if (StringUtils.isNotEmpty(key)) {
			if (key.startsWith("'") && key.endsWith("'")) {
				return true;
			} else if (key.startsWith("\"") && key.endsWith("\"")) {
				return true;
			}
		}
		return bln;
	}

	/**
	 * 判断是否为关键字
	 * 
	 * @param key
	 * @return
	 */
	private static boolean isKeyWord(String key) {
		return keyWords.contains(key.toLowerCase());
	}

	/**
	 * @category 测试
	 * @param args
	 */
//	public static void main(String[] args) {
////		String script = "tan(30) ";
////		try {
////			Object value = AviatorUtils.exec(script);
////			System.out.println(script + ",result:" + value);
////		} catch (Exception e) {
////			e.printStackTrace();
////		}
////	}
//
//		String script = "if(a == 0,b+a,b/a)";
//		// script = "if(a == 0,10,20)" ;
//		Object value = "";
//		try {
//			Map<String, Object> params = new HashMap<String, Object>();
//			int v = 0;
//			int v2 = 3344;
//
//			// BigDecimal v = new BigDecimal(0.0);
//			// BigDecimal v2 = new BigDecimal(124.0);
//
//			params.put("a", v);
//			params.put("b", v2);
//			// params.put("c", 122222);
//			value = AviatorUtils.exec(script, params);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		System.out.println("result:" + value);
////		// 1.计算公式
////		script = "if(zzdm>'123' or zzdm='CBA' or cjdm=\"china\",1 BZGZSJ*round(wzxx.bm,2)-100/5 -GS/wb.xhl,$system.gets('数量')*$bzhs.gets('单价'))";
////		// 2.获取公式中的变量
////		Map<String, List<String>> paramMap = AviatorUtils.getAllParams(script);
////		List<String> paramList = paramMap.get("var"); // 所有变量列表（不包含数据源）
////		List<String> tdsList = paramMap.get("tds"); // 数据源列表（数据源别名，含$）
////		List<String> tdsFormulaList = paramMap.get("tdsFormula"); // 数据源公式列表
////		// 3.1变量赋值(普通变量)
////		Map<String, Object> valueMap = new HashMap<String, Object>();
////		for (String param : paramList) {
////			// 获取变量值并赋值
////			Object val = null;
////			if ("zzdm".equals(param)) {
////				val = "002";
////			} else if ("BZGZSJ".equals(param)) {
////				val = 7.5;
////			} else if ("wzxx.bm".equals(param)) {
////				val = 3.14167;
////			} else {
////				val = 1;
////			}
////			valueMap.put(param, val);
////		}
////		// 3.2变量赋值(数据源变量)
////		int i = 1;
////		Map<String, Object> tdsValueMap = new HashMap<String, Object>();
////		for (String param : tdsFormulaList) {
////			// 获取变量值并赋值(数据源解析略)
////			i++;
////			// key:$system.get(0).getf('数量')
////			// value:1
////			tdsValueMap.put(param, i);
////		}
////
////		// 4.公式计算
////		AviatorResult result = AviatorUtils.execute(script, valueMap, tdsValueMap);
////		value = result.getResult(); // 计算结果
////		String gs = result.getExpression();// 计算公式
////		System.out.println("script:" + script + ",gs:" + gs + ",result:" + value);
//	}

}
