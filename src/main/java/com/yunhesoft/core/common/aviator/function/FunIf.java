package com.yunhesoft.core.common.aviator.function;

import java.util.Map;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;

/**
 * iif 判断函数
 * 
 * iif(2>1,100,101)
 * 
 * <AUTHOR>
 *
 */
public class FunIf extends AbstractFunction {

	private static final long serialVersionUID = 1L;

	@Override
	public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2, AviatorObject arg3) {
		boolean condition = FunctionUtils.getBooleanValue(arg1, env); // 条件判断
		if (condition) {
			return arg2;
		} else {
			return arg3;
		}
	}

	@Override
	public String getName() {
		return "iif";
	}

}
