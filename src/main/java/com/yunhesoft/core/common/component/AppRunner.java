package com.yunhesoft.core.common.component;

import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;

import org.apache.catalina.util.ServerInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.yunhesoft.core.common.utils.ServerID;
import com.yunhesoft.core.utils.StringUtils;

import lombok.Data;
import lombok.extern.log4j.Log4j2;

@Data
@Component
@Log4j2
public class AppRunner implements ApplicationRunner {

	@Autowired
	private Environment env;
	private String project;
	private String application;
	private String module;
	private String ip;
	private String port;
	private String build_name;
	private String build_version;
	private Date start = new Date();
	private String contextPath;

	@Override
	public void run(ApplicationArguments args) throws Exception {
		// 初始化系统参数
		initEnvement();
		// 打印logo
		printYunheLogo();
		// 打印地址信息
		printAddress();
	}

	/**
	 * @category 初始化程序相关的配置信息
	 */
	public void initEnvement() {
		try {
			String ip = env.getProperty("server.ip");
			if (StringUtils.isEmpty(ip)) {
				ip = InetAddress.getLocalHost().getHostAddress();
			}
			String port = env.getProperty("server.port");
			if (StringUtils.isEmpty(port)) {
				port = "8080";
			}
			this.ip = ip;
			this.port = port;
			this.build_name = getUtf8Property("app.build_name");
			this.build_version = getUtf8Property("app.build_version").replaceAll("[\r\n]+", "");
			this.project = getUtf8Property("app.project");
			this.module = getUtf8Property("app.module");
			this.application = getUtf8Property("app.application");
		} catch (UnknownHostException e) {
			log.error(e.getMessage());
		}
	}

	public void printAddress() {
		String context = env.getProperty("server.servlet.context-path");
		if (StringUtils.isEmpty(context)) {
			context = "";
		} else {
			if (!context.startsWith("/")) {
				context = "/" + context;
			}
		}
		String http = "http";
		if ("true".equalsIgnoreCase(env.getProperty("server.ssl.enabled"))) {
			http = "https";
		}
		String url = http + "://" + ip + ":" + port + context;
		String doc = http + "://" + ip + ":" + port + context + "/doc.html";
		String datasource = env.getProperty("spring.datasource.url");
		String redis_url = env.getProperty("spring.redis.host");
		if (StringUtils.isNotEmpty(redis_url)) {
			redis_url += ":" + env.getProperty("spring.redis.port");
		}
		String redis_db = env.getProperty("spring.redis.database");
		System.out.println("");
		System.out.println("      [系统应用编号]：     " + getAppID());
		System.out
				.println("      [系统应用信息]：     项目: " + getProject() + " 应用:" + getApplication() + " 模块:" + getModule());
		System.out.println("      [系统发布名称]：     " + build_name);
		System.out.println("      [系统构建版本]：     " + build_version);
		System.out.println("      [Web服务器版本]：   " + ServerInfo.getServerInfo());
		System.out.println("      [服务应用编号]：     " + ServerID.getSerID());
		String appMode = env.getProperty("app.mode");
		if (StringUtils.isEmpty(appMode)) {
			appMode = "生产环境";
		}
		System.out.println("      [系统运行环境]：     " + appMode);
		if (StringUtils.isNotEmpty(datasource)) {
			System.out.println("      [关系数据库]：        " + datasource);
		}
		if (StringUtils.isNotEmpty(redis_url)) {
			System.out.println("      [内存数据库]：        redis:" + redis_url + ",dbIndex:" + redis_db);
		}

		// MongoDB
		String mongodbUri = env.getProperty("spring.data.mongodb.uri");
		String mongodb = env.getProperty("spring.data.mongodb.host");
		if (StringUtils.isNotEmpty(mongodb) || StringUtils.isNotEmpty(mongodbUri)) {
			if (StringUtils.isNotEmpty(mongodbUri)) {
				mongodb = mongodbUri;
			} else {
				mongodb += ":" + env.getProperty("spring.data.mongodb.port");
				mongodb += "/" + env.getProperty("spring.data.mongodb.database");
			}
			System.out.println("      [MongoDB]：     " + mongodb);
		}

		String druidFilters = env.getProperty("spring.datasource.druid.filters");
		if ("com.yunhesoft.system.kernel.druid.Tm4MultiTenantIdFilter".equals(druidFilters)) {
			System.out.println("      [多租户模式]：     " + druidFilters);
		}

		System.out.println("");
		System.out.println("      [系统访问地址]：     " + url);
		System.out.println("      [文档访问地址]：     " + doc);
		System.out.println("");
	}

	/**
	 * @category 得到部署的应用程序的唯一编码(MD5)编码
	 * 
	 *           <pre>
	 *           根据项目 应用程序名称 模块名称 ip:port 来共同生成一个32位md5摘要
	 *           </pre>
	 * 
	 * @return
	 */
	public String getAppID() {
		String id = project + "." + application + "." + module + "." + ip + ":" + port;
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(id.getBytes());
			return (new BigInteger(1, md.digest()).toString(16)).toUpperCase();
		} catch (NoSuchAlgorithmException e) {
			log.error(e.getMessage());
		}
		return id.toUpperCase();
	}

	private static void printYunheLogo() {
		System.out.println("                                                         ");
		System.out.println("        ooooooooooo oooo     oooo   oo   oo     oooooo   ");
		System.out.println("        88  888  88  8888o   888    88   88     88  88   ");
		System.out.println("            888      88 888o8 88    8888888     88  88   ");
		System.out.println("            888      88  888  88         88     88  88   ");
		System.out.println("           ooooo    oooo  o  oooo        oo oo  oooooo   ");
		System.out.println("                                                         ");
		System.out.println("   Copyright 运和软件 www.yunhesoft.com  All Rights Reserved  ");
		System.out.println("                                                         ");
	}

	private String iso88591ToUtf8(String src) {
		return new String(src.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
	}

	/**
	 * @category 读取utf8属性值，避免中文乱码问题
	 * @param key
	 * @return
	 */
	public String getUtf8Property(String key) {
		String value = env.getProperty(key);
		return StringUtils.isEmpty(value) ? "" : iso88591ToUtf8(value);
	}
}
