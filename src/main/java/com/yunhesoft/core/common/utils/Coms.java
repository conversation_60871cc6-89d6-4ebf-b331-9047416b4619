package com.yunhesoft.core.common.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class Coms {

	/**
	 * 每页记录数 10
	 */
	public final static int ps1 = 10;
	/**
	 * 每页记录数 20
	 */
	public final static int ps2 = 20;
	/**
	 * 每页记录数 50
	 */
	public final static int ps3 = 50;
	/**
	 * 数量 2
	 */
	public final static int i1 = 2;

	/**
	 * 数量 5
	 */
	public final static int i2 = 5;
	/**
	 * 数量 10
	 */
	public final static int i3 = 10;
	/**
	 * 数量 15
	 */
	public final static int i4 = 15;
	/**
	 * 数量 20
	 */
	public final static int i5 = 20;

	public final static String replaceCharacter = "x";

	/**
	 * 字符转换相应ascii码
	 * 
	 * @param c
	 * @return
	 */
	public static int intToChar(char c) {
		return (int) c;
	}

	/**
	 * ascii码转字符
	 * 
	 * @param i
	 * @return
	 */
	public static char charToInt(int i) {
		return (char) i;
	}

	public static String getExcelColStr(int i) {
		if (i > 25) {
			int ti = (i / 26) - 1;
			int ei = i % 26;
			return "" + charToInt(65 + ti) + charToInt(65 + ei);
		} else {
			return charToInt(65 + i) + "";
		}
	}

	@SuppressWarnings({ "finally", "rawtypes" })
	/**
	 * 通过反射获取类
	 */
	public static Class getClassLoad(String clsStr) {
		Class cls = null;
		try {
			cls = Class.forName(clsStr);
		} catch (Exception e) {
		} finally {
			return cls;
		}
	}

	/**
	 * 获得截取字符串
	 * 
	 * @param str    原字符串
	 * @param length 截取长度
	 * @return 字符串
	 */
	public static String getSubStr(String str, int length) {
		return getSubStr(str, 0, length);
	}

	/**
	 * 获得截取字符串
	 * 
	 * @param str     原字符串
	 * @param starPos 开始位置
	 * @param endPos  结束位置
	 * @return 字符串
	 */
	public static String getSubStr(String str, int starPos, int endPos) {
		return getSubStr(str, starPos, endPos, str);
	}

	/**
	 * 获得截取字符串
	 * 
	 * @param str        原字符串
	 * @param starPos    开始位置
	 * @param endPos     结束位置
	 * @param defaultVal 如果长度小于结束位置，返回的字符串
	 * @return 字符串
	 */
	public static String getSubStr(String str, int starPos, int endPos, String defaultVal) {
		if (str == null)
			return "";
		if (str.length() < endPos) {
			return defaultVal;
		} else {
			return str.substring(starPos, endPos);
		}
	}

	/**
	 * 对象和字符串比较
	 * 
	 * @param o   对象
	 * @param val 字符串
	 * @return 布尔类型数据
	 */
	public static boolean bj(Object o, String val) {
		if (o == null)
			return false;
		if (val.equals(o.toString()))
			return true;
		return false;
	}

	/**
	 * 格式化输出，页面值
	 * 
	 * @param o 输入对象
	 * @return 字符串
	 */
	public static String changeHtmlObject(Object o) {
		if (o == null) {
			return "";
		} else {
			String str = "";
			if (o.getClass().getName().equals("java.sql.Timestamp")) {
				str = DateTimeUtils.formatDate((Date) o);
			} else if (o.getClass().getName().equals("java.util.Date")) {
				str = DateTimeUtils.formatDate((Date) o);
			} else if (o.getClass().getName().equals("java.sql.Date")) {
				str = DateTimeUtils.formatDate((Date) o);
			} else {
				str = String.valueOf(o);
			}
			return str;
		}
	}

	/**
	 * 复制map对象
	 * 
	 * @param map 原map对象
	 * @return 复制出的map对象
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static Map copyMap(Map map) {
		Map new_map = new LinkedHashMap();
		if (map.isEmpty()) {
			return new_map;
		}
		for (int i = 0; i < map.size(); i++) {
			new_map.put(map.keySet().toArray()[i], map.values().toArray()[i]);
		}
		return new_map;
	}

	/**
	 * 字符串转数组List
	 * 
	 * @param s  字符串
	 * @param bz 分割标识
	 * @return list数组
	 */
	public static List<String> StrToList(String s, String bz) {
		return StrToList(s, bz, false);
	}

	/**
	 * 字符串转数组List
	 * 
	 * @param s          字符串
	 * @param bz         分割标识
	 * @param IgnoreCase 转出数组内容是否格式为小写字符
	 * @return list数组
	 */
	public static List<String> StrToList(String s, String bz, boolean IgnoreCase) {
		List<String> list = new ArrayList<String>();
		if (s == null)
			return list;
		String[] ss = s.split(bz);
		for (String str : ss) {
			if (IgnoreCase) {
				list.add(str.toLowerCase());
			} else {
				list.add(str);
			}
		}
		ss = null;
		return list;
	}

	/**
	 * <AUTHOR> 拷贝列表对象
	 * @param list 原列表对象
	 * @return 复制的列表对象
	 */
	public static <T> List<?> copyList(List<T> list) {
		List<T> new_list = new ArrayList<T>();
		if (list == null)
			return new_list;
		for (int i = 0; i < list.size(); i++) {
			new_list.add(list.get(i));
		}
		return new_list;
	}

	/**
	 * 清除字符串中的word格式标记
	 * 
	 * @param str 字符串
	 * @return 字符串
	 */
	public static String clearFormat(String str) {
		Pattern p = Pattern.compile("</?p[^>]*>|</?span[^>]*>|</?o:p[^>]*>|</?font[^>]*>");
		Matcher m = p.matcher(str);
		str = m.replaceAll("");
		return str;
	}

	/**
	 * 数据类型处理
	 ************************************************************************************************************************/

	public static boolean judgeTrimInt(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
		so = so.trim();
		if (so.length() == 0)
			return false;
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 2)
			return false;
		if (so.split("-").length > 2)
			return false;
		if (so.indexOf("-") != -1 && !so.startsWith("-"))
			flag = false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("-0123456789.".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	/**
	 * 判断是否是数据类型
	 * 
	 * @param obj 判断对象 return 布尔类型对象
	 */
	public static boolean judgeInt(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
		so = so.replaceAll(" ", "");
		if (so.length() == 0)
			return false;
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 2)
			return false;
		if (so.split("-").length > 2)
			return false;
		if (so.indexOf("-") != -1 && !so.startsWith("-"))
			flag = false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("-0123456789.".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	public static boolean judgeTrimLong(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
		so = so.trim();
		if (so.length() == 0)
			return false;
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 1)
			return false;
		if (so.split("-").length > 2)
			return false;
		so = so.split("\\.")[0];
		if (so.startsWith("-"))
			so = so.substring(1);
		if (so.length() == 0)
			return false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("0123456789".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	/**
	 * 判断是否是整型数据（正数）
	 * 
	 * @param obj 判断对象
	 * @return 布尔类型对象
	 */
	public static boolean judgeLong(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
		so = so.replaceAll(" ", "");
		if (so.length() == 0)
			return false;
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 1)
			return false;
		if (so.split("-").length > 2)
			return false;
		so = so.split("\\.")[0];
		if (so.startsWith("-"))
			so = so.substring(1);
		if (so.length() == 0)
			return false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("0123456789".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	public static boolean judgeTrimDouble(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
		so = so.trim();
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 2)
			return false;
		if (so.split("-").length > 2)
			return false;
		if (so.startsWith("-"))
			so = so.substring(1);
		if (so.length() == 0)
			return false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("0123456789.".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	/**
	 * 判断是否是双精度类型数据（正数）
	 * 
	 * @param obj 判断对象
	 * @return 布尔类型对象
	 */
	public static boolean judgeDouble(Object obj) {
		boolean flag = true;
		if (obj == null)
			return false;
		String so = String.valueOf(obj);
//		so = so.replaceAll(" ", "");
		so = so.trim();
		if (so.endsWith("-") || so.endsWith("."))
			return false;
		if (so.split("\\.").length > 2)
			return false;
		if (so.split("-").length > 2)
			return false;
		if (so.startsWith("-"))
			so = so.substring(1);
		if (so.length() == 0)
			return false;
		for (int i = 0; i < so.length(); i++) {
			String s = String.valueOf(so.charAt(i));
			if ("0123456789.".indexOf(s) == -1)
				return false;
		}

		return flag;
	}

	@SuppressWarnings("finally")
	/**
	 * 转换对象到整数（正数）
	 * 
	 * @param obj 判断对象
	 * @return 布尔类型对象
	 */
	public static int glInt(Object obj) {
		int i = 0;
		if (obj == null)
			return i;
		if (!judgeInt(obj))
			return i;
		try {
			i = Integer.parseInt(obj.toString());
		} catch (Exception e) {
		} finally {
			return i;
		}

	}

	/**
	 * 获得百分率字符串 格式：###.00%
	 * 
	 * @param a 分子
	 * @param b 分母 return 结果字符串
	 */
	public static String getPercent(int a, int b) {
		double dou = new Double(a).doubleValue();
		String result = "";
		if (b == 0)
			return result;
		DecimalFormat df = new DecimalFormat("##0.00");
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(dou * 100 / b) + "%";
		return result;
	}

	/**
	 * 获得百分率字符串 格式：###.00%
	 * 
	 * @param a    分子
	 * @param b    分母
	 * @param sort 自定义格式 null时默认###.00
	 * @param flag 返回字符串是否添加％
	 * @return 结果字符串
	 */
	public static String getPercent(int a, int b, String sort, boolean flag) {
		sort = sort == null ? "##0.00" : sort;
		double dou = new Double(a).doubleValue();
		String result = "";
		if (b == 0)
			return result;
		DecimalFormat df = new DecimalFormat(sort);
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(dou * 100 / b);
		if (flag)
			result += "%";
		return result;
	}

	/**
	 * 将对象为分子，如果对象是数据类型导出其百分率，否则，输出空字符串
	 * 
	 * @param obj 输入对象
	 * @return 字符串
	 */
	public static String getPercent(Object obj) {
		if (obj == null)
			return "";
		DecimalFormat df = new DecimalFormat("##0.00");
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		if (obj instanceof Double || obj instanceof Integer || obj instanceof Float) {
			return df.format(new Double(obj.toString()).doubleValue() * 100);
		}
		return "";

	}

	/**
	 * 格式化数字
	 * 
	 * @param obj    输入对象
	 * @param length 小数位数
	 * @return 格式化后的字符串
	 */
	public static String formatNumber(Object obj, Integer length) {
		String result = "";
		int len = length == null ? 2 : length;
		double d = 0;
		if (obj == null)
			return result;
		try {
			d = new Double(obj.toString()).doubleValue();
		} catch (Exception e) {
			return result;
		}
		String format = "##0";
		if (length > 0) {
			format += ".";
			for (int i = 0; i < len; i++) {
				format += "0";
			}
		}
		DecimalFormat df = new DecimalFormat(format);
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(d);
		int pos = result.indexOf(".");
		if (pos == -1) {
			if (length > 0) {
				result += ".";
				for (int i = 0; i < length; i++) {
					result += "0";
				}
			}
		} else {
			int extlen = result.substring(pos + 1, result.length()).length();
			for (int i = 0; i < (length - extlen); i++) {
				result += "0";
			}
		}

		return result;
	}

	/**
	 * 格式化输入内容，如果是数值，格式化，否则返回原内容
	 * 
	 * @param obj
	 * @param length
	 * @return
	 */
	public static String formatInput(Object obj, Integer length) {
		String result = "";
		int len = length == null ? 2 : length;
		double d = 0;
		if (obj == null)
			return result;
		try {
			d = new Double(obj.toString()).doubleValue();
		} catch (Exception e) {
			return obj + "";
		}
		String format = "##0";
		if (length > 0) {
			format += ".";
			for (int i = 0; i < len; i++) {
				format += "0";
			}
		}
		DecimalFormat df = new DecimalFormat(format);
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(d);
		int pos = result.indexOf(".");

		if (pos == -1) {
			if (length > 0) {
				result += ".";
				for (int i = 0; i < length; i++) {
					result += "0";
				}
			}
		} else {
			int extlen = result.substring(pos + 1, result.length()).length();
			for (int i = 0; i < (length - extlen); i++) {
				result += "0";
			}
		}

		return result;
	}

	/**
	 * 格式化数据
	 * 
	 * @param d double数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String formatNumber(double d, String s) {
		s = s == null ? "###.0" : s;
		if ("G".equals(s))
			return xlsFormatDouble(d + "");
		String result = "";
		DecimalFormat df = new DecimalFormat(s);
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(d);
		return result;
	}

	/**
	 * 格式化数据
	 * 
	 * @param f float数值
	 * @param s 格式，为空默认###.0
	 * @return 字符串
	 */
	public static String formatNumber(float f, String s) {
		s = s == null ? "###.0" : s;
		String result = "";
		DecimalFormat df = new DecimalFormat(s);
		df.setRoundingMode(java.math.RoundingMode.valueOf(4));
		result = df.format(f);
		return result;
	}

	/**
	 * <AUTHOR>
	 * @category 格式化数值类型对象
	 * @param obj
	 * @param s
	 * @return
	 */
	public static String formatAllNumber(Object obj, String s) {
		s = s == null ? "##################################.#############################" : s;
		if (obj == null)
			return "0";
		DecimalFormat df = new DecimalFormat(s);
		if (obj instanceof Double || obj instanceof Integer || obj instanceof Float) {
			return df.format(obj);
		} else if (obj instanceof String) {
			if (judgeDouble(obj)) {
				return df.format(new Double(obj.toString()));
			} else if (isFind(obj.toString(), "^-?\\d+\\.?\\d+[E][-]?\\d+$")) {
				try {
					return df.format(Double.parseDouble(obj.toString()));
				} catch (Exception e) {
					return "0";
				}
			} else {
				return "0";
			}
		} else {
			return "0";
		}
	}

	/**
	 * 数值类型在HTML中显示
	 * 
	 * @param i 数值
	 * @return 为0显示空格，否则正常显示其字符串
	 */
	public static String intHtmlshow(int i) {
		if (i == 0)
			return "&nbsp;";
		return i + "";
	}

	/**
	 * 对象转换成long数值
	 * 
	 * @param obj 对象
	 * @return long数值，为空返回0
	 */
	public static long glLong(Object obj) {
		if (obj == null)
			return 0;
		return Long.parseLong(obj.toString());
	}

	/**
	 * 对象转换成float数值
	 * 
	 * @param obj 对象
	 * @return float数值，为空返回0
	 */
	public static float glFloat(Object obj) {
		if (obj == null)
			return 0;
		return Float.parseFloat(obj.toString());
	}

	/**
	 * 对象转换成float型字符串，格式#########.0
	 * 
	 * @param obj 对象
	 * @return 字符串，为空返回空字符串
	 */
	public static String glFloatStr(Object obj) {
		if (obj == null)
			return "";
		return formatNumber(Float.parseFloat(obj.toString()), "#########.0");
	}

	/**
	 * 对象转换成double型字符串，格式#########.0
	 * 
	 * @param obj 对象
	 * @return double数值，为空返回0
	 */
	public static double glDouble(Object obj) {
		if (obj == null)
			return 0;
		return Double.parseDouble(obj.toString());
	}

	/**
	 * 对象转换成double型字符串，格式#########.0
	 * 
	 * @param obj 对象
	 * @return 字符串，为空返回空字符串
	 */
	public static String glDoubleStr(Object obj) {
		if (obj == null)
			return "";
		return formatNumber(Double.parseDouble(obj.toString()), "#########.0");
	}

	/**
	 * 建立int数组，初始化元素为0
	 * 
	 * @param l 数组长度
	 * @return int数组
	 */
	public static int[] createInts(int l) {
		int[] nint = new int[l];
		for (int i : nint) {
			nint[i] = 0;
		}
		return nint;
	}

	/**
	 * <AUTHOR> 判定两个字符串数组是否有共同内容，有返回true
	 * @param s1 数组1
	 * @param s2 数组2
	 * @return 布尔结果
	 */
	public static boolean strIsStr(String[] s1, String[] s2) {
		List<String> list = new ArrayList<String>();
		for (String s : s2) {
			list.add(s);
		}
		for (String s : s1) {
			if (!list.contains(s))
				return false;
		}
		return true;
	}

	/**
	 * <AUTHOR> 数组变成字符串，中间用,分割
	 * @param strs 字符串数组
	 * @return 字符串
	 */
	public static String strsToStr(String[] strs) {
		String str = "";
		if (strs == null) {
			return str;
		} else {
			for (String s : strs) {
				str += s + ",";
			}
		}

		return str;
	}

	/**
	 * 判断是否符合HH：mm格式数据
	 * 
	 * @param hms 字符串
	 * @return 布尔类型
	 */
	public static boolean isHM(String hms) {
		boolean flag = false;
		String rex = "(0?[0-9]|1[0-9]|2[0-3]):[0-5][0-9]";
		Pattern p = Pattern.compile(rex);
		Matcher m = p.matcher(hms);
		flag = m.find() && m.replaceAll("").length() == 0;
		return flag;
	}

	/**
	 * 获得字符串长度，中文字符算2个
	 * 
	 * @param strLen 字符串
	 * @return
	 */
	public static int getLength(String strLen) {
		char[] strL = null;

		if (strLen == null)
			return 0;
		if ("转码".length() == 2) {
			int l;
			int t;
			int c;
			l = strLen.length();
			strL = strLen.toCharArray();
			t = l;

			for (int i = 0; i < l; i++) {
				c = (int) strL[i];

				if (c < 0) {
					c = c + 65536;
				}

				if (c > 255) {
					t = t + 1;
				}
			}

			return t;
		} else {
			return 0;
			// return strL.length;
		}
	}

	/**
	 * 格式化Excel未设置数值格式的数值结果
	 * 
	 * @param dstr
	 * @return
	 */
	public static String xlsFormatDouble(String dstr) {
		String rval = "";
		if (dstr == null)
			return rval;
		if (judgeDouble(dstr)) {
			if (dstr.indexOf(".") == -1) {
				return dstr;
			} else {
				String top = dstr.substring(0, dstr.indexOf("."));
				String bot = dstr.substring(dstr.indexOf(".") + 1);
				if (String.valueOf(Long.parseLong(bot) + 1).length() > bot.length() && bot.length() > 4) {// 后面全是9
					return String.valueOf(Long.parseLong(top) + 1);
				}
				if (Double.parseDouble("0" + bot) == 0) {
					return top;
				} else {
					if (dstr.length() <= 11) {
						rval = dstr;
					} else {
						rval = top + "." + bot.substring(0, 11 - top.length());
					}
				}

				int j = 0;
				for (int i = rval.length() - 1; i > top.length() + 1; i--) {
					if ("0".equals(dstr.charAt(i) + "")) {
						j++;
					} else {
						break;
					}
				}

				if (j > 0)
					rval = rval.substring(0, rval.length() - j);
			}

		}
		return rval;
	}

	/**
	 * JAVA正则
	 ************************************************************************************/

	/**
	 * @category 是否完全匹配变量（以字母或下划线开头，内容只含有数值、字母和下划线）
	 * @param s return true符合变量 false不符合变量要求
	 */
	public static boolean matchVar(String s) {
		String rex = "^[_A-Za-z]+\\w+$";
		return match(s, rex);
	}

	/**
	 * @category 是否完全匹配变量（以字母或下划线开头，内容含有中文字符、数值、字母和下划线）
	 * @param s
	 * @return true符合变量 false不符合变量要求
	 */
	public static boolean matchVarCn(String s) {
		String rex = "^[_A-Za-z]+([^\\x00-\\xff]|[\\w])+$";
		return match(s, rex);
	}

	public static boolean match(String s, String rex) {
		rex = rex == null ? "^[_A-Za-z]+\\w+$" : rex;
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(s);
		return m.matches();
	}

	public static String replaceSpecialCharacter(String s, String rex, String repChar) {
		return s.replaceAll("[^\\w]", repChar);
	}

	/**
	 * @category 将特殊字符替换成指定字符x(除数值、字母和下划线外的字符)
	 * @param s
	 * @return
	 */
	public static String replaceSpecialCharacter(String s) {
		return replaceSpecialCharacter(s, "[^\\w]", replaceCharacter);
	}

	/**
	 * @category 将特殊字符替换成指定字符x(除中文字符、数值、字母和下划线外的字符)
	 * @param s
	 * @return
	 */
	public static String replaceSpecialCharacterCn(String s) {
		return replaceSpecialCharacter(s, "[^\\w[^\\x00-\\xff]]", replaceCharacter);
	}

	/**
	 * BSC计算使用是否找到匹配
	 * 
	 * @param str 字符串
	 * @param rex 正则 return 布尔类型
	 */
	public static boolean isFind(String str, String rex) {
		boolean flag = false;
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		if (m.find())
			flag = true;
		return flag;
	}

	/**
	 * BSC计算是否有并且头匹配
	 * 
	 * @param str 字符串
	 * @param rex 正则 return 布尔类型
	 */
	public static boolean isFirst(String str, String rex) {
		boolean flag = false;
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		if (m.find()) {
			if (m.start() == 0)
				flag = true;
		}
		return flag;
	}

	/**
	 * 返回第一个匹配的位置，如果全字符匹配，返回 -1
	 * 
	 * @param str 全字符
	 * @param rex 匹配正则
	 * @return
	 */
	public static int getPos(String str, String rex) {
		int pos = 1;
		int len = str.length();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		if (m.find()) {
			if (m.end() == len && m.start() == 0) {
				pos = -1;
			} else {
				pos = m.start();
			}
		}
		return pos;
	}

	/**
	 * 获得匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @return
	 */
	public static List<String> getBl(String str, String rex) {
		List<String> list = new ArrayList<String>();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		while (m.find()) {
			list.add(m.group());
		}
		return list;
	}

	/**
	 * 获得替换匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @return
	 */
	public static List<String> getBl(String str, String rex, Map<String, String> map) {
		List<String> list = new ArrayList<String>();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		while (m.find()) {
			String otherVar = map.get(m.group());
			otherVar = otherVar == null ? "" : otherVar;
			list.add(otherVar);
		}
		return list;
	}

	/**
	 * 替换匹配的变量
	 * 
	 * @param str
	 * @param rex
	 * @param list 变量的替换值列表
	 * @return
	 */
	public static String replaceBl(String str, String rex, List<String> list) {
		String s = "";
		int i = 0;
		int size = list.size();
		if (rex == null ? true : rex.trim().length() == 0)
			rex = "([^\\/^\\+^\\-^\\*^ ^\\(^\\)^\\,^\\>,^\\=,^\\<]+\\.)+([A-Za-z]+)";
		Pattern pattern = Pattern.compile(rex);
		String[] r = pattern.split(str);
		int pos = getPos(str, rex);
		if (pos == -1) {
			s = list.get(0);
		} else {
			for (i = 0; i < r.length; i++) {
				if (size > i) {
					s += r[i] + list.get(i);
				} else {
					s += r[i];
				}
			}
			if (size > i)
				s += list.get(i);
		}

		return s;
	}

	public static String replaceBl(String str, String rex, String repStr) {
//		String s = "";
//		if(rex == null || rex.length() == 0)
//			return str;
//		
//		str+="#%@";
//		Pattern pattern = Pattern.compile(rex);
//		String[] r=pattern.split(str);
//		int pos = getPos(str, rex);
//		if(pos==-1) {
//			s = repStr;
//		}else{
//			for (int i = 0,len = r.length-1; i < len; i++) {
//				s+=r[i]+repStr;
//			}
//
//		}
//		
//		return s;
		return str.replaceAll(rex, repStr);
	}

	/**
	 * 转码iso-8859-1-->GBK
	 * 
	 * @param str
	 * @return
	 */
	public static String changeEncode(String str) {
		return changeEncode(str, "iso-8859-1", "GBK");
	}

	/**
	 * 转码处理
	 * 
	 * @param str  字符串
	 * @param enc1 编码1
	 * @param enc2 编码2
	 * @return
	 */
	public static String changeEncode(String str, String enc1, String enc2) {
		String s = "";
		try {
			s = new String(str.getBytes(enc1), enc2);
		} catch (UnsupportedEncodingException e) {
		}
		return s;
	}

	/**
	 * 格式化变量为Java标准命名格式
	 * 
	 * @param str str变量支持 a.b.c
	 * @return java命名格式
	 */

	public static String formatVar(String str) {
		if (str == null) {
			return "";
		}

		str = str.trim();
		String rtn = "";
		String rex = "^[a-zA-Z_]+[a-zA-Z0-9_]*$"; // 以字母或者下划线开头
		str = str.replaceAll("#", "SIGN1"); // 替换＃

		String[] strNames = str.split("\\.");
		for (int i = 0; i < strNames.length; i++) {
			int pos = getPos(strNames[i], rex);
			if (pos != -1) {
				strNames[i] = "_" + strNames[i];
			}
			if (i > 0) {
				rtn += ".";
			}
			;
			rtn += strNames[i];
		}
		return rtn;
	}

	/**
	 * URL参数传递加密
	 * 
	 * @param para
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String ec(String para) throws UnsupportedEncodingException {
		return URLEncoder.encode(para, "UTF-8");
	}

	/**
	 * URL参数传递解密
	 * 
	 * @param para
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public static String dc(String para) throws UnsupportedEncodingException {
		return URLDecoder.decode(para, "UTF-8");
	}

	/**
	 * @category 根据绝对位置数返回Excel定位位置 1_1 --> B2
	 * @param apos
	 * @return
	 */
	public static String getEp(String apos) {
		String str = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		String lcstr[] = apos.split("_");
		int tops = Integer.parseInt(lcstr[1]) / 26;
		int bottoms = Integer.parseInt(lcstr[1]) % 26;
		if (tops == 0) {
			return str.charAt(bottoms) + lcstr[0];
		} else {
			return str.charAt(tops - 1) + "" + str.charAt(bottoms) + lcstr[0];
		}
	}

	/**
	 * @category 根据Excel定位位置数返回绝对位置 B2 --> 1_1
	 * @param apos
	 * @return
	 */
	public static String getRep(String apos) {
//		String str="ABCDEFGHIJKLMNOPQRSTUVWXYZ";
		String l = "";
		List<Integer> clist = new ArrayList<Integer>();
		for (int i = 0; i < apos.length(); i++) {
			int x = apos.charAt(i);
			if (x > 57) { // 字母
				clist.add(x - 64);
			} else { // 数字
				l += (x - 48) + "";
			}
		}
		String rval = l + "_";
		if (clist.size() > 1) {
			rval += clist.get(0) * 26 + clist.get(1) - 1;
		} else {
			rval += clist.get(0) - 1;
		}

		return rval;
	}

	public static String formatEgsToTzgs(String egs) {
		String gs = "";

		return gs;
	}

	/**
	 * 解析公式中的全部变量
	 * 
	 * @category 解析公式中的全部变量
	 * @param formula (String) 公式
	 * @return List<String> 公式中的变量列表(按出现顺序)
	 */
	public static List<String> getVar(String formula) {

		List<String> result = new ArrayList<String>();

		if (formula != null && formula.length() != 0) {

			// String[] czf = { "+", "-", "*", "/", ">", "<", "(", ")", ",", "^", "="};//操作符

			formula = formula.replaceAll("[\\r\\n]+", "");// 去掉\r\n
			formula = formula.replaceAll("(\\'[^\\']*[\\'])|(\\\"[^\\\"]*[\\\"])", "''");// 去掉引号中的内容

			String regexBegin = "(^|(?<=[\\+\\-\\*\\/\\>\\<\\(\\,\\^\\=]))";// 零宽度正回顾后发断言 用于确定要替换的字符的前面为
																			// 操作符{不算")",字符前出现则不符合运算规则。不视为变量}
																			// 或者^(字符串起始标志)
			String regexEnd = "((?=[\\+\\-\\*\\/\\>\\<\\)\\,\\^\\=])|$)";// 零宽度正预测先行断言 用于确定要替换的字符的后面为 操作符
																			// {不算"(",字符后出现则不符合运算规则。不视为变量} 或者$(字符串结束标志)

			// String regex = regexBegin+ "\\s*([\\w\\.]+)\\s*" + regexEnd;
			String regex = regexBegin + "\\s*([^\\+\\-\\*\\/\\>\\<\\(\\)\\,\\^\\=\\\"\\'\\s]+)\\s*" + regexEnd;// 匹配任意操作数但不匹配字符常量

			Pattern pattern = Pattern.compile(regex);
			Matcher m = pattern.matcher(formula);

			Pattern patternNum = Pattern.compile("^((\\d*(\\.\\d+)?)|(\\d+(\\.\\d*)?))$");// 是否是数字

			HashSet<String> hs = new HashSet<String>(); // 用于去掉重复变量

			while (m.find()) {

				String strTemp = m.group().replaceAll("\\s", "");// 去掉全部的空字符

				Matcher mTemp = patternNum.matcher(strTemp);

				if (mTemp.find()) {// 校验是不是数字
					// 数字不处理
				} else {
					if (strTemp != null && strTemp.length() != 0) {// 找到的值不是空

						int oldSize = hs.size();// 记录未添加前HashSet的大小

						hs.add(strTemp);// 非数字则为变量,存储到HashSet中去掉重复

						if (hs.size() > oldSize) { // 添加进了HashSet，代表无重复

							result.add(strTemp);// 为了保持变量的读取顺序。HashSet中顺序会被打乱

						}
					}
				}

			}

			// result.addAll(hs);//将HashSet中的内容添加到list

		}
		// log.info(result);
		return result;
	}

	/**
	 * 将公式中名称为key的变量替换成value
	 * 
	 * @category 替换公式中的变量
	 * @param formula (String) 要替换的公式
	 * @param key     (String) 变量名称
	 * @param value   (String) 变量值
	 * @return String 替换后的公式
	 */
	public static String replaceVar(String formula, String key, String value) {
		String result = formula;
		if (formula != null && key != null && value != null) {// 参数都有内容

			// String[] czf = { "+", "-", "*", "/", ">", "<", "(", ")", ",", "^", "="};//操作符

			String[] conversionStr = { ".", "?", "$", "|", "+", "-", "*", "^", "[", "]", "(", ")" };// 需要转义的字符数组
			String replaceKey = key;
			for (int i = 0; i < conversionStr.length; i++) {

				replaceKey = replaceKey.replace(conversionStr[i], "\\" + conversionStr[i]);// 转义小数点

			}

			String regexBegin = "(^|(?<=[\\+\\-\\*\\/\\>\\<\\(\\,\\^\\=]))";// 零宽度正回顾后发断言 用于确定要替换的字符的前面为
																			// 操作符{不算")",字符前出现则不符合运算规则。不视为变量}
																			// 或者^(字符串起始标志)
			String regexEnd = "((?=[\\+\\-\\*\\/\\>\\<\\)\\,\\^\\=])|$)";// 零宽度正预测先行断言 用于确定要替换的字符的后面为 操作符
																			// {不算"(",字符后出现则不符合运算规则。不视为变量} 或者$(字符串结束标志)

			String regex = regexBegin + "\\s*(" + replaceKey + ")\\s*" + regexEnd;

			Pattern pattern = Pattern.compile(regex);
			Matcher m = pattern.matcher(formula);

			StringBuffer sb = new StringBuffer();

			while (m.find()) {

				String replacement = m.group().replace(key, value);
				m.appendReplacement(sb, replacement);// 追加到 StringBuffer
			}
			m.appendTail(sb);// 将剩下未匹配的尾部字符添加进StringBuffer
			result = sb.toString();
		}
		return result;
	}

	/**
	 * 判断字符是否是操作符
	 * 
	 * @param c
	 * @return
	 */
	public static boolean isCzf(String c) {
		// 判断字符是否是操作符
		c = c.trim();
		String[] czf = { "+", "-", "*", "/", ">", "<", "(", ")", ",", "^", "=", "@" };
		for (int i = 0; i < czf.length; i++) {
			if (c.equals(czf[i])) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 转码成 UTF-8 判断是否是关键字
	 * 
	 * @param c
	 * @return boolean
	 */
	public static boolean iskeyword(String c) {
		// 判断是否是关键字
		String[] keyword = { "not", "and", "or", "find", "equal", "abs", "cos", "if", "case", "when", "then", "day",
				"daysafter", "int", "isnull", "isnumber", "left", "len", "long", "mid", "month", "round", "second",
				"sin", "year", "trim", "tan", "else", "today", "avg", "sqrt", "mod", "right" };
		for (int i = 0; i < keyword.length; i++) {
			if (c.equals(keyword[i])) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 转码成 UTF-8 判断是否是值限细分的参数
	 * 
	 * @param c
	 * @return boolean
	 */
	public static boolean isZxxf(String c) {
		// 判断是否是关键字
		String[] keyword = { "getzxsx", "getzxxx", "getbxsx", "getbxxx" };
		for (int i = 0; i < keyword.length; i++) {
			if (c.equals(keyword[i])) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 方法说明：利用递归返回gs中‘)’的结束位置,需要判断‘(’与‘)’的数量是否一致
	 * 
	 * @param gs 包含‘(’和‘)’的字符串
	 * @param ks 判断开始位置
	 * @return
	 */
	public static int getZfjs(String gs, int ks) {
		int rtn = 0;
		int jsd;
		int qkhsl;
		int hkhsl;
		String cs;
		jsd = gs.indexOf(")", ks) + 1;
		cs = gs.substring(0, jsd);
		qkhsl = getZfsl(cs, "(");
		hkhsl = getZfsl(cs, ")");
		if (qkhsl == hkhsl) {
			rtn = jsd;
		} else {
			rtn = getZfjs(gs, jsd);
		}
		return rtn;
	}

	/**
	 * 方法说明返回var中包含zf的数量 创建人：文玉林 创建日期：2009-10-13
	 * 
	 * @param var 字符串
	 * @param zf  要判断的字符
	 * @return int
	 */
	public static int getZfsl(String var, String zf) {
		int sl = 0;
		int i;
		for (i = 0; i < var.length(); i++) {
			if (var.substring(i, i + 1).equals(zf)) {
				sl++;
			}
		}
		return sl;
	}

	/**
	 * 方法说明：对calcReturn做四舍五入取小数位 创建人：文玉林 创建日期：2009-10-13
	 * 
	 * @param calcReturn 数值
	 * @param numberFor  小数位数
	 * @return
	 */

	public static String numFormat(String calcReturn, int numberFor) {
		String returnStr = "";
		double eachValue = 0;
		double addValue = 0;// 要加上的小数
		String[] cs;

		try {
			cs = calcReturn.split("\\.");
			if (cs.length > 1) {
				if (numberFor >= cs[1].length()) {
					// 要保留的小数位数大于现有的小数位数，此时不用转换
					returnStr = calcReturn;
				} else {
					// 需要转换
					addValue = 1 / Math.pow(10, cs[1].length() + 1);// 额外要加上的小数
					eachValue = Double.parseDouble(calcReturn);
					eachValue = eachValue + addValue;
					NumberFormat nf = NumberFormat.getNumberInstance();
					nf.setMaximumFractionDigits(numberFor);
					nf.setMinimumFractionDigits(numberFor);
					returnStr = nf.format(eachValue);
				}
			} else {
				// 没有小数不用转换
				returnStr = calcReturn;
			}

		} catch (NumberFormatException nb) {
			log.info("转换出错");
		}
		return returnStr;
	}

	/**
	 * 判断字符串是否是数字
	 * 
	 * @param str
	 * @return
	 */
	public static boolean isNum(String str) {
		if (str == null || "".equals(str)) {
			return false;
		} else {
			Pattern pattern = Pattern.compile("[0-9]*");
			return pattern.matcher(str).matches();
		}
	}

	/**
	 * <AUTHOR>
	 * @category 列出BSC所有可在数据源公式中设置的变量
	 * @return map对象
	 */
	public static Map<String, String> getBscParaMap() {
		Map<String, String> map = new LinkedHashMap<String, String>();
		map.put("@zyid", "个人ID标识");
		map.put("@id", "身份证号");
		map.put("@bzdm", "班组代码");
		map.put("@zzdm", "装置代码");
		map.put("@workid", "工号");
		map.put("@bzmc", "班组名称");
		map.put("@zbbm", "指标别名");
		return map;
	}

	/**
	 * <AUTHOR>
	 * @category list转成用逗号分割的字符串
	 * @param list
	 * @return
	 */
	public static String listToString(List<String> list) {
		return listToString(list, ",");
	}

	public static String listObjToString(List<Object> list) {
		return listObjToString(list, ",");
	}

	/**
	 * <AUTHOR>
	 * @category list转成用标识分割的字符串
	 * @param list
	 * @param bs
	 * @return
	 */
	public static String listToString(List<String> list, String bs) {
		StringBuilder sb = new StringBuilder();
		for (String s : list) {
			if (sb.length() > 0) {
				sb.append(bs);
			}
			sb.append(s);
		}

		return sb.toString();
	}

	public static String listToString2(List<String> list, String bs) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0, l = list.size(); i < l; i++) {
			String s = String.valueOf(list.get(i));
			if (sb.length() > 0) {
				sb.append(bs);
			}
			sb.append(s);
		}

		return sb.toString();
	}

	public static String listObjToString(List<Object> list, String bs) {
		StringBuilder sb = new StringBuilder();
		for (Object o : list) {
			if (sb.length() > 0) {
				sb.append(bs);
			}
			sb.append(String.valueOf(o));
		}

		return sb.toString();
	}

	public static int strlength(String value) {
		int valueLength = 0;
		String chinese = "[\u0391-\uFFE5]";
		/* 获取字段值的长度，如果含中文字符，则每个中文字符长度为2，否则为1 */
		for (int i = 0; i < value.length(); i++) {
			/* 获取一个字符 */
			String temp = value.substring(i, i + 1);
			/* 判断是否为中文字符 */
			if (temp.matches(chinese)) {
				/* 中文字符长度为2 */
				valueLength += 2;
			} else {
				/* 其他字符长度为1 */
				valueLength += 1;
			}
		}
		return valueLength;
	}
}
