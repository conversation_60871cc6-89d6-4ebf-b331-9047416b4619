package com.yunhesoft.core.common.utils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.time.DateFormatUtils;
import lombok.extern.log4j.Log4j2;

/**
 * @category java8的时间工具类
 * <AUTHOR>
 * @date 2020-8-28
 */
@Log4j2
public class DateTimeUtils extends org.apache.commons.lang3.time.DateUtils {

	private static final DateTimeFormatter DATETIMEFORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private static final DateTimeFormatter DATETIMEFORMATTER1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

	public static String YYYY = "yyyy";

	public static String YYYY_MM = "yyyy-MM";

	public static String YYYY_MM_DD = "yyyy-MM-dd";

	public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

	private static String[] parsePatterns = { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
			"yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM", "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss",
			"yyyy.MM.dd HH:mm", "yyyy.MM" };

	public static final int Date_year = 1;
	public static final int Date_month = 2;
	public static final int Date_week = 3;
	public static final int Date_day = 4;
	public static final int Date_hour = 5;
	public static final int Date_minute = 6;
	public static final int Date_second = 7;

	public static final long secOfHour = 3600000;
	public static final long secOfDay = 3600000 * 24;

	public static final String DateFormat_YMD = "yyyy-MM-dd";
	public static final String DateFormat_ymd = "yyyyMMdd";
	public static final String DateFormat_YM = "yyyy-MM";
	public static final String DateFormat_ym = "yyyyMM";
	public static final String DateFormat_YMDHMS = "yyyy-MM-dd HH:mm:ss";
	public static final String DateFormat_HMS = "HH:mm:ss";
	public static final String DateFormat_YMDCN = "yyyy年MM月dd日";
	public static final String DateFormat_HMSCN = "HH时mm分ss秒";

	public static final String DAY = "day";
	public static final String HOUR = "hour";
	public static final String MINITE = "minute";
	public static final String SECOND = "second";

	static {
		TimeZone tz = TimeZone.getTimeZone("GMT+08:00");// 获取中国北京时区
		TimeZone.setDefault(tz);// 设置中国北京时区为默认时区
	}

	/**
	 * @category 格式化【1971-09-20T13:00:00.000Z】格式
	 * @param ldt
	 * @return
	 */
	public static String formatLocalDateTimeUTC(LocalDateTime ldt) {
		return ldt.format(DATETIMEFORMATTER1).replace(" ", "T") + "Z";
	}

	public static String formatLocalDateTime(LocalDateTime ldt, String formater) {
		return ldt.format(DateTimeFormatter.ofPattern(formater));
	}

	// Obtains an instance of Date from an Instant object.
	public static Date from(Instant instant) {
		try {
			return new Date(instant.toEpochMilli());
		} catch (ArithmeticException ex) {
			throw new IllegalArgumentException(ex);
		}
	}

	// 01. java.util.Date --> java.time.LocalDateTime
	public static LocalDateTime date2LocalDateTime(Date date) {
		Instant instant = date.toInstant();
		ZoneId zone = ZoneId.systemDefault();
		return LocalDateTime.ofInstant(instant, zone);
	}

	// 02. java.util.Date --> java.time.LocalDate
	public static LocalDate date2LocalDate(Date date) {
		Instant instant = date.toInstant();
		ZoneId zone = ZoneId.systemDefault();
		LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
		return localDateTime.toLocalDate();
	}

	// 03. java.util.Date --> java.time.LocalTime
	public static LocalTime date2LocalTime(Date date) {
		Instant instant = date.toInstant();
		ZoneId zone = ZoneId.systemDefault();
		LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
		return localDateTime.toLocalTime();
	}

	// 04. java.time.LocalDateTime --> java.util.Date
	public static Date localDateTime2Date(LocalDateTime localDateTime) {
		ZoneId zone = ZoneId.systemDefault();
		Instant instant = localDateTime.atZone(zone).toInstant();
		return Date.from(instant);
	}

	// 05. java.time.LocalDate --> java.util.Date
	public static Date localDate2Ddate(LocalDate localDate) {
		ZoneId zone = ZoneId.systemDefault();
		Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
		return Date.from(instant);
	}

	/**
	 * @category 格式化【1971-09-20 13:00:00.000】格式
	 * @param date
	 * @return
	 */
	public static String formatDate(Date date) {
		if (date == null) {
			return null;
		}
		SimpleDateFormat SIMPLEDATEFORMATER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String res = SIMPLEDATEFORMATER.format(date);
		SIMPLEDATEFORMATER = null;
		return res;
	}

	/**
	 * @category 格式化
	 * @param date
	 * @param formater 格式化模板
	 * @return
	 */
	public static String formatDate(Date date, String formater) {
		SimpleDateFormat sformater = new SimpleDateFormat(formater);
		String res = sformater.format(date);
		return res;
	}

	/**
	 * @category 格式化成标准的【1971-09-20 13:00:00】格式
	 * @param ldt
	 * @return
	 */
	public static String formatLocalDateTime(LocalDateTime ldt) {
		return ldt.format(DATETIMEFORMATTER);
	}

	public static LocalDateTime parseLocalDateTime(String datetime) {
		MyDateTimeStruct dts = parse_datetime(datetime);
		if (!dts.notNull) {
			return null;
		}
		return LocalDateTime.of(dts.y, dts.m, dts.d, dts.h, dts.mi, dts.s, 1000000 * dts.ms);
	}

	private static MyDateTimeStruct parse_datetime(String datetime) {
		if (datetime == null || datetime.trim() == "") {
			return new DateTimeUtils().new MyDateTimeStruct();
		}
		List<String> list = new ArrayList<String>();
		StringBuffer sb = new StringBuffer();
		byte typ = '0';
		for (int i = 0; i < datetime.length(); i++) {
			char ch = datetime.charAt(i);
			if (ch == '-' || ch == '/' || ch == ' ' || ch == '.' || ch == ':' || ch == '年' || ch == '月' || ch == '日'
					|| ch == '时' || ch == '分' || ch == '秒' || ch == 'T' || ch == 't' || ch == 'Z' || ch == 'z') {
				if (sb.length() > 0) {
					list.add(sb.toString());
					sb.setLength(0);
				}
				if (typ == '0' && ch == '/') {
					typ = '/';
				} else if (typ == '0' && ch == '-') {
					typ = '-';
				}
			} else {
				if (ch > 47 && ch < 58) {
					sb.append(ch);
				} else {
					log.warn("解析日期时间字符串时警告:[" + datetime + "] 忽略无效的字符:" + "  " + ch);
				}
			}
		}
		if (sb.length() > 0) {
			list.add(sb.toString());
		}
		// en-us : 09/20/1971 13:30:59.888
		// zh-cn : 1971-09-20 13:30:59.888
		if (typ == '/' || Integer.parseInt(list.get(2)) > 31) {// en-us交换成zh-cn
			int t = Integer.parseInt(list.get(2));
			list.set(2, list.get(1));
			list.set(1, list.get(0));
			list.set(0, String.valueOf(t));
		}
		MyDateTimeStruct dts = new DateTimeUtils().new MyDateTimeStruct();
		int size = list.size();
		if (size >= 3) {
			dts.y = Integer.parseInt(list.get(0));
			dts.m = Integer.parseInt(list.get(1));
			dts.d = Integer.parseInt(list.get(2));
		} else {
			log.error("解析日期格式不正确:" + datetime);
		}
		if (size >= 4) {
			dts.h = Integer.parseInt(list.get(3));
		}
		if (size >= 5) {
			dts.mi = Integer.parseInt(list.get(4));
		}
		if (size >= 6) {
			dts.s = Integer.parseInt(list.get(5));
		}
		if (size >= 7) {
			if (list.get(6).length() == 1) {
				list.set(6, list.get(6) + "00");
			} else if (list.get(6).length() == 2) {
				list.set(6, list.get(6) + "0");
			}
			if (Integer.parseInt(list.get(6)) >= 1000) {
				log.warn("解析日期时间字符串格式错误：毫秒数不能>=1000  {} 忽略 {} 数据", datetime, dts.ms);
			} else {
				dts.ms = Integer.parseInt(list.get(6)); // * 1000000;
			}
		}
		dts.notNull = true;
		return dts;
	}

	public static Date parseDate(String datetime) {
		MyDateTimeStruct dts = parse_datetime(datetime);
		if (!dts.notNull) {
			return null;
		}
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, dts.y);
		cal.set(Calendar.MONTH, dts.m - 1);
		cal.set(Calendar.DATE, dts.d);
		cal.set(Calendar.HOUR_OF_DAY, dts.h);// cal.set(Calendar.HOUR, dts.h);
		cal.set(Calendar.MINUTE, dts.mi);
		cal.set(Calendar.SECOND, dts.s);
		cal.set(Calendar.MILLISECOND, dts.ms);
		return cal.getTime();
	}

	/**
	 * @category 从java.util.Date转java.sql.Date
	 * @param date
	 * @return
	 */
	public static java.sql.Date date2SqlDate(Date date) {
		return new java.sql.Date(date.getTime());
	}

	public static boolean isDate(String string) {
		return Pattern.compile(
				"^[\\d]{4}[\\.\\-]+[\\d]+[\\.\\-]+[\\d]+[\\s\\tTt]+[\\d]+[:]+[\\d]+[:]+[\\d]+[.]{0,}[\\d]{0,}[zZ]{0,}$")
				.matcher(string).find();
	}

	public static boolean isLocalDateTime(String string) {
		return Pattern.compile(
				"^[\\d]{4}[.\\-]+[\\d]+[.\\-]+[\\d]+[\\s\\tTt]+[\\d]+[:]+[\\d]+[:]+[\\d]+[.]{0,}[\\d]{0,}[zZ]{0,}$")
				.matcher(string).find();
	}

	public static long toEpochMilli(LocalDateTime ldt) {
		return ldt.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
	}

	/**
	 * 获取当前Date型日期
	 * 
	 * @return Date() 当前日期
	 */
	public static Date getNowDate() {
		return new Date();
	}

	/**
	 * 获取当前日期, 默认格式为yyyy-MM-dd
	 * 
	 * @return String
	 */
	public static String getDate() {
		return dateTimeNow(YYYY_MM_DD);
	}

	public static final String getTime() {
		return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
	}

	public static final String dateTimeNow() {
		return dateTimeNow(YYYYMMDDHHMMSS);
	}

	public static final String dateTimeNow(final String format) {
		return parseDateToStr(format, new Date());
	}

	public static final String dateTime(final Date date) {
		return parseDateToStr(YYYY_MM_DD, date);
	}

	public static final String parseDateToStr(final String format, final Date date) {
		return new SimpleDateFormat(format).format(date);
	}

	public static final Date dateTime(final String format, final String ts) {
		try {
			return new SimpleDateFormat(format).parse(ts);
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 日期路径 即年/月/日 如2018/08/08
	 */
	public static final String datePath() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyy/MM/dd");
	}

	/**
	 * 日期路径 即年/月/日 如20180808
	 */
	public static final String dateTime() {
		Date now = new Date();
		return DateFormatUtils.format(now, "yyyyMMdd");
	}

	/**
	 * 日期型字符串转化为日期 格式
	 */
	public static Date parseDate(Object str) {
		if (str == null) {
			return null;
		}
		try {
			return parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获取服务器启动时间
	 */
	public static Date getServerStartDate() {
		long time = ManagementFactory.getRuntimeMXBean().getStartTime();
		return new Date(time);
	}

	/**
	 * 计算两个时间差
	 */
	public static String getDatePoor(Date endDate, Date nowDate) {
		long nd = 1000 * 24 * 60 * 60;
		long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		// long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = endDate.getTime() - nowDate.getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		// long sec = diff % nd % nh % nm / ns;
		return day + "天" + hour + "小时" + min + "分钟";
	}

	// 原tm3 Dates===

	/**
	 * 获取当前日期对象 时分秒为0
	 * 
	 * @return Date对象
	 */
	public static Date getND() {
		try {
			return parseDate(getNowDateStr());
		} catch (Exception e) {
			log.error("", e);
		}
		return null;
	}

	/**
	 * 获取当前日期对象 毫秒为0
	 * 
	 * @return Date对象
	 */
	public static Date getNDT() {
		return parseDateTime(getNowDateTimeStr());
	}

	/**
	 * 获取当前日期字符串yyyy-MM-dd
	 * 
	 * @return Date对象
	 */
	public static String getNowDateStr() {
		return formatDate(getNowDate(), "yyyy-MM-dd");
	}

	/**
	 * 获取当前日期字符串yyyy-MM-dd HH:mm:ss
	 * 
	 * @return Date对象
	 */
	public static String getNowDateTimeStr() {
		return formatDateTime(getNowDate());
	}

	/**
	 * 获取当前日期字符串yyyy_MM_dd_HH_mm_ss
	 * 
	 * @return Date对象
	 */
	public static String getDTStr() {
		return format(getNowDate(), "yyyy_MM_dd_HH_mm_ss");

	}

	/**
	 * 获取当前日期字符串yyyyMMdd_HHmmssS
	 * 
	 * @return Date对象
	 */
	public static String getYmdhmssStr() {
		return format(getNowDate(), "yyyyMMdd_HHmmssS");

	}

	/**
	 * 获取当前月字符串yyyy-MM
	 * 
	 * @return String
	 */
	public static String getNowYmStr() {
		return format(getNowDate(), "yyyy-MM");
	}

	/**
	 * 获取上月字符串yyyy-MM
	 * 
	 * @return String
	 */
	public static String getUpYmStr() {
		return format(doMonth(getNowDate(), -1), "yyyy-MM");
	}

	/**
	 * @category 获取月份
	 * @param diff
	 * @return
	 */
	public static String getYmStr(int diff) {
		return format(doMonth(getNowDate(), diff), "yyyy-MM");
	}

	/**
	 * 根据参数获取当前日期年月日 1：年 2：月 3：日 其它：0
	 * 
	 * @return Date对象
	 */
	public static String getYMD(int i) {
		Calendar cal = Calendar.getInstance();
		if (i == 1) {
			return cal.get(Calendar.YEAR) + "";
		} else if (i == 2) {
			return (cal.get(Calendar.MONTH) + 1) + "";
		} else if (i == 3) {
			return cal.get(Calendar.DAY_OF_MONTH) + "";
		} else {
			return "0";
		}
	}

	/**
	 * 获取本月月末字符
	 * 
	 * @param strMonth
	 * @return 月末字符串
	 */
	public static String getMonthEnd(String strMonth) {
		String strMonthEnd = "";
		try {
			Calendar ca = Calendar.getInstance();
			ca.setTime(parseDate(strMonth + "-01"));
			ca.set(Calendar.DAY_OF_MONTH, 1);
			// Date firstDate = ca.getTime();
			ca.add(Calendar.MONTH, 1);
			ca.add(Calendar.DAY_OF_MONTH, -1);
			Date lastDate = ca.getTime();
			strMonthEnd = format(lastDate, "yyyy-MM-dd");
		} catch (Exception e) {
			log.error("", e);
		}
		return strMonthEnd;
	}

	/**
	 * 获取本月日期字符（月初月末）
	 * 
	 * @return 字符串数组[起始日期，结束日期]
	 */
	public static String[] nowMonthStr() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		str[0] = formatDateTime(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		str[1] = formatDateTime(cal.getTime());
		return str;
	}

	/**
	 * 获取本月日期字符（月初月末）
	 * 
	 * @return 字符串数组[起始日期时间，结束日期时间]
	 */
	public static String[] nowMonthStrDay() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		str[0] = formatDate(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		str[1] = formatDate(cal.getTime());
		return str;
	}

	/**
	 * 获取上月日期字符（月初月末）
	 * 
	 * @return 字符串数组[起始日期00:00:00，结束日期23:59:59]
	 */
	public static String[] upMonthStr() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.add(Calendar.MONTH, -1);
		str[0] = formatDateTime(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		str[1] = formatDateTime(cal.getTime());
		return str;
	}

	/**
	 * 获取上月日期字符（月初月末）
	 * 
	 * @return 字符串数组[起始日期，结束日期]
	 */
	public static String[] upMonthStrDay() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.add(Calendar.MONTH, -1);
		str[0] = formatDate(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		str[1] = formatDate(cal.getTime());
		return str;
	}

	/**
	 * 获取上月日期字符（月初）
	 * 
	 * @return 字符串数组[起始日期00:00:00，结束日期00:00:00]
	 */
	public static String[] upMonthStr2() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.add(Calendar.MONTH, -1);
		str[0] = formatDateTime(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		str[1] = formatDateTime(cal.getTime());
		return str;
	}

	/**
	 * 获取本年年初年末日期
	 * 
	 * @return 字符串数组[起始日期00:00:00，结束日期00:00:00]
	 */
	public static String[] nowYearDays() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.MONTH, 0);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		str[0] = formatDate(cal.getTime());
		cal.add(Calendar.YEAR, 1);
		cal.add(Calendar.DAY_OF_YEAR, -1);
		str[1] = formatDate(cal.getTime());
		return str;
	}

	/**
	 * 返回英文格式日期
	 * 
	 * @return 字符串 格式:日月年
	 */
	public static String formatErq(String rq) {
		String rval = "";
		if (rq == null) {
		} else if (rq.length() == 10) {
			rval = rq.substring(8, 10) + "-" + getEMonths().get(rq.substring(5, 7)) + "-" + rq.substring(0, 4);
		} else if (rq.length() == 19) {
			rval = rq.substring(8, 10) + "-" + getEMonths().get(rq.substring(5, 7)) + "-" + rq.substring(0, 4)
					+ rq.substring(10);
		}

		return rval;
	}

	/**
	 * 比较时间 时间1 是否大于 时间2
	 * 
	 * @param time1 时间1
	 * @param time2 时间2
	 * @return 布尔类型
	 */
	public static boolean bjTime(String time1, String time2) {
		boolean flag = true;
		Date d1 = parseDateTime("1900-01-01 " + time1);
		Date d2 = parseDateTime("1900-01-01 " + time2);
		flag = d1.compareTo(d2) > 0;
		return flag;
	}

	/**
	 * 获得两个日期间的天数
	 * 
	 * @param qsj 起时间
	 * @param zsj 止时间
	 * @return int 天数
	 */
	public static int getExistDays(Date qsj, Date zsj) {
		int d = 0;
		if (qsj == null || zsj == null)
			return d;
		long qlong = qsj.getTime();
		long zlong = zsj.getTime();
		if (zlong > qlong) {
			return new Long((zlong - qlong) / (1000 * 3600 * 24)).intValue();
		}

		return d;
	}

	/**
	 * @category 返回两个日期间的整天数
	 * @param qsj
	 * @param zsj
	 * @return
	 */
	public static int dayDiff(Date qsj, Date zsj) {
		Long d = diffDate(zsj, qsj, DAY);
		return d.intValue();
	}

	/**
	 * <AUTHOR> 2009-12-9下午03:46:11 获得两个时间间的秒数
	 * @param qsj
	 * @param zsj
	 * @return
	 */
	public static int getExistSecond(Date qsj, Date zsj) {
		if (qsj == null || zsj == null)
			return 360000;
		long qlong = qsj.getTime();
		long zlong = zsj.getTime();
		return Math.abs(new Long((zlong - qlong) / 1000).intValue());

	}

	/**
	 * 获取本年年初日期
	 * 
	 * @return 字符串
	 */
	public static String nowYearFirstDay() {
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		cal.set(Calendar.MONTH, 0);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		return formatDate(cal.getTime());
	}

	/**
	 * 格式化输出日期
	 * 
	 * @param dt 日期 格式：yyyy-MM-dd HH-mm-ss
	 * @return 字符串
	 */
	public static String formatDateTime(Date dt) {
		if (dt == null) {
			return "";
		} else {
			return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dt);
		}
	}

	/**
	 * 格式化输出日期
	 * 
	 * @param dt 日期 格式：yyyy-MM-dd
	 * @return 字符串
	 */
	public static String formatTime(Date dt) {
		if (dt == null) {
			return "";
		} else {
			return new SimpleDateFormat("HH:mm:ss").format(dt);
		}
	}

	/**
	 * 格式化输出日期
	 * 
	 * @param dt 日期 格式：自定义
	 * @return 字符串
	 */
	public static String format(Date dt, String s) {
		if (dt == null) {
			return "";
		} else {
			try {
				return new SimpleDateFormat(s).format(dt);
			} catch (Exception e) {
				return "";
			}
		}
	}

	/**
	 * 解析输出日期类型数据 格式yyyy-MM-dd
	 * 
	 * @param dtStr 日期字符串
	 * @param Date  出现错误返回参数 格式：yyyy-MM-dd
	 * @return DATE
	 */
	public static Date parseDate(String dtStr, Date o) {
		if (dtStr == null ? true : dtStr.length() == 0)
			return o;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			if (dtStr == null ? true : "".equals(dtStr)) {
				return sdf.parse(sdf.format(new Date()));
			} else {
				return sdf.parse(dtStr);
			}
		} catch (Exception e) {
			return o;
		}

	}

	/**
	 * 解析输出日期类型数据
	 * 
	 * @param dtStr 日期字符串
	 * @deprecated 格式：yyyy-MM-dd HH:mm:ss.S
	 * @return
	 */
	public static Date parseSqlDate(String dtStr) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.S");
		try {
			if (dtStr == null ? true : "null".equals(dtStr)) {
				return null;
			} else {
				return sdf.parse(dtStr);
			}
		} catch (Exception e) {
			return new Date();
		}

	}

	/**
	 * 解析输出日期类型数据
	 * 
	 * @param dtStr 日期字符串 格式：yyyy-MM-dd HH-mm-ss
	 * @return DATE
	 */
	public static Date parseDateTime(String dtStr) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			if (dtStr == null ? true : "".equals(dtStr)) {
				return sdf.parse(sdf.format(new Date()));
			} else {
				dtStr = dtStr.replace("T", " ");// 考虑如果带有 2018-05-20T00:00:00 这种格式的日期，也可以转换
				return sdf.parse(dtStr);
			}
		} catch (Exception e) {
			return new Date();
		}
	}

	/**
	 * <AUTHOR> 2009-12-9下午03:47:22 特殊解析日期方法
	 * @param dtStr
	 * @param format
	 * @return
	 */
	public static Date parseD(String dtStr, String format) {
		format = format == null ? "yyyy-MM-dd" : format;
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try {
			if (dtStr == null ? true : "".equals(dtStr)) {
				return sdf.parse(sdf.format(new Date()));
			} else {
				return sdf.parse(dtStr);
			}
		} catch (Exception e) {
			return new Date();
		}

	}

	/**
	 * 对秒进行加减
	 */
	public static Date doSecond(Date dt, int second) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.SECOND, second);

		return cal.getTime();
	}

	/**
	 * 对分钟进行加减
	 */
	public static Date doMinute(Date dt, int minute) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.MINUTE, minute);

		return cal.getTime();
	}

	/**
	 * 对日期进行加减天数
	 */
	public static Date doDate(Date dt, int day) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.DAY_OF_MONTH, day);

		return cal.getTime();
	}

	/**
	 * 对月份进行加减
	 */
	public static Date doMonth(Date dt, int yue) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.MONTH, yue);

		return cal.getTime();
	}

	/**
	 * 对月份进行加减
	 * 
	 * @param month 月份字符串 yyyy-MM
	 * @param yue   月 -100~100之间，-1代表上个月，1代表下个月，0代表当前月
	 * @return yyyy-MM(出错，返回传入月份)
	 */
	public static String doMonth(String month, String yue) {
		String retValue = month;
		if (yue != null && !"".equals(yue.trim())) {
			try {
				int iyue = Integer.parseInt(yue);
				String tempMonth = month + "-01";
				retValue = format(doMonth(parseDate(tempMonth), iyue), "yyyy-MM");
			} catch (Exception ex) {
				retValue = month;
				log.error("", ex);
			}
		}
		return retValue;
	}

	/**
	 * 对小时进行加减
	 */
	public static Date doHour(Date dt, int hour) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.HOUR, hour);

		return cal.getTime();
	}

	/**
	 * 对年进行加减
	 */
	public static Date doYear(Date dt, int nian) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		cal.add(Calendar.YEAR, nian);

		return cal.getTime();
	}

	/**
	 * 日期相差天数 dataSort: day hour minute second
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	public static long diffDate(Date d1, Date d2, String dataSort) {
		if (d1 == null || d2 == null)
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		if (DAY.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60 * 60 * 24);
		} else if (HOUR.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60 * 60);
		} else if (MINITE.equals(dataSort)) {
			return (l1 - l2) / (1000 * 60);
		} else if (SECOND.equals(dataSort)) {
			return (l1 - l2) / 1000;
		}
		return 0;
	}

	/**
	 * 该日期与当前日期比较
	 * 
	 * @param d1
	 * @return 布尔类型
	 */
	public static long diffDate(Date d1) {
		return diffDate(d1, new Date(), "day");
	}

	/**
	 * 当前日期与该日期比较
	 * 
	 * @param d1
	 * @return 布尔类型
	 */
	public static long diffDate2(Date d1) {
		return diffDate(new Date(), d1, "day");
	}

	/**
	 * 日期相差秒毫秒数
	 * 
	 * @param d1
	 * @param d2
	 * @return long
	 */
	public static long diffSs(Date d1, Date d2) {
		if (d1 == null || d2 == null)
			return 0;
		if ("null".equals(d1) || "null".equals(d2))
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		long l = l1 - l2;
		if (l < 0)
			return -l;
		return l;
	}

	/**
	 * 此函数有问题，禁止使用
	 * 
	 * @param d1
	 * @param d2
	 * @return
	 */
	/*
	 * @Deprecated public static long diffSec(Date d1, Date d2) { if (d1 == null ||
	 * d2 == null) return 0; if ("null".equals(d1) || "null".equals(d2)) return 0;
	 * long l1 = d1.getTime(); long l2 = d2.getTime(); long l = ((l1 - l2) / 1000) +
	 * 1; if (l < 0) return -l; return l; }
	 */

	/**
	 * 日期相差秒数
	 * 
	 * @d
	 * @param d1
	 * @param d2
	 * @return long
	 */

	public static long diffSecond(Date d1, Date d2) {
		if (d1 == null || d2 == null)
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		long l = Math.abs(((l1 - l2) / 1000));
		return l;
	}

	/**
	 * 日期相差小时
	 * 
	 * @param d1
	 * @param d2
	 * @return double
	 */
	public static double diffHour(Date d1, Date d2) {
		if (d1 == null || d2 == null)
			return 0;
		if ("null".equals(d1) || "null".equals(d2))
			return 0;
		long l1 = d1.getTime();
		long l2 = d2.getTime();
		double d = 3600000.00;
		double l = (l1 - l2) / d;
		l = Math.round(l * 100);
		l = l / 100.00;
		return l;
	}

	/**
	 * 日期相差小时
	 * 
	 * @param d1
	 * @param d2
	 * @return double
	 */
	public static double diffHour(String strD1, String strD2) {
		Date d1 = parseDateTime(strD1);
		Date d2 = parseDateTime(strD2);
		return diffHour(d1, d2);
	}

	/**
	 * 比较两个日期大小
	 * 
	 * @param d1 日期
	 * @param d2 日期
	 * @return int 1:d1>d2 -1:d1<d2 0:d1==d2
	 */
	public static int bjDate(Date d1, Date d2) {
		return d1.compareTo(d2);
	}

	/**
	 * 判断时间d 是否在d1和d2时间段间
	 * 
	 * @param d1 起时间
	 * @param d2 止时间
	 * @param d  要比较的时间
	 * @return
	 */
	public static boolean bjDate(Date d1, Date d2, Date d) {
		boolean flag = false;
		if (d.after(d1) && d.before(d2))
			return true;
		return flag;
	}

	public static boolean bjDateF(Date d1, Date d2, Date d) {
		boolean flag = false;
		if ((d.after(d1) || d.compareTo(d1) == 0) && d.before(d2))
			return true;
		return flag;
	}

	public static boolean bjDateL(Date d1, Date d2, Date d) {
		boolean flag = false;
		if (d.after(d1) && (d.compareTo(d2) == 0 || d.before(d2)))
			return true;
		return flag;
	}

	public static boolean bjDate(String d11, String d22, String d0) {
		boolean flag = false;
		try {
			Date d = parseDateTime(d0);
			Date d1 = parseDateTime(d11);
			Date d2 = parseDateTime(d22);
			if (d.after(d1) && d.before(d2))
				return true;
		} catch (Exception e) {
			return flag;
		}
		return flag;
	}

	public static boolean bjDate(Date d, Object obj) {
		boolean flag = false;
		try {
			String[] s = (String[]) obj;
			Date d1 = parseDateTime(s[0]);
			Date d2 = parseDateTime(s[1]);
			if (d.after(d1) && d.before(d2))
				return true;
		} catch (Exception e) {
			return flag;
		}
		return flag;
	}

	/**
	 * 获得月份列表
	 * 
	 * @return
	 */
	public static Map<String, String> getMonths() {
		return getMonths(false);
	}

	public static Map<String, String> getMonths(boolean flag) {
		Map<String, String> map = new LinkedHashMap<String, String>();
		if (flag) {
			map.put("1", "一月");
			map.put("2", "二月");
			map.put("3", "三月");
			map.put("4", "四月");
			map.put("5", "五月");
			map.put("6", "六月");
			map.put("7", "七月");
			map.put("8", "八月");
			map.put("9", "九月");
			map.put("10", "十月");
			map.put("11", "十一月");
			map.put("12", "十二月");
		} else {
			for (int i = 1; i < 13; i++) {
				map.put(i + "", i + "月");
			}
		}

		return map;
	}

	public static String getNowYear() {
		@SuppressWarnings("deprecation")
		int y = getNowDate().getYear() + 1900;
		return String.valueOf(y);
	}

	public static Map<String, String> getEMonths() {
		Map<String, String> ymap = new LinkedHashMap<String, String>(12);
		ymap.put("01", "Jan");
		ymap.put("02", "Feb");
		ymap.put("03", "Mar");
		ymap.put("04", "Apr");
		ymap.put("05", "May");
		ymap.put("06", "Jun");
		ymap.put("07", "Jul");
		ymap.put("08", "Aug");
		ymap.put("09", "Sep");
		ymap.put("10", "Oct");
		ymap.put("11", "Nov");
		ymap.put("12", "Dec");

		return ymap;
	}

	public static String[] upWeekStr() {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		int weekno = getWeedNum(cal.getTime());
		cal.add(Calendar.DAY_OF_MONTH, 1 - weekno);

		cal.add(Calendar.DAY_OF_MONTH, -7);
		str[0] = formatDate(cal.getTime());
		cal.add(Calendar.DAY_OF_MONTH, -7);
		str[1] = formatDateTime(cal.getTime());
		return str;
	}

	public static String[] upDaysStr(int ts) {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(getNowDate());
		str[1] = formatDate(cal.getTime());
		cal.add(Calendar.DAY_OF_MONTH, ts);
		str[0] = formatDate(cal.getTime());
		return str;
	}

	public static String[] getTimeBound(String lx, String hmd) {
		return getTimeBound(lx, hmd, getNowDate());
	}

	/**
	 * 根据参数获取起始、结束时间,起始日期
	 * 
	 * @param lx  间隔类型
	 * @param hmd 起始时间
	 * @param nd  判断依据日期
	 * @return
	 */
	public static String[] getTimeBound(String lx, String hmd, Date nd) {
		String[] tb = { "", "", "" };

		if (lx == null || "日".equals(lx)) {
			String qsj = formatDate(nd);
			Date tdate = parseDateTime(qsj + " " + hmd);
			if (nd.compareTo(parseDateTime(qsj + " " + hmd)) == 1) { // 如果在范围内,记录当天数据
				tb[0] = qsj + " " + hmd;
				tb[1] = formatDateTime(doDate(tdate, 1));
				tb[2] = qsj;
			} else { // 否则为上一范围数据
				tb[0] = formatDateTime(doDate(tdate, -1));
				tb[1] = qsj + " " + hmd;
				tb[2] = formatDate(doDate(tdate, -1));
			}

		} else if ("周".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			int weekDay = cal.get(Calendar.DAY_OF_WEEK);
			if (weekDay > 2) { // 如果是周3~周6
				cal.add(Calendar.DAY_OF_MONTH, 2 - weekDay);
			} else if (weekDay == 1) { // 如果是周日
				cal.add(Calendar.DAY_OF_MONTH, -6);
			}
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得该周第一天
			if (nd.compareTo(qd) == 1) { // 如果在范围内,记录当天数据
				tb[0] = qsj + " " + hmd;
				tb[1] = formatDateTime(doDate(qd, 7));
				tb[2] = qsj;
			} else { // 否则为上一范围数据
				tb[0] = formatDateTime(doDate(qd, -7));
				tb[1] = qsj + " " + hmd;
				tb[2] = formatDate(doDate(qd, -7));
			}

		} else if ("月".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得该月第一天
			if (nd.compareTo(qd) == 1) { // 如果在范围内,记录当天数据
				tb[0] = qsj + " " + hmd;
				tb[1] = formatDateTime(doMonth(qd, 1));
				tb[2] = qsj;
			} else { // 否则为上一范围数据
				tb[0] = formatDateTime(doMonth(qd, -1));
				tb[1] = qsj + " " + hmd;
				tb[2] = formatDate(doMonth(qd, -1));
			}
		} else if ("季".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			cal.set(Calendar.DAY_OF_MONTH, 1);

			int month = cal.get(Calendar.MONTH);
			if (month < 3) {// 第一季度 0~2
				cal.set(Calendar.MONTH, 0);
			} else if (month < 6) {// 第一季度 3~5
				cal.set(Calendar.MONTH, 3);
			} else if (month < 9) {// 第一季度 6~8
				cal.set(Calendar.MONTH, 6);
			} else if (month < 12) {// 第一季度 9~11
				cal.set(Calendar.MONTH, 9);
			}
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得日期
			tb[0] = qsj + " " + hmd;
			tb[1] = formatDateTime(doMonth(qd, 3));
			tb[2] = qsj;

		} else if ("年".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			cal.set(Calendar.MONTH, 0);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得该月第一天
			if (nd.compareTo(qd) == 1) { // 如果在范围内,记录当天数据
				tb[0] = qsj + " " + hmd;
				tb[1] = formatDateTime(doYear(qd, 1));
				tb[2] = qsj;
			} else { // 否则为上一范围数据
				tb[0] = formatDateTime(doYear(qd, -1));
				tb[1] = qsj + " " + hmd;
				tb[2] = formatDate(doYear(qd, -1));
			}
		}
		return tb;
	}

	/**
	 * 关于周的运算****************************************************************************
	 */

	public static String getWeekStr(Date d) {
		String rval = "星期";
		int w = getWeedNum(d);
		switch (w) {
		case 1:
			rval += "一";
			break;
		case 2:
			rval += "二";
			break;
		case 3:
			rval += "三";
			break;
		case 4:
			rval += "四";
			break;
		case 5:
			rval += "五";
			break;
		case 6:
			rval += "六";
			break;
		case 7:
			rval += "日";
			break;
		default:
			break;
		}
		return rval;
	}

	/**
	 * 获得该日是周几
	 * 
	 * @return int (1-7)
	 */
	public static int getWeedNum(Date d) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(d);
		int i = cal.get(Calendar.DAY_OF_WEEK);
		i = i - 1 == 0 ? 7 : (i - 1);
		return i;
	}

	/**
	 * 获得该年月天数
	 * 
	 * @param year
	 * @param month
	 * @return
	 */
	public static int getMonthDays(int year, int month) {
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, year);
		cal.set(Calendar.MONTH, month);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		return cal.get(Calendar.DAY_OF_MONTH);
	}

	@SuppressWarnings("deprecation")
	public static int getMonthDays(String d) {
		try {
			if (d.length() == 7) {
				d += "-01";
				Date date = doDate(doMonth(parseDate(d), 1), -1);
				return date.getDate();
			}
		} catch (Exception e) {
			log.error("", e);
		}

		return 0;
	}

	/**
	 * 当天所在月
	 * 
	 * @param d
	 * @return
	 */
	public static String getWeekMonth(Date d) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(d);
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DAY_OF_MONTH);

		int weekday = getWeedNum(d);
		int weekdays = getMonthDays(year, month);

		if (weekday > 0 && weekday < 4) {// 如果是星期1~3
			if ((weekdays - day) < 3) {// 如果离月末的天数大于2
				if (month == 12) {
					year++;
					month = 1;
				} else {
					month++;
				}
				;
			}
		} else if (weekday > 4) {// 如果是星期5~6
			if ((weekday + day) < 7) {// 如果离月初的天数大于2
				if (month > 1) {
					month--;
				} else {
					year--;
					month = 12;
				}
				;
			}
		} else {// 如果是星期日
			if (day < 4) {// 如果离月初的天数大于2
				if (month > 1) {
					month--;
				} else {
					year--;
					month = 12;
				}
				;
			}
		}
		return year + "-" + month;
	}

	/**
	 * 获得该日所在周的信息
	 * 
	 * @param d
	 * @return string[] (起始日期,结束日期,第几周)
	 */
	public static String[] getWeek(Date d) {
		String[] str = { "", "", "" };
		Calendar cal = Calendar.getInstance();
		Calendar cal2 = Calendar.getInstance();
		cal.setTime(d);
		cal.add(Calendar.DAY_OF_MONTH, 1 - getWeedNum(d));// 获得该周一的日期
		int day = cal.get(Calendar.DAY_OF_MONTH);

		int weekNo = 1;

		cal2.set(Calendar.YEAR, cal.get(Calendar.YEAR));
		cal2.set(Calendar.MONTH, cal.get(Calendar.MONTH));
		cal2.set(Calendar.DAY_OF_MONTH, day);
		int nmonthdays = getMonthDays(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH) + 1);// 该月天数

		if (nmonthdays - day >= 3) {// 小于3的情况，该周属于下月第一周
			int addWeek = day / 7;
			int rm = day % 7;
			if (rm > 4)// 如果前面有一周
				weekNo++;
			weekNo += addWeek;
		}

		str[0] = formatDate(cal.getTime());
		cal.add(Calendar.DAY_OF_MONTH, 6);
		str[1] = formatDate(cal.getTime());
		str[2] = weekNo + "";

		return str;
	}

	/**
	 * 获取日期当月的月初和月末日期时间
	 * 
	 * @param nd
	 * @return
	 */
	public static String[] getNowMonthStr(Date nd) {
		return getNowMonthStr(nd, 1);
	}

	/**
	 * <AUTHOR> 2009-4-24下午01:21:49 获取月初月末字符串
	 * @param d
	 * @param fw 0月初月末到日；1月初月末到秒；2跨月到1号
	 * @return
	 */
	public static String[] getNowMonthStr(Date d, int fw) {
		int hour = 0;
		int minute = 0;
		int second = 0;
		switch (fw) {
		case 0:
			break;
		case 1:
			hour = 23;
			minute = 59;
			second = 59;
			break;
		default:
			break;
		}
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.setTime(d);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		str[0] = formatDateTime(cal.getTime());
		cal.add(Calendar.MONTH, 1);
		if (fw != 2)
			cal.add(Calendar.DAY_OF_MONTH, -1);
		cal.set(Calendar.HOUR_OF_DAY, hour);
		cal.set(Calendar.MINUTE, minute);
		cal.set(Calendar.SECOND, second);
		str[1] = formatDateTime(cal.getTime());
		return str;
	}

	public static String[] getRqBound(String lx, String hmd) {
		String[] rq = { "", "" };
		if (hmd == null)
			hmd = "08:00:00";
		Date nd = getNowDate();
		if ("2".equals(lx)) {
			String qsj = formatDate(nd);
			if (nd.compareTo(parseDateTime(qsj + " " + hmd)) == 1) { // 如果在范围内,记录当天数据
				rq[0] = formatDate(doDate(nd, -1));
				rq[1] = qsj;
			} else { // 否则为上一范围数据
				rq[0] = qsj;
				rq[1] = formatDate(doDate(nd, 1));
			}

		} else if ("3".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			int weekDay = cal.get(Calendar.DAY_OF_WEEK);
			if (weekDay > 2) { // 如果是周3~周6
				cal.add(Calendar.DAY_OF_MONTH, 2 - weekDay);
			} else if (weekDay == 1) { // 如果是周日
				cal.add(Calendar.DAY_OF_MONTH, -6);
			}
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得该周第一天
			if (nd.compareTo(qd) == 1) { // 如果在范围内,记录当天数据
				rq[0] = formatDate(doDate(nd, -7));
				rq[1] = qsj;
			} else { // 否则为上一范围数据
				rq[0] = qsj;
				rq[1] = formatDate(doDate(nd, 7));
			}

		} else if ("4".equals(lx)) {
			Calendar cal = Calendar.getInstance();
			cal.setTime(nd);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			String qsj = formatDate(cal.getTime());
			Date qd = parseDateTime(qsj + " " + hmd);// 获得该月第一天
			if (nd.compareTo(qd) == 1) { // 如果在范围内,记录当天数据
				rq[0] = qsj;
				rq[1] = formatDate(doMonth(nd, 1));
			} else { // 否则为上一范围数据
				rq[0] = formatDate(doMonth(nd, -1));
				rq[1] = qsj;
			}
		}

		return rq;
	}

	/**
	 * <AUTHOR> 2009-6-22下午02:08:32 show 判断是否是日期时间格式 yyyy-MM-dd HH:mm:ss
	 * @param str 输入判断的内容
	 * @return boolean
	 */
	public static boolean isDateTime(String str) {
		return isDataMatch(str,
				"([1-2])\\d{3}(-)(0?[1-9]|1[012])\\2(0?[1-9]|[12][0-9]|3[01])( )([01][0-9]|[2][0123]):([0-5][0-9]):([0-5][0-9])");
	}

	public static boolean isDataMatch(String str, String rex) {
		if (str == null)
			return false;
		if (rex == null)
			return false;
		boolean flag = false;
		int len = str.length();
		Pattern pattern = Pattern.compile(rex);
		Matcher m = pattern.matcher(str);
		if (m.find()) {
			if (m.end() == len && m.start() == 0) {
				flag = true;
			}
		}

		return flag;
	}

	public static String[] getQZsj(String yf) {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, Integer.parseInt(yf.split("-")[0]));
		cal.set(Calendar.MONTH, Integer.parseInt(yf.split("-")[1]) - 1);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		int ts = cal.get(Calendar.DAY_OF_MONTH);
		str[0] = yf + "-01";
		str[1] = yf + "-" + ts;
		return str;
	}

	/**
	 * 获取本月第一个周一和最后一个周日的日期
	 * 
	 * @param yf
	 * @return
	 */
	public static String[] getQZWeekday(String yf) {
		String[] str = { "", "" };
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, Integer.parseInt(yf.split("-")[0]));
		cal.set(Calendar.MONTH, Integer.parseInt(yf.split("-")[1]) - 1);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		int dayofweek = cal.get(Calendar.DAY_OF_WEEK);
		if (dayofweek < 6 && dayofweek != 1) {// 取本周一
			cal.add(Calendar.DAY_OF_MONTH, 2 - dayofweek);
		} else if (dayofweek > 5 || dayofweek == 1) {// 取下周一
			if (dayofweek == 1) {
				cal.add(Calendar.DAY_OF_MONTH, 1);
			} else {
				cal.add(Calendar.DAY_OF_MONTH, 7 - dayofweek + 2);
			}
		}
		// 取周一日期
		int mon = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		String monstr = "" + mon;
		String daystr = "" + day;
		if (mon < 10)
			monstr = "0" + mon;
		if (day < 10)
			daystr = "0" + day;
		str[0] = cal.get(Calendar.YEAR) + "-" + monstr + "-" + daystr;

		cal.set(Calendar.YEAR, Integer.parseInt(yf.split("-")[0]));
		cal.set(Calendar.MONTH, Integer.parseInt(yf.split("-")[1]) - 1);
		cal.set(Calendar.DAY_OF_MONTH, 1);
		cal.add(Calendar.MONTH, 1);
		cal.add(Calendar.DAY_OF_MONTH, -1);
		dayofweek = cal.get(Calendar.DAY_OF_WEEK);
		if (dayofweek > 4 || dayofweek == 1) {// 取本周日
			if (dayofweek != 1) {
				cal.add(Calendar.DAY_OF_MONTH, 7 - dayofweek + 1);
			}
		} else if (dayofweek < 5 && dayofweek != 1) {// 取上周日
			cal.add(Calendar.DAY_OF_MONTH, 1 - dayofweek);
		}
		mon = cal.get(Calendar.MONTH) + 1;
		day = cal.get(Calendar.DAY_OF_MONTH);
		monstr = "" + mon;
		daystr = "" + day;
		if (mon < 10)
			monstr = "0" + mon;
		if (day < 10)
			daystr = "0" + day;
		str[1] = cal.get(Calendar.YEAR) + "-" + monstr + "-" + daystr;

		return str;
	}

	/**
	 * <AUTHOR> 2010-11-24上午09:46:19 获取两个日期月份差值
	 * @param dtFrom 结束日期
	 * @param dtEnd  起始日期
	 * @return 数值型
	 */
	public static Integer monthDiff(Date dtFrom, Date dtEnd) {
		if (dtFrom == null || dtEnd == null)
			return null;
		Calendar calf = Calendar.getInstance();
		calf.setTime(dtFrom);
		Calendar cale = Calendar.getInstance();
		cale.setTime(dtEnd);

		int fm = calf.get(Calendar.YEAR) * 12 + calf.get(Calendar.MONTH);
		int em = cale.get(Calendar.YEAR) * 12 + cale.get(Calendar.MONTH);

		return em - fm;
	}

	/**
	 * <AUTHOR> 2010-11-24下午12:53:12 自定义日期
	 * @param year  年
	 * @param month 月
	 * @param day   日
	 * @return Date日期对象
	 */
	public static Date getDate(Integer year, Integer month, Integer day) {
		Calendar cal = Calendar.getInstance();
		if (year != null)
			cal.set(Calendar.YEAR, year);
		if (year != month)
			cal.set(Calendar.MONTH, month - 1);
		if (year != day)
			cal.set(Calendar.DAY_OF_MONTH, day);

		return cal.getTime();
	}

	/**
	 * 比较输入时间是否在给定时间段内(时间可以跨天 startTime>endTime)
	 * 
	 * <AUTHOR> 2012-05-29下午16:47:00
	 * @param time      (String) {11:11:11} 要判断的时间
	 * @param startTime (String) {00:00:00} 起始时间
	 * @param endTime   (String) {23:59:59}截止时间
	 * @return boolean true 在时间内，false 不在时间内
	 */
	public static boolean isInTime(String time, String startTime, String endTime) {

		boolean result = false;

		if (time != null && startTime != null && endTime != null) {// 参数有效

			if (bjTime(startTime, endTime)) {// 起始时间大于截止时间(时间跨天)

				if (bjTime(startTime, time) && bjTime(time, endTime)) {// 在起始时间和截止时间之外

					result = false;

				} else {

					result = true;
				}

			} else {

				if (bjTime(startTime, time) || bjTime(time, endTime)) {// 在起始时间和截止时间之外

					result = false;

				} else {

					result = true;
				}

			}

		}

		return result;

	}

	/**
	 * 格式化非标准日期
	 * 
	 * @category 格式化非标准日期
	 * @param dataTimeStr (String) 非标准化的日期字符串 例： 2014/3/26 0:00:00 或 2014/3/26
	 * @return String 标准化的日期字符串 例： 2014-03-26 00:00:00
	 */
	public static String formNonStandardDateTime(String dataTimeStr) {
		String result = getNowDateStr();
		if (dataTimeStr != null && dataTimeStr.length() != 0) {// 日期参数有效
			StringBuffer resultBuf = new StringBuffer();
			String[] dataArr = dataTimeStr.split("\\D");
			if (dataArr != null && dataArr.length > 0) {
				String[] nowArr = result.split("-");
				if (dataArr.length > 0) {// 年
					resultBuf.append(dataArr[0]);
				} else {
					resultBuf.append(nowArr[0]);
				}

				if (dataArr.length > 1) {// 月
					String month = dataArr[1];
					if (month.length() < 2) {// 不足2位
						month = "0" + month;
					}
					resultBuf.append("-" + month);
				} else {
					resultBuf.append("-" + nowArr[1]);
				}

				if (dataArr.length > 2) {// 日
					String day = dataArr[2];
					if (day.length() < 2) {// 不足2位
						day = "0" + day;
					}
					resultBuf.append("-" + day);
				} else {
					resultBuf.append("-" + nowArr[2]);
				}

				if (dataArr.length > 3) {// 时
					String hour = dataArr[3];
					if (hour.length() < 2) {// 不足2位
						hour = "0" + hour;
					}
					resultBuf.append(" " + hour);
				} else {
					resultBuf.append(" 00");
				}
				if (dataArr.length > 4) {// 分
					String minute = dataArr[4];
					if (minute.length() < 2) {// 不足2位
						minute = "0" + minute;
					}
					resultBuf.append(":" + minute);
				} else {
					resultBuf.append(":00");
				}
				if (dataArr.length > 5) {// 秒
					String second = dataArr[5];
					if (second.length() < 2) {// 不足2位
						second = "0" + second;
					}
					resultBuf.append(":" + second);
				} else {
					resultBuf.append(":00");
				}
			}
			result = resultBuf.toString();
		} else {
			result += " 00:00:00";// 补足时间部分
		}
		return result;
	}

	/**
	 * 
	 * @category 获取 日期所在的周
	 * @param date 日期格式 yyyy-MM-dd
	 * @return 周数相关数据 2016-12,1,2016-11-28,2016-12-4
	 *
	 *         例: String dateString = "2016-11-30";
	 *         log.info(Coms.strsToStr(Dates.getDateByWeek(dateString)));
	 *         输出：2016-12,1,2016-11-28,2016-12-4 2016-12：表示日期所在周的月份 1：表示第几周
	 *         2016-11-28:这周开始日期 即 本周一 2016-12-4: 这周结束日期 即 本周日 2016-11-30
	 *         所属：2016年-12月的第1周，这周的开始日期:2016-11-28 结束日期：2016-12-04
	 *
	 */
	public static String[] getDateByWeek(String date) {
		String[] result = null;
		try {
			List<String> strlist = null;
			String oldDate = getNowDateStr();
			Date paramDate = parseDate(date);
			String yy = format(paramDate, "yyyy");
			String mm = format(paramDate, "MM");
			String[] monStratEnd = null;// 获取周组件的月份开始和结束日期
			Integer dd = Integer.valueOf(format(paramDate, "dd"));
			OK: for (int i = 1; i <= 2; i++) {// 循环二次 第一次 找 本月 第二次 找上月或下月的
				int year = 0;
				int month = 0;
				if (yy == null || yy.equals("")) {
					year = Integer.valueOf(format(parseDate(oldDate), "yyyy"));
					yy = format(parseDate(oldDate), "yyyy");
				} else {
					year = Integer.valueOf(yy);
				}
				if (mm == null || mm.equals("")) {
					mm = format(parseDate(oldDate), "MM");
					month = Integer.valueOf(format(parseDate(oldDate), "MM"));
				} else {
					month = Integer.valueOf(mm);
				}
				if (month > 12) {
					month = 1;
					year = year + 1;
					mm = "01";
					yy = String.valueOf(year);
				}
				if (month < 1) {
					month = 12;
					year = year - 1;
					mm = "12";
					yy = String.valueOf(year);
				}
				Date newDate = parseDate(yy + "-" + mm + "-01");
				newDate = doMonth(newDate, 1);

				int[] m = new int[13];

				m[1] = 31;
				m[3] = 31;
				m[5] = 31;
				m[7] = 31;
				m[8] = 31;
				m[10] = 31;
				m[12] = 31;
				m[2] = 28;
				m[4] = 30;
				m[6] = 30;
				m[9] = 30;
				m[11] = 30;
				if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {// 是闰年
					m[2] = 29;
				}
				int mms = m[month];// 本月标准天数
				Date d1 = parseDate(yy + "-" + mm + "-01");// 本月1号日期
				Date d2 = doDate(newDate, -1);// 本月最后一号日期
				String day = format(doDate(d1, -1), "dd");
				int upMms = Integer.valueOf(day);
				int week1 = getWeedNum(d1) + 1;
				int week2 = getWeedNum(d2) + 1;
				if (week1 == 1)
					week1 = 8;
				if (week2 == 1)
					week2 = 8;
				week1 = week1 - 1;
				week2 = week2 - 1;
				int fAdds = 0;
				int eAdds = 0;
				int days = mms;
				if (week1 <= 4) {
					fAdds = week1 - 1;// '从上月拿来天数
					days = days + fAdds;
					// month = month - 1;
				} else {
					fAdds = -1 * (8 - week1);// '从本月拿走天数
					days = days + fAdds;
				}
				if (week2 >= 4) {
					eAdds = 7 - week2;// '从下月拿来天数
					days = days + eAdds;
					// month = month + 1;
				} else {
					eAdds = -1 * week2;// '从本月拿走天数
					days = days + eAdds;
				}
				int yStr = 0;
				int mStr = 0;
				int fStr = 0;
				int dayNum2 = 0;
				int mStr2 = 0;
				int yStr2 = 0;
				int fStr2 = 0;
				int zs = 0;
				// String fColor = "";
				String resultStr = "";
				for (int dayNum = 1; dayNum <= days; dayNum++) {// 遍历这个月的天数
					if (fAdds > 0 && dayNum <= fAdds) {
						yStr = year;
						mStr = month - 1;
						if (mStr == 0) {
							mStr = 12;
							yStr = yStr - 1;
						}
						fStr = upMms - fAdds + dayNum;
						// fColor = "#999999";
					} else if (eAdds > 0 && (dayNum - fAdds) > mms) {
						yStr = year;
						mStr = month + 1;
						if (mStr == 13) {
							mStr = 1;
							yStr = yStr + 1;
						}
						fStr = dayNum - fAdds - mms;
						// fColor = "#999999";
					} else {
						yStr = year;
						mStr = month;
						fStr = dayNum - fAdds;
						// fColor = "#000000";
					}

					dayNum2 = dayNum + 6;
					if (fAdds > 0 && dayNum2 <= fAdds) {
						yStr2 = year;
						mStr2 = month - 1;
						if (mStr2 == 0) {
							mStr2 = 12;
							yStr2 = yStr2 - 1;
						}
						fStr2 = upMms - fAdds + dayNum2;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						fStr2 = dayNum2 - fAdds - mms;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						fStr2 = dayNum2 - fAdds - mms;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						fStr2 = dayNum2 - fAdds - mms;
					} else {
						yStr2 = year;
						mStr2 = month;
						fStr2 = dayNum2 - fAdds;
					}

					if ((dayNum - 1) % 7 == 0) {
						zs = (dayNum - 1) / 7 + 1;
						// log.info((yStr + "-" + mI + "-" + fI));
						// 日期显示格式化
						int mmInt = -1;
						if (Coms.judgeDouble(mm)) {
							mmInt = Integer.valueOf(mm);
							if (mmInt < 10) {
								mm = "0" + mmInt;
							}
						}
						String mStrStr = mStr + "";
						if (mStr < 10) {
							mStrStr = "0" + mStr;
						}
						String fStrStr = fStr + "";
						if (fStr < 10) {
							fStrStr = "0" + fStr;
						}
						String fStr2Str = fStr2 + "";
						if (fStr2 < 10) {
							fStr2Str = "0" + fStr2;
						}

						String mStr2Str = mStr2 + "";
						if (mStr2 < 10) {
							mStr2Str = "0" + mStr2;
						}
						resultStr = yy + "-" + mm + "," + zs + "," + yStr + "-" + mStrStr + "-" + fStrStr + "," + yStr2
								+ "-" + mStr2Str + "-" + fStr2Str;

					}
					String mI = "";
					if (mStr > 0 && mStr < 10) {
						mI = "0" + mStr;
					} else {
						mI = String.valueOf(mStr);
					}
					String fI = "";
					if (fStr > 0 && fStr < 10) {
						fI = "0" + fStr;
					} else {
						fI = String.valueOf(fStr);
					}
					if ((yStr + "-" + mI + "-" + fI).equals(date)) {
						// log.info(r);
						strlist = Coms.StrToList(resultStr, ",");
						monStratEnd = getWeekViewStartEnd(yy + "-" + mm);
						break OK;
					} else {
					}
				}
				if (dd >= 23) {
					month = month + 1;// '从下月拿来天数
					if (month > 12) {
						month = 1;
						year = year + 1;
						yy = String.valueOf(year);
					}
					if (month <= 9) {
						mm = "0" + month;
					} else {
						mm = String.valueOf(month);
					}
				} else if (dd <= 7) {// '从上月拿来天数
					month = month - 1;
					if (month < 1) {
						month = 12;
						year = year - 1;
						yy = String.valueOf(year);
					}
					if (month <= 9) {
						mm = "0" + month;
					} else {
						mm = String.valueOf(month);
					}

				}
			}
			if (strlist != null && strlist.size() > 0 && monStratEnd != null && monStratEnd.length == 2) {
				strlist.add(monStratEnd[0]);
				strlist.add(monStratEnd[1]);
				result = Coms.listToString(strlist, ",").split(",");
			}

		} catch (Exception e) {
			log.error("", e);
		}

		return result;

	}

	/**
	 * 
	 * @category 获取周组件的每个月份的开始和结束日期
	 * @param mon 月份
	 * @return
	 */
	public static String[] getWeekViewStartEnd(String mon) {
		String[] result = new String[2];

		try {
			String date = mon + "-15";
			String oldDate = getNowDateStr();
			Date paramDate = parseDate(date);
			String yy = format(paramDate, "yyyy");
			String mm = format(paramDate, "MM");
			Integer dd = Integer.valueOf(format(paramDate, "dd"));

			for (int i = 1; i <= 2; i++) {// 循环二次 第一次 找 本月 第二次 找上月或下月的
				int year = 0;
				int month = 0;
				if (yy == null || yy.equals("")) {
					year = Integer.valueOf(format(parseDate(oldDate), "yyyy"));
					yy = format(parseDate(oldDate), "yyyy");
				} else {
					year = Integer.valueOf(yy);
				}
				if (mm == null || mm.equals("")) {
					mm = format(parseDate(oldDate), "MM");
					month = Integer.valueOf(format(parseDate(oldDate), "MM"));
				} else {
					month = Integer.valueOf(mm);
				}
				if (month > 12) {
					month = 1;
					year = year + 1;
					mm = "01";
					yy = String.valueOf(year);
				}
				if (month < 1) {
					month = 12;
					year = year - 1;
					mm = "12";
					yy = String.valueOf(year);
				}
				Date newDate = parseDate(yy + "-" + mm + "-01");
				newDate = doMonth(newDate, 1);

				int[] m = new int[13];

				m[1] = 31;
				m[3] = 31;
				m[5] = 31;
				m[7] = 31;
				m[8] = 31;
				m[10] = 31;
				m[12] = 31;
				m[2] = 28;
				m[4] = 30;
				m[6] = 30;
				m[9] = 30;
				m[11] = 30;
				if (year % 4 == 0 && year % 100 != 0 || year % 400 == 0) {// 是闰年
					m[2] = 29;
				}
				int mms = m[month];// 本月标准天数
				Date d1 = parseDate(yy + "-" + mm + "-01");// 本月1号日期
				Date d2 = doDate(newDate, -1);// 本月最后一号日期
				String day = format(doDate(d1, -1), "dd");
				int upMms = Integer.valueOf(day);
				int week1 = getWeedNum(d1) + 1;
				int week2 = getWeedNum(d2) + 1;
				if (week1 == 1)
					week1 = 8;
				if (week2 == 1)
					week2 = 8;
				week1 = week1 - 1;
				week2 = week2 - 1;
				int fAdds = 0;
				int eAdds = 0;
				int days = mms;
				if (week1 <= 4) {
					fAdds = week1 - 1;// '从上月拿来天数
					days = days + fAdds;
					// month = month - 1;
				} else {
					fAdds = -1 * (8 - week1);// '从本月拿走天数
					days = days + fAdds;
				}
				if (week2 >= 4) {
					eAdds = 7 - week2;// '从下月拿来天数
					days = days + eAdds;
					// month = month + 1;
				} else {
					eAdds = -1 * week2;// '从本月拿走天数
					days = days + eAdds;
				}
				int yStr = 0;
				int mStr = 0;
				int fStr = 0;
				int dayNum2 = 0;
				int mStr2 = 0;
				int yStr2 = 0;
				// int fStr2 = 0;
				// int zs = 0;
				// String fColor = "";
				// String resultStr = "";
				for (int dayNum = 1; dayNum <= days; dayNum++) {// 遍历这个月的天数
					if (fAdds > 0 && dayNum <= fAdds) {
						yStr = year;
						mStr = month - 1;
						if (mStr == 0) {
							mStr = 12;
							yStr = yStr - 1;
						}
						fStr = upMms - fAdds + dayNum;
						// fColor = "#999999";
					} else if (eAdds > 0 && (dayNum - fAdds) > mms) {
						yStr = year;
						mStr = month + 1;
						if (mStr == 13) {
							mStr = 1;
							yStr = yStr + 1;
						}
						fStr = dayNum - fAdds - mms;
						// fColor = "#999999";
					} else {
						yStr = year;
						mStr = month;
						fStr = dayNum - fAdds;
						// fColor = "#000000";
					}

					dayNum2 = dayNum + 6;
					if (fAdds > 0 && dayNum2 <= fAdds) {
						yStr2 = year;
						mStr2 = month - 1;
						if (mStr2 == 0) {
							mStr2 = 12;
							yStr2 = yStr2 - 1;
						}
						// fStr2 = upMms - fAdds + dayNum2;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						// fStr2 = dayNum2 - fAdds - mms;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						// fStr2 = dayNum2 - fAdds - mms;
					} else if (eAdds > 0 && (dayNum2 - fAdds) > mms) {
						yStr2 = year;
						mStr2 = month + 1;
						if (mStr2 == 13) {
							mStr2 = 1;
							yStr2 = yStr2 + 1;
						}
						// fStr2 = dayNum2 - fAdds - mms;
					} else {
						yStr2 = year;
						mStr2 = month;
						// fStr2 = dayNum2 - fAdds;
					}

					// zs = (dayNum - 1) / 7 + 1;

					// 日期显示格式化
					int mmInt = -1;
					if (Coms.judgeDouble(mm)) {
						mmInt = Integer.valueOf(mm);
						if (mmInt < 10) {
							mm = "0" + mmInt;
						}
					}
					String mStrStr = mStr + "";
					if (mStr < 10) {
						mStrStr = "0" + mStr;
					}
					String fStrStr = fStr + "";
					if (fStr < 10) {
						fStrStr = "0" + fStr;
					}
					/*
					 * String fStr2Str = fStr2 + ""; if (fStr2 < 10) { fStr2Str = "0" + fStr2; }
					 * 
					 * String mStr2Str = mStr2 + ""; if (mStr2 < 10) { mStr2Str = "0" + mStr2; }
					 */

					// resultStr = yy + "-" + mm + "," + zs + "," + yStr + "-" + mStrStr + "-" +
					// fStrStr + "," + yStr2+ "-" + mStr2Str + "-" + fStr2Str;

					if (dayNum == 1) {
						result[0] = yStr + "-" + mStrStr + "-" + fStrStr;
					}
					if (days == dayNum) {
						result[1] = yStr + "-" + mStrStr + "-" + fStrStr;

					}
					/*
					 * String mI = ""; if (mStr > 0 && mStr < 10) { mI = "0" + mStr; } else { mI =
					 * String.valueOf(mStr); } String fI = ""; if (fStr > 0 && fStr < 10) { fI = "0"
					 * + fStr; } else { fI = String.valueOf(fStr); }
					 */
				}
				if (dd >= 23) {
					month = month + 1;// '从下月拿来天数
					if (month > 12) {
						month = 1;
						year = year + 1;
						yy = String.valueOf(year);
					}
					if (month <= 9) {
						mm = "0" + month;
					} else {
						mm = String.valueOf(month);
					}
				} else if (dd <= 7) {// '从上月拿来天数
					month = month - 1;
					if (month < 1) {
						month = 12;
						year = year - 1;
						yy = String.valueOf(year);
					}
					if (month <= 9) {
						mm = "0" + month;
					} else {
						mm = String.valueOf(month);
					}

				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return result;

	}

	/**
	 * 解析输出日期类型数据yyyy-MM-dd (如果传入的[日]部分的值超过当前月最大日期，则返回传入月份的最后一天的日期)
	 * 
	 * @param month 月份字符串 格式：yyyy-MM
	 * @param day   日
	 * @return DATE
	 */
	public static Date parseDateEnd(String month, String day) {
		Date dt = null;
		try {
			String date = month + "-" + day;
			// 判断设置的日期，不能超过最大日期
			int dayNum = getMonthDays(month);// 获取当前月最大天数
			if (Integer.parseInt(day) > dayNum) {
				date = month + "-" + dayNum;
			}
			dt = parseDate(date);
		} catch (Exception e) {
			return new Date();
		}
		return dt;
	}

	/**
	 * @category 根据时间戳得到localdatetime对象
	 * @param time
	 * @return
	 */
	public static LocalDateTime ofEpochMilli(long time) {
		return Instant.ofEpochMilli(time).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
	}

	public class MyDateTimeStruct {
		int y = 0;
		int m = 0;
		int d = 0;
		int h = 0;
		int mi = 0;
		int s = 0;
		int ms = 0;
		boolean notNull = true;
	}
}
