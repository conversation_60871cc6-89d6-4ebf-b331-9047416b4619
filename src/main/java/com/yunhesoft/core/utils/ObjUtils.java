package com.yunhesoft.core.utils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.SQLException;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.rowset.serial.SerialClob;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.DateTimeUtils;

import lombok.extern.log4j.Log4j2;

/**
 * 
 * @category 对象操作工具
 * <AUTHOR>
 * @since 2019年12月23日 下午7:49:01
 *
 */
@Log4j2
public class ObjUtils {

	/**
	 * list 数据复制
	 * 
	 * @param <T>
	 * @param clazz
	 * @param list
	 * @param isNullCopy  空值是否复制
	 * @param excludeList 排除字段
	 * @return
	 */
	public static <T> List<T> convertToList(Class<T> clazz, List<?> list, boolean isNullCopy,
			List<String> excludeList) {
		List<T> dess = new ArrayList<T>();
		ObjUtilsReflection srcRefl = null;
		ObjUtilsReflection desRefl = new ObjUtilsReflection(clazz);
		for (Object src : list) {
			if (src != null && srcRefl == null) {
				srcRefl = new ObjUtilsReflection(src.getClass());
				break;
			}
		}
		for (Object src : list) {
			try {
				T des = clazz.newInstance();
				copyTo(srcRefl, src, desRefl, des, isNullCopy, excludeList);
				dess.add(des);
			} catch (InstantiationException | IllegalAccessException e) {
				log.error("实例化类 {} 失败!", clazz.getName(), e);
				dess.add(null);
			}
		}
		return dess;

	}

	/**
	 * list 数据复制
	 * 
	 * @param <T>
	 * @param clazz
	 * @param list
	 * @param isNullCopy 空值是否复制
	 * @return
	 */
	public static <T> List<T> convertToList(Class<T> clazz, List<?> list, boolean isNullCopy) {
		return convertToList(clazz, list, isNullCopy, null);
	}

	public static <T> List<T> convertToList(Class<T> clazz, List<?> list) {
		return convertToList(clazz, list, false);
	}

	/**
	 * 对象复制（空值不覆盖）
	 * 
	 * @param <T>
	 * @param src
	 * @param clazz
	 * @return
	 */
	public static <T> T copyTo(Object src, Class<T> clazz) {
		return copyTo(src, clazz, false);
	}

	/**
	 * 对象复制（可排除某些属性不复制）
	 * 
	 * @param <T>
	 * @param src
	 * @param clazz
	 * @param excludeList 排除属性列表
	 * @return
	 */
	public static <T> T copyTo(Object src, Class<T> clazz, List<String> excludeList) {
		return copyTo(src, clazz, false, excludeList);
	}

	@SuppressWarnings("unchecked")
	public static <T> T copyTo(Object src, Class<T> clazz, boolean isNullCopy, List<String> excludeList) {
		T des = null;
		try {
			if (ObjUtilsReflection.isMapType(clazz)) {
				des = (T) new HashMap<Object, Object>();
			} else if (ObjUtilsReflection.isListType(clazz)) {
				des = (T) new ArrayList<Object>();
			} else {
				des = clazz.newInstance();
			}
			copyTo(src, des, isNullCopy, excludeList);
		} catch (InstantiationException | IllegalAccessException e) {
			log.error("实例化对象 {} 失败", clazz.getName(), e);
		}
		return des;
	}

	public static <T> T copyTo(Object src, Class<T> clazz, boolean isNullCopy) {
		return copyTo(src, clazz, isNullCopy, null);
	}

	/**
	 * 对象复制（空值不复制）
	 * 
	 * @param src 源对象
	 * @param des 目标对象
	 */
	public static void copyTo(Object src, Object des) {
		copyTo(src, des, false);
	}

	/**
	 * 对象复制
	 * 
	 * @param src        源对象
	 * @param des        目标对象
	 * @param isNullCopy 空值是否复制
	 */
	public static void copyTo(Object src, Object des, boolean isNullCopy) {
		copyTo(src, des, false, null);
	}

	/**
	 * 对象复制
	 * 
	 * @param src         源对象
	 * @param des         目标对象
	 * @param isNullCopy  空值是否复制
	 * @param excludeList 哪些属性不复制
	 */
	public static void copyTo(Object src, Object des, boolean isNullCopy, List<String> excludeList) {
		copyTo(new ObjUtilsReflection(src.getClass()), src, new ObjUtilsReflection(des.getClass()), des, isNullCopy,
				excludeList);
	}

	/**
	 * @category 对象复制到对象
	 * @param src         源对象实例
	 * @param des         目标对象实例
	 * @param isNullCopy  空值是否复制
	 * @param excludeList 排除字段
	 */
	private static void copyTo(ObjUtilsReflection srcRefl, Object src, ObjUtilsReflection desRefl, Object des,
			boolean isNullCopy, List<String> excludeList) {
		if (srcRefl.isMapType() && !desRefl.isMapType()) {
			// mapToObj
			mapToObj(src, desRefl, des, isNullCopy, excludeList);
		} else if (srcRefl.isMapType() && desRefl.isMapType()) {
			// mapToMap
			mapToMap(src, des, excludeList);
		} else if (!srcRefl.isMapType() && desRefl.isMapType()) {
			// objToMap
			objToMap(srcRefl, src, des, excludeList);
		} else {
			objToObj(srcRefl, src, desRefl, des, isNullCopy, excludeList);
		}
	}

	/**
	 * @category map对象复制到普通对象
	 * @param src
	 * @param desRefl
	 * @param des
	 * @param isNullCopy  空值是否复制
	 * @param excludeList 排除字段
	 */
	@SuppressWarnings("unchecked")
	private static void mapToObj(Object src, ObjUtilsReflection desRefl, Object des, boolean isNullCopy,
			List<String> excludeList) {
		Map<Object, Object> srcMap = (Map<Object, Object>) src;
		for (Object key : srcMap.keySet()) {
			String name = String.valueOf(key);
			Object value = srcMap.get(key);
			try {
				if (!isNullCopy && value == null) { // 空值不复制
					continue;
				}
				if (isExcludeField(name, excludeList)) {// 属性在排除列表里
					continue;
				}
				if (value != null) {// 准备存储的数据
					value = convertTo(value, desRefl.getField(name));
				}
				Method desSet = desRefl.getSetMethod(name);
				if (desSet != null) {
					desSet.invoke(des, value);
				} else {
					Field field = desRefl.getField(name);
					if (field != null) {
						field.setAccessible(true);
						field.set(des, value);
						field.setAccessible(false);
					}
				}
			} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
				log.error("map复制到对象失败", e);
			}
		}
	}

	/**
	 * @category 数值转换到字段属性对应的类型
	 * @param value
	 * @param field
	 * @return
	 */
	private static Object convertTo(Object value, Field field) {
		if (value == null || field == null) {
			return null;
		}
		Class<? extends Object> srcClazz = value.getClass();
		Class<?> desClazz = field.getType();
		if (srcClazz == desClazz) {
			// 源类与目标类相同
			return desClazz.cast(value);
		} else if (ObjUtilsReflection.isMapType(srcClazz)) {
			// map转map或map转对象
			return copyTo(value, desClazz);
		} else if (desClazz.isEnum()) {
			// 枚举对象转换
			return newEnum(desClazz, Integer.valueOf(String.valueOf(value)));
		} else if (ObjUtilsReflection.isListType(field.getType())) {
			// 如果字段是list，则需要转换到字段list的泛型
			List<?> srcValues = (List<?>) value;
			ObjUtilsReflection srcRefl = null;
			for (Object src : srcValues) {
				if (src != null && srcRefl == null) {
					srcRefl = new ObjUtilsReflection(src.getClass());
					break;
				}
			}
			Type clazz = ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
			ObjUtilsReflection desRefl = new ObjUtilsReflection((Class<?>) clazz);
			List<Object> desValues = new ArrayList<Object>();

			for (Object src : srcValues) {
				try {
					Object des = ((Class<?>) clazz).newInstance();
					if (srcRefl.isJavaClass() && desRefl.isJavaClass()) {
						des = desRefl.getClazz().cast(src);
					} else {
						copyTo(srcRefl, src, desRefl, des, false, null);
					}
					desValues.add(des);
				} catch (InstantiationException | IllegalAccessException e) {
					log.error("转list对象时错误:", e);
				}
			}
			return desValues;
		} else if (desClazz == String.class) {
			return String.valueOf(value);
		} else if (isJavaClass(desClazz)) {
			return string2class(String.valueOf(value), desClazz);
		} else {
			return value;
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private static void mapToMap(Object src, Object des, List<String> excludeList) {
		Map desMap = ((Map) des);
		desMap.putAll((Map) src);
		if (excludeList != null) {
			for (String key : excludeList) {
				desMap.remove(key);
			}
		}

	}

	/**
	 * 判断是否为排除字段
	 * 
	 * @param fieldName
	 * @param excludeList
	 * @return
	 */
	private static boolean isExcludeField(String fieldName, List<String> excludeList) {
		if (excludeList != null && excludeList.size() > 0) {
			if (excludeList.indexOf(fieldName) >= 0) {// 属性在排除列表里
				return true;
			}
			for (String name : excludeList) {
				if (name.equalsIgnoreCase(fieldName)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * @category 对象复制到对象
	 * 
	 * @param srcRefl    源对象反射对象
	 * @param src        源对象实例
	 * @param desRefl    目标对象反射对象
	 * @param des        目标对象实例
	 * @param isNullCopy 空值是否复制
	 * @throws IllegalAccessException
	 * @throws IllegalArgumentException
	 * @throws InvocationTargetException
	 */
	private static void objToObj(ObjUtilsReflection srcRefl, Object src, ObjUtilsReflection desRefl, Object des,
			boolean isNullCopy, List<String> excludeList) {
		for (String name : srcRefl.getFieldMap().keySet()) {
			Field srcField = srcRefl.getField(name);
			if (Modifier.isStatic(srcField.getModifiers()) || Modifier.isFinal(srcField.getModifiers())) {
				// 如果是静态类型或final类型，则不复制
				continue;
			}
			try {
				Object value = null;
				Method srcGet = srcRefl.getGetMethod(name);
				if (srcGet == null) {
					srcField.setAccessible(true);
					value = srcField.get(src);
					srcField.setAccessible(false);
				} else {
					value = srcGet.invoke(src);
				}
				if (!isNullCopy && value == null) { // 空值不复制
					continue;
				}
				if (isExcludeField(name, excludeList)) {// 属性在排除列表里
					continue;
				}
				if (value != null) {// 准备存储的数据
					value = convertTo(value, desRefl.getField(name));
				}
				Method desSet = desRefl.getSetMethod(name);
				if (desSet == null) {
					Field desField = desRefl.getField(name);
					if (desField != null) {
						desField.setAccessible(true);
						desField.set(des, value);
						desField.setAccessible(false);
					}
				} else {
					desSet.invoke(des, value);
				}

			} catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException e) {
				log.error("对象复制到另一个对象错误,字段 {}:", name, e);
			}
		}
	}

	/**
	 * @category 基本类型互转
	 * @param <T>
	 * @param value    原始对象
	 * @param desClazz 目标对象
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T convertTo(Object value, Class<T> desClazz) {
		// 判断基础类型,空值转默认值
		if (isJavaClass(desClazz) && value == null) {
			return null2class(desClazz);
		}

		if (value == null) {
			return null;
		}
		Class<? extends Object> srcClazz = value.getClass();
		if (srcClazz == desClazz) {// 源类与目标类类型相同
			return desClazz.cast(value);
		}
		if (ObjUtilsReflection.isMapType(srcClazz)) {// map转map或map转对象
			return copyTo(value, desClazz);
		}
		if (ObjUtilsReflection.isListType(srcClazz)) {// 如果是list，则涉及到如何转换到泛型对象
			return (T) value;
		}
		if (desClazz.isEnum()) {
			return (T) newEnum(desClazz, Integer.valueOf(String.valueOf(value)));
		}
		if (isJavaClass(srcClazz) && isJavaClass(desClazz)) {// 如果两个类型全是基本类型
			return string2class(String.valueOf(value), desClazz);
		}
		return (T) value;
	}

	@SuppressWarnings("unchecked")
	public static <T> T null2class(Class<T> clazz) {
		Object res = null;
		if (clazz == int.class) {
			res = 0;
		} else if (clazz == double.class) {
			res = 0.0;
		} else if (clazz == long.class) {
			res = 0L;
		} else if (clazz == short.class) {
			res = 0;
		} else if (clazz == float.class) {
			res = 0.0f;
		} else if (clazz == boolean.class) {
			res = false;
		}
		if (res != null) {
			return (T) res;
		} else {
			return null;
		}
	}

	/**
	 * @category 字符串转换到指定的java基本类型
	 * @param <T>
	 * @param value 源字符串
	 * @param clazz 指定的基本类型
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T string2class(String value, Class<T> clazz) {
		Object res = value;
		if (clazz == Integer.class || clazz == int.class) {
			if ("true".equalsIgnoreCase(String.valueOf(value).trim())) {
				res = 1;
			} else if ("false".equalsIgnoreCase(String.valueOf(value).trim())) {
				res = 0;
			} else {
				res = Integer.parseInt(value);
			}
		} else if (clazz == Double.class || clazz == double.class)

		{
			res = Double.parseDouble(value);
		} else if (clazz == Long.class || clazz == long.class) {
			res = Long.parseLong(value);
		} else if (clazz == Short.class || clazz == short.class) {
			res = Short.parseShort(value);
		} else if (clazz == Float.class || clazz == float.class) {
			res = Float.parseFloat(value);
		} else if (clazz == Boolean.class || clazz == boolean.class) {
			if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
				res = Boolean.parseBoolean(value);
			} else if (StringUtils.isNumeric(value)) {
				if (Integer.parseInt(value) > 0) {
					res = true;
				} else {
					res = false;
				}
			}
		} else if (clazz == Date.class || clazz == LocalDateTime.class || clazz == java.sql.Date.class
				|| clazz == LocalDate.class || clazz == LocalTime.class) {
			Date date = null;
			try {
				long time = Long.parseLong(value);// 转换成时间戳
				date = new Date();
				date.setTime(time);
			} catch (Exception e) {
				try {
					date = DateTimeUtils.parseDate(value);
				} catch (Exception e1) {
					log.error("解析日期时间字符串格式错误:{}", value, e1);
				}
			}
			if (date != null) {
				if (clazz == Date.class) {
					res = date;
				} else if (clazz == LocalDateTime.class) {
					res = DateTimeUtils.date2LocalDateTime(date);
				} else if (clazz == LocalDate.class) {
					res = DateTimeUtils.date2LocalDate(date);
				} else if (clazz == LocalTime.class) {
					res = DateTimeUtils.date2LocalTime(date);
				} else if (clazz == java.sql.Date.class) {
					res = (java.sql.Date) date;
				} else if (clazz == Time.class) {
					res = (java.sql.Time) date;
				} else {
					res = null;
				}
			} else {
				log.error("字符串转日期类型错误:{}", value);
				res = null;
			}
		} else if (clazz == Clob.class) {
			try {
				res = new SerialClob(value.toCharArray());
			} catch (SQLException e) {
				log.error("字符串转clob类型错误:{}", value);
			}
		} else if (clazz == Blob.class) {
			log.warn("字符串转blob类型不支持，请优化代码实现:{}", value);
		}
		return (T) res;
	}

	public static <T> Enum<?> newEnum(Class<T> clazz, int value) {
		Enum<?>[] enumConstants = (Enum[]) clazz.getEnumConstants();
		for (int i = 0; i < enumConstants.length; i++) {
			Enum<?> e = enumConstants[i];
			if (e.ordinal() == value) {
				return e;
			}
		}
		return null;
	}

	/**
	 * @category 复制实例对象到map对象
	 * @param srcRefl
	 * @param src
	 * @param des
	 */
	@SuppressWarnings("unchecked")
	private static void objToMap(ObjUtilsReflection srcRefl, Object src, Object des, List<String> excludeList) {
		Map<String, Object> desMap = (Map<String, Object>) des;
		for (String name : srcRefl.getFieldMap().keySet()) {
			try {
				Object value = null;
				if (isExcludeField(name, excludeList)) {// 属性在排除列表里
					continue;
				}
				Method srcGet = srcRefl.getGetMethod(name);
				if (srcGet == null) {
					value = srcRefl.getFieldMap().get(name).get(src);
				} else {
					value = srcGet.invoke(src);
				}
				desMap.put(srcRefl.getField(name).getName(), value);
			} catch (IllegalArgumentException | IllegalAccessException | InvocationTargetException e) {
				log.error("对象复制到map对象错误:", e);
			}
		}
	}

	/**
	 * @category 复制java的list对象列表k
	 * 
	 *           该函数为最早编写，命名等均不准确，与其它函数有重复，建议更换为copyToList方法
	 * 
	 * @param <E>
	 * @param <T>
	 * @param srcs
	 * @param clazz
	 * @return
	 */
	@Deprecated
	public static <E, T> List<T> toJavaList(List<E> srcs, Class<T> clazz) {
		return copyToList(srcs, clazz);
	}

	public static <E, T> List<T> copyToList(List<E> srcs, Class<T> clazz) {
		List<T> dess = new ArrayList<T>();
		ObjUtilsReflection desRefl = new ObjUtilsReflection(clazz);
		ObjUtilsReflection srcRefl = null;
		for (E src : srcs) {
			if (srcRefl == null && src != null) {
				srcRefl = new ObjUtilsReflection(src.getClass());
				break;
			}
		}
		for (E src : srcs) {
			T des = null;
			try {
				des = clazz.newInstance();
			} catch (InstantiationException | IllegalAccessException e) {
				log.error("实例化类 {} 失败", clazz.getName(), e);
			}
			copyTo(srcRefl, src, desRefl, des, false, null);
			dess.add(des);
		}
		return dess;
	}

	/**
	 * 复制一个对象的非空属性到另外一个对象
	 * 
	 * @param <T>
	 * @param target 目标对象
	 * @param in     复制值
	 * @return
	 */
	public static <T> T copyNonNullProperties(T target, T in) {
		if (in == null || target == null || target.getClass() != in.getClass()) {
			return target;
		}
		final BeanWrapper src = new BeanWrapperImpl(in);
		final BeanWrapper trg = new BeanWrapperImpl(target);
		for (final Field property : target.getClass().getDeclaredFields()) {
			if (property.getName().equalsIgnoreCase("serialVersionUID")) {
				continue;
			}
			Object providedObject = src.getPropertyValue(property.getName());
			if (providedObject != null && !(providedObject instanceof Collection<?>)) {
				trg.setPropertyValue(property.getName(), providedObject);
			}
		}
		return target;
	}

	/**
	 * map转换为对象
	 * 
	 * @param clazz
	 * @param map
	 * @return
	 */
	public static <T> T convertToObject(Class<T> clazz, Map<String, ?> map) {
		return convertToObject(clazz, map, false);
	}

	public static <T> T convertToObject(Class<T> clazz, Map<String, ?> map, boolean isNullCopy,
			List<String> excludeList) {
		T des = null;
		try {
			des = clazz.newInstance();
			mapToObj(map, new ObjUtilsReflection(clazz), des, isNullCopy, excludeList);
		} catch (InstantiationException | IllegalAccessException e) {
			log.error("实例化类 {} 失败", clazz.getName(), e);
		}
		return des;
	}

	public static <T> T convertToObject(Class<T> clazz, Map<String, ?> map, boolean isNullCopy) {
		return convertToObject(clazz, map, isNullCopy, null);
	}

	/**
	 * java 对象转换为map (****此函数不支持复制继承的父类属性，使用时需注意)
	 * 
	 * @param <T>
	 * @param bean java 对象
	 * @return
	 */
	@Deprecated
	public static <T> Map<String, Object> convertToMap(T bean) {
		if (bean == null) {
			return null;
		}
		Map<String, Object> map = new HashMap<String, Object>();
		final BeanWrapper trg = new BeanWrapperImpl(bean);
		for (final Field property : bean.getClass().getDeclaredFields()) {
			if (property.getName().equalsIgnoreCase("serialVersionUID")) {
				continue;
			}
			String fieldName = property.getName();
			try {
				Object value = trg.getPropertyValue(fieldName);
				map.put(fieldName, value);
			} catch (Exception e) {
				// log.warn("对象转换警告", e);
			}

		}
		return map;
	}

	public static String camel2line(String source) {
		return ObjUtilsReflection.camel2line(source);
	}

	public static List<Field> reflectFieldList(Class<?> clazz) {
		return ObjUtilsReflection.reflectFieldList(clazz);
	}

	/**
	 * @category 判断是否是java类型
	 * @param clz
	 * @return
	 */
	public static boolean isJavaClass(Class<?> clz) {
		return (clz != null && clz.getClassLoader() == null) || (clz == Date.class || clz == Time.class
				|| clz == LocalDate.class || clz == LocalTime.class || clz == LocalDateTime.class
				|| clz == java.sql.Date.class || clz == java.sql.Time.class || clz == Clob.class || clz == Blob.class);
	}

	/**
	 * @category 对象空判断
	 * 
	 *           <pre>
	 *           1.字符串为空字符串返回真
	 *           2.集合长度为0时返回真
	 *           </pre>
	 * 
	 * @param obj
	 * @return
	 */
	public static boolean isEmpty(Object obj) {
		if (obj == null || "".equals(obj.toString().trim())) {
			return true;
		}

		if (obj.getClass().isArray()) {
			return Array.getLength(obj) == 0;
		}
		if (obj instanceof Collection) {
			return ((Collection<?>) obj).isEmpty();
		}
		if (obj instanceof Map) {
			return ((Map<?, ?>) obj).isEmpty();
		}
		// else
		return false;
	}

	public static boolean notEmpty(Object obj) {
		return !isEmpty(obj);
	}

	/**
	 * @category 返回混合型的类的字段映射(自然，驼峰，下划线，大写，小写)
	 * @param clazz
	 * @return
	 */
	public static Map<String, Field> reflectFieldMix(Class<?> clazz) {
		return ObjUtilsReflection.reflectFieldMix(clazz, true);
	}

	/**
	 * redis list 对象转换
	 * 
	 * @param clazz
	 * @param list
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<Object> redisConvertToListObj(Class<?> clazz, List<?> list) {
		List<Object> newList = new ArrayList<>();
		for (Object object : list) {
			Map<String, Object> map = (Map<String, Object>) object;
			newList.add(redisMapToClass(clazz, map));
		}
		return newList;
	}

	/**
	 * map redis map 转换为对象(不推荐使用) ，推荐使用 convertToObject
	 * 
	 * @param clazz
	 * @param map
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@Deprecated
	public static Object redisMapToClass(Class<?> clazz, Map<String, ?> map) {
		Object obj = null;
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
			obj = clazz.newInstance();
			for (PropertyDescriptor property : propertyDescriptors) {
				try {
					Method setter = property.getWriteMethod();
					if (setter != null) {
						Object val = map.get(property.getName());
						if (val != null) {
							String valueType = val.getClass().getName();// 值类型
							if ("java.util.ArrayList".equalsIgnoreCase(valueType)) {// list
								try {
									String typeName = setter.getGenericParameterTypes()[0].getTypeName();
									String className = typeName.substring(typeName.indexOf("<") + 1,
											typeName.indexOf(">"));
									if (className.startsWith("java.lang.")) {// list里是基本类型
										setter.invoke(obj, val);
									} else {// java对象
										Class<?> cls = Class.forName(className);
										List<Object> subList = redisConvertToListObj(cls, (List<Map<String, ?>>) val);
										setter.invoke(obj, subList);
									}
								} catch (Exception e) {
									setter.invoke(obj, val);
									log.error("ArrayList对象转换错误", e);
								}
							} else if ("java.util.LinkedHashMap".equalsIgnoreCase(valueType)) {// java对象
								try {
									String className = setter.getGenericParameterTypes()[0].getTypeName();
									if (className.indexOf(",") > 0) {// 真正的map
										setter.invoke(obj, val);
									} else {// java对象
										Class<?> cls = Class.forName(className);
										Object subObj = redisMapToClass(cls, map);
										setter.invoke(obj, subObj);
									}
								} catch (Exception e) {
									setter.invoke(obj, val);
									log.error("ArrayList对象转换错误", e);
								}
							} else {// 基本类型
								Class<?> p[] = setter.getParameterTypes();
								if (p != null && p.length > 0) {
									String paramType = p[0].getName();
									if ("java.util.Date".equalsIgnoreCase(paramType) && (val instanceof Long)) {// 日期型需要特殊处理
										Date d = new Date();
										d.setTime(Long.parseLong(val.toString()));
										setter.invoke(obj, d);
									} else if ("java.lang.Long".equalsIgnoreCase(paramType)
											&& (val instanceof Integer)) {
										Long v = Long.parseLong(String.valueOf(val));
										setter.invoke(obj, v);
									} else {
										setter.invoke(obj, val);
									}
								}
							}
						}
					}

				} catch (Exception ex) {
					log.error("", ex);
				}
			}
		} catch (Exception e) {
			log.info("ObjUtils.convertToObject 对象转换失败！");
			log.error("", e);
		}
		return obj;

	}

	/**
	 * 复制一个map到一个对象
	 * 
	 * @param <T>
	 * @param target
	 * @param map
	 * @return
	 */
	public static <T> T copy(T target, Map<String, Object> map) {
		if (map == null || target == null) {
			return target;
		}
		final BeanWrapper trg = new BeanWrapperImpl(target);
		for (final Field property : target.getClass().getDeclaredFields()) {
			if (property.getName().equalsIgnoreCase("serialVersionUID")) {
				continue;
			}
			String fieldName = property.getName();
			if (map.containsKey(fieldName)) {
				Object value = map.get(fieldName);
				trg.setPropertyValue(fieldName, value);
			}
		}
		return target;
	}

	/**
	 * 复制一个JSONObj到一个对象
	 * 
	 * @param <T>
	 * @param target
	 * @param map
	 * @return
	 */
	public static <T> T copy(T target, JSONObject jsonObj) {
		if (jsonObj == null || target == null) {
			return target;
		}
		final BeanWrapper trg = new BeanWrapperImpl(target);
		for (final Field property : target.getClass().getDeclaredFields()) {
			if (property.getName().equalsIgnoreCase("serialVersionUID")) {
				continue;
			}
			String fieldName = property.getName();
			if (jsonObj.containsKey(fieldName)) {
				Object value = jsonObj.get(fieldName);
				trg.setPropertyValue(fieldName, value);
			}
		}
		return target;
	}

}
