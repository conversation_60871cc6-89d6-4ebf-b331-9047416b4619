package com.yunhesoft.core.utils;

import java.nio.charset.StandardCharsets;

import org.springframework.core.env.Environment;

import com.yunhesoft.core.utils.spring.SpringUtils;

/**
 * 读取配置文件
 * 
 */
public class PropertyUtils {

	/**
	 * 获取配置文件内容
	 * 
	 * @param key
	 * @return
	 */
	public static String getProperty(String key) {
		return getUtf8Property(key);
	}

	private static String getUtf8Property(String key) {
		String value = SpringUtils.getBean(Environment.class).getProperty(key);
		return StringUtils.isEmpty(value) ? "" : iso88591ToUtf8(value);
	}

	private static String iso88591ToUtf8(String src) {
		return new String(src.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
	}

}