package com.yunhesoft.job;

import com.alibaba.fastjson.JSONObject;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.joblist.service.impl.JobGeneraterServiceImpl;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 * @Description: 任务生成调度
 * @date 2024/7/22
 */
@Component
public class GeneraterJobHandler {
    static private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @XxlJob("jobListGeneraterJobHandler")
    public void jobListGeneraterJobHandler() {
        //获取执行器参数
        String param = XxlJobHelper.getJobParam();
        JobGeneraterServiceImpl bean = SpringUtils.getBean(JobGeneraterServiceImpl.class);
        JSONObject jsonObject = bean.activityGeneraterMain(null);
        XxlJobHelper.handleSuccess("执行成功，结果[" + String.valueOf(jsonObject) + "],完成时间["
                + sdf.format(new Date()) + "]");
    }
}
