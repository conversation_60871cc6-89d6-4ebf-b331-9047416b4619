package com.yunhesoft.joblist.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.entity.dto.JobAllocationDto;
import com.yunhesoft.joblist.entity.dto.JoblistGenDto;
import com.yunhesoft.joblist.service.impl.JobGeneraterServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/joblist/taskGenerater")
@Api(tags = "标准工作库维护测试")
public class JoblistTaskGeneraterController {
    @Autowired
    private JobGeneraterServiceImpl jobGeneraterService;

    @RequestMapping(value = "/getPersonTaskNumCurrentUserOrg", method = RequestMethod.POST)
    @ApiOperation("当前用户任务数量")
    public Res<?> getPersonTaskNumCurrentUserOrg(@RequestBody JoblistGenDto joblistGenDto) {
        return Res.OK(jobGeneraterService.getPersonTaskNumCurrentUserOrg(joblistGenDto));
    }

    @RequestMapping(value = "/activityAllocation", method = RequestMethod.POST)
    @ApiOperation("当前用户任务数量")
    public Res<?> getPersonTaskNumCurrentUserOrg(@RequestBody JobAllocationDto allocationDto) {
        return Res.OK(jobGeneraterService.activityAllocation(allocationDto));
    }

    @RequestMapping(value = "/activityGeneraterMain", method = RequestMethod.POST)
    @ApiOperation("任务生成")
    public Res<?> activityGeneraterMain() {
        return Res.OK(jobGeneraterService.activityGeneraterMain(null));
    }

}