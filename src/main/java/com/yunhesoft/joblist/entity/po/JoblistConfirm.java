package com.yunhesoft.joblist.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "活动任务确认")
@Data
@Entity
@Table(name = "JOBLIST_CONFIRM")
public class JoblistConfirm  extends BaseEntity {
    //活动实例id
    @Column(name="ACTIVITY_INSTANCE_ID", length=100)
    private String activityInstanceId;

    @Column(name="ACTIVITY_PROPERTIES_ID", length=100)
    private String activityPropertiesId;

    @Column(name="ACTIVITY_NAME", length=100)
    private String activityName;

    //0未开始 1 进行中 2已完成
    @Column(name="ACTIVITY_STATUS", length=100)
    private Integer activityStatus;

    @Column(name="ACTIVITY_FEED_CONTENT", length=100)
    private String activityFeedContent;
    //是否获得分数
    @Column(name="IS_GET_SCORE", length=100)
    private Integer isGetScore;

    @Column(name="CONFIRM_PERSON", length=100)
    private String confirmPerson;

    @Column(name="CONFIRM_PERSON_ID", length=100)
    private String confirmPersonId;

    //更新方式
    @Column(name="UPDATA_TYPE", length=100)
    private Integer updateType;
}
