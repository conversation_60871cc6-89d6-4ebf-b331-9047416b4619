package com.yunhesoft.joblist.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	专业表
 */
@Entity
@Setter
@Getter
@Table(name = "JOBLIST_EXTPROPERTIESCONFIG")
public class JoblistExtPropertiesConfig extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 父专业ID */
    @Column(name="PID", length=100)
    private String pid; //根分类ID是root
    
    /** 机构ID */
    @Column(name="ORGID", length=100)
    private String orgid; //专业所属的机构ID，system是租户内通用的默认分类 ，如果机构有自己的专业就用自己的
    
    /** 类型 */
    @Column(name="PTYPE")
    private Integer ptype; //0 专业，1 优先级，2频次，3 规程
    
    /** 名称 */
    @Column(name="PNAME", length=200)
    private String pname;
    
    /** 描述 */
    @Column(name="MEMO", length=2000)
    private String memo;
    
    /** 频次类型 */
    @Column(name="FREQUENCYTYPE")
    private Integer frequencytype; //0 单次 1 重复
    
    /** 1、使用；0、不使用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
}
