package com.yunhesoft.joblist.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案考核方法表
 */
@Entity
@Setter
@Getter
@Table(name = "JOBLIST_PROGRAM_ASSMETHOD")
public class JoblistProgramAssMethod extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 父ID */
    @Column(name="PID", length=100)
    private String pid; //方案id
    
    /** 考核类型 */
    @Column(name="ASSESSTYPE")
    private Integer assessType; //0、岗位；
    
    /** 考核ID */
    @Column(name="ASSESSID", length=100)
    private String assessId; //岗位ID
    
    /** 考核名称 */
    @Column(name="ASSESSNAME", length=200)
    private String assessName; //岗位名称
    
    /** 考核权重 */
    @Column(name="ASSESSWEIGHT")
    private Double assessWeight;
    
    /** 考核分值 */
    @Column(name="ASSESSSCORE")
    private Double assessScore;
    
    /** 1、使用；0、不使用 */
    @Column(name="TMUSED")
    private Integer tmused;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
}
