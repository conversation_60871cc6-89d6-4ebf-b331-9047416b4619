package com.yunhesoft.joblist.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("推导周期频次时返回的对象")
@Data
public class JobFrequencyCalcVo {

	@ApiModelProperty("频次编号：具体时间的返回时间，其余返回从1开始的数值")
	private String frequencyNo;

	@ApiModelProperty("频次的开始时间")
	private String beginTime;

	@ApiModelProperty("频次的结束时间")
	private String endTime;

	@ApiModelProperty("是按照班组单独推导频次时是true，否则是false,大于班次的周期，在不指定到具体时间时都是true")
	private boolean isEachTeam;

}