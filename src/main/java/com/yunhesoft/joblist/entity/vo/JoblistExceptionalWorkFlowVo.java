package com.yunhesoft.joblist.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel("例外工作流程对象")
@Data
public class JoblistExceptionalWorkFlowVo {
    @ApiModelProperty("流程 ID")
    private String flowId;

    @ApiModelProperty("流程名称")
    private String flowName;

    //private Integer template;
    //private String verTime;
    //private Integer callbackMode;
    //private Integer extBound;
    //private Integer publishMode;
    //private String themeId;
    //private Integer assess;
    //private Integer addMode;
    //private String resolveBound;
    //private Integer tmapply;
    //private Date applyTime;
    //private Date lastUpdateTime;
    //private Date stopTime;
    //private String stopUserid;
    //private String memo;
    //private String createOrgCode;
    //private String basicScore;
    //private String basicBonus;
    //private Integer tmsort;
    //private Integer tmused;
    //private String indexBound;
    //private String periodType;
    //private String themeName;
    //private String stepInfo;
    //private Integer useFeedWeek;
    //private String id;
    //private Date createTime;
    //private String createBy;
    //private Date updateTime;
    //private String updateBy;
    //private String createByOrg;
    //private String createByPost;
}
