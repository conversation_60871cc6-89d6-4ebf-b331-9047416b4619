package com.yunhesoft.joblist.entity.vo;

import com.yunhesoft.task.flow.entity.vo.TmtaskPropVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ApiModel("例外工作清单")
@Data
public class JoblistExceptionalWorkVo {
    /*@ApiModelProperty("工作ID")
    private String workId;

    @ApiModelProperty("工作名称")
    private String workName;

    @ApiModelProperty("工作描述")
    private String workDesc;

    @ApiModelProperty("工作来源类型，无、内置功能、外部数据")
    private String sourceType;

    @ApiModelProperty("工作来源模块，电子台账、核算报表、采集点录入、……")
    private String sourceModule;

    @ApiModelProperty("工作完成状态，0=未开始，1=进行中，2=已完成")
    private String workStatus;

    @ApiModelProperty("需要完成的例外工作数量")
    private Integer requiredWorkCount;

    @ApiModelProperty("已完成的例外工作数量")
    private Integer completedWorkCount;*/

    private String id;
    private String flowId;
    private Integer orgAccMark;
    private String title;
    private String content;
    private Date startTime;
    private Date endTime;
    private String startTimeStr;
    private String endTimeStr;
    private String extInfo1;
    private String extInfo2;
    private Integer stepNum;
    private Integer isCreate;
    private Integer opMark;
    private String opName;
    private Boolean canOpAcc;
    private String opId;
    private List<TmtaskPropVo> propList;
}
