package com.yunhesoft.joblist.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class JoblistSummaryDetailVo {
    @ApiModelProperty(value = "记录类型", example = "0=无，1=内置功能")
    private String recordType;
    @ApiModelProperty(value = "记录子类型", example = "内置功能：2=行云流表单")
    private String recordId;

    @ApiModelProperty("表单数据")
    private List<JoblistSummaryFormVo> formData;
    @ApiModelProperty("常规活动明细表格数据")
    private List<JoblistSummaryActivityVo> tableData;

    //表单附加参数 ----------------------------------
    private String activityDate;
    private String activityId;
    private String shiftId;
    private String sbsj;
    private String xbsj;
    private String orgcode;
    private String pOrgcode;
    private String exampleId;
    private String postId;
    private String postName;
    private String tbrq;

}
