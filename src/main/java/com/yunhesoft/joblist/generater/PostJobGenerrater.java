package com.yunhesoft.joblist.generater;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.po.*;
import com.yunhesoft.joblist.entity.vo.JoblistActivityExampleVo;
import com.yunhesoft.joblist.entity.vo.SysEmployeeInfoVo;
import com.yunhesoft.system.employee.entity.dto.EmpOrgPostParamDto;
import com.yunhesoft.system.employee.entity.po.SysEmployeeInfo;
import com.yunhesoft.system.employee.entity.po.SysEmployeeOrgPost;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.ISysEmployeeInfoService;
import com.yunhesoft.system.employee.service.ISysEmployeeOrgPostService;
import com.yunhesoft.system.org.entity.po.SysOrg;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class PostJobGenerrater extends AbstractJobGenerater {
    /**
     * 根据活动id生成活动实例
     *
     * @param activityIds
     * @param activityProperties
     * @return
     * <AUTHOR>
     * @date 2024/6/25
     * @params activityIds
     * {
     * "activityId":
     * activityStart: 2024-06-25
     * activityEnd: 2024-06-25
     * frequency: [
     * "beginDate": "2024-06-25 12:00:00",
     * "endDate": "2024-06-25 12:00:00"
     * ]
     * }
     */

    @Override
    public List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityIds, List<JoblistActivityProperties> activityProperties) {
        return this.generateActivityInstance(activityIds, true, activityProperties);
    }

    @Override
    public List<JoblistActivityExampleVo> generateActivityInstance(JSONObject activityConfigs, Boolean useRecord, List<JoblistActivityProperties> activityProperties) {
        List<JoblistActivityExampleVo> activityExampleVos = new ArrayList<>();
        List<String> activityIdList = new ArrayList<>();
        //校验数据有效性
        if (StringUtils.isEmpty(activityConfigs)) {
            return activityExampleVos;
        }
        //根据活动生成配置生成活动
        if (StringUtils.isEmpty(activityProperties)) {
            return activityExampleVos;
        }
        //获取实例最大排序
        int max = getExampleMaxSort();
        //岗位活动 获取岗位活动的人员列表
        List<String> idList = activityProperties.stream().map(JoblistActivityProperties::getId).collect(Collectors.toList());
        //获取绑定表单信息
        List<JoblistPersonBind> formBindList = jobGeneraterService.getActivityBind(idList, 4);
        Map<String, List<JoblistPersonBind>> formBindMapByActivityId = new HashMap<>();
        if (StringUtils.isNotEmpty(formBindList)) {
            formBindMapByActivityId = formBindList.stream().collect(Collectors.groupingBy(JoblistPersonBind::getPid));
        }
        //根据活动获取活动岗位人员组
        List<JoblistPersonBind> bindList = jobGeneraterService.getActivityBind(idList, 1);
        Map<String, List<SysEmployeeInfoVo>> bindPersonMap = getPostPersonsGroupByActivity(bindList, orgPostService, employeeInfoService);
        List<JoblistActivityExample> saveList = new ArrayList<>();
        List<JobListExampleDutyPerson> saveDutyPersonList = new ArrayList<>();
        List<JoblistExampleFormBind> saveBindList = new ArrayList<>();
        //活动分数情况
        // 根据活动属性进行生成活动实例
        for (JoblistActivityProperties joblistActivityProperties : activityProperties) {
            //通过活动配置生成活动负责人列表
            List<JobListExampleDutyPerson> dutyPersonList = new ArrayList<>();
            //通过活动配置生成活动负责人列表
            getDutyPersonListByActivity(joblistActivityProperties, bindPersonMap, dutyPersonList);
            //取得当前活动配置的 生成配置信息
            if(!activityConfigs.containsKey(joblistActivityProperties.getId())){
                continue;
            }
            JSONObject activityConfig = activityConfigs.getJSONObject(joblistActivityProperties.getId());
            //取出生成配置 中的班次信息
            JSONArray shiftList = activityConfig.getJSONArray("shiftList");
            //获取可以执行的频次配置
            JSONArray configList;
            if (useRecord) {
                //校验此班次配置的活动是否可以进行生成
                configList = getCanExecConfig(joblistActivityProperties, shiftList);
            } else {
                // 不校验 暂时只有插入才用
                configList = shiftList;
            }
            if (configList == null) continue;
            max = getActivityExampleSaveList(joblistActivityProperties, max, saveList, configList, dutyPersonList, saveDutyPersonList, formBindMapByActivityId, saveBindList);
            if (max == -9999) {
                return activityExampleVos;
            }
        }
        if (StringUtils.isNotEmpty(saveList)) {
            finishSave(activityConfigs, saveList, saveDutyPersonList, activityExampleVos, saveBindList, useRecord);
        }
        //生成的活动实例
        return activityExampleVos;
    }

    /**
     * 通过活动配置生成活动负责人列表
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public void getDutyPersonListByActivity(JoblistActivityProperties joblistActivityProperties, Map<String, List<SysEmployeeInfoVo>> bindPersonMap, List<JobListExampleDutyPerson> dutyPersonList) {
        List<SysEmployeeInfoVo> postPersonList = bindPersonMap.get(joblistActivityProperties.getId());
        if (StringUtils.isNotEmpty(postPersonList)) {
            for (SysEmployeeInfoVo sysEmployeeInfo : postPersonList) {
                JobListExampleDutyPerson person = new JobListExampleDutyPerson();
                String scoreKey = joblistActivityProperties.getId() + "_" + sysEmployeeInfo.getPostId();
                person.setOrgCode(sysEmployeeInfo.getOrgId());
                person.setPostId(sysEmployeeInfo.getPostId());
                person.setPersonId(sysEmployeeInfo.getId());
                person.setPersonName(sysEmployeeInfo.getEmpname());
                dutyPersonList.add(person);
            }
        }
    }

    /**
     * 根据活动获取活动岗位人员组
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    public Map<String, List<SysEmployeeInfoVo>> getPostPersonsGroupByActivity(List<JoblistPersonBind> bindList, ISysEmployeeOrgPostService orgPostService, ISysEmployeeInfoService employeeInfoService) {
        Map<String, List<SysEmployeeInfoVo>> bindPersonMap = new HashMap<>();
        if (StringUtils.isNotEmpty(bindList)) {
            //岗位活动列表
            EmpOrgPostParamDto paramDto = new EmpOrgPostParamDto();
            for (JoblistPersonBind joblistPersonBind : bindList) {
                String bindid = joblistPersonBind.getBindid();
                String orgid = joblistPersonBind.getOrgid();
                //获取全部子机构
                List<SysOrg> orgList = orgService.getOrgList(orgid);
                List<String> orgScope = Optional.ofNullable(orgList).orElse(Collections.emptyList()).stream().map(SysOrg::getId).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(bindid)) {
                    //获取人员
                    paramDto.setPostid(bindid);
                    //根据岗位id获取岗位正式人员
                    List<SysEmployeeOrgPost> employeeOrgPost = orgPostService.getEmployeeOrgPost(paramDto);
                    //根据岗位  获取外委人员
                    List<EmployeeVo> partTimePost = employeeInfoService.getPartTimePost(null, bindid, false);
                    if(StringUtils.isNotEmpty(partTimePost)){
                        for (EmployeeVo employeeVo : partTimePost) {
                            SysEmployeeOrgPost bean = new SysEmployeeOrgPost();
                            bean.setPostid(employeeVo.getPostid());
                            bean.setOrgcode(employeeVo.getOrgcode());
                            bean.setEmpid(employeeVo.getEmpTmuid());
                            employeeOrgPost.add(bean);
                        }
                    }
                    if (StringUtils.isNotEmpty(employeeOrgPost)) {
                        //将列表去重
                        employeeOrgPost = employeeOrgPost
                                .stream()
                                .collect(Collectors.collectingAndThen(
                                                Collectors.toCollection(
                                                        () ->
                                                                new TreeSet<>(
                                                                        (v1, v2) -> {
                                                                            if (Objects.equals(v1.getEmpid(),
                                                                                    v2.getEmpid())
                                                                                    && Objects.equals(v1.getPostid(),
                                                                                    v2.getPostid())
                                                                                    && Objects.equals(v1.getOrgcode(),
                                                                                    v2.getOrgcode())
                                                                            ){
                                                                                return 0;
                                                                            }else {
                                                                                return -1;
                                                                            }
                                                                        }
                                                                )
                                                )
                                        , ArrayList::new
                                        )
                                );
                        List<SysEmployeeInfoVo> postPersonList = new ArrayList<>();
                        for (SysEmployeeOrgPost sysEmployeeOrgPost : employeeOrgPost) {
                            //机构必须是作业单元机构下的  （装置机构下）
                            //当前岗位关系不在作业单元机构下  跳过
                            if(!orgScope.contains(sysEmployeeOrgPost.getOrgcode())) continue;
                            SysEmployeeInfo person = employeeInfoService.findEmployeeById(sysEmployeeOrgPost.getEmpid());
                            SysEmployeeInfoVo personVo = ObjUtils.copyTo(person, SysEmployeeInfoVo.class);
                            personVo.setPostId(sysEmployeeOrgPost.getPostid());
                            personVo.setOrgId(sysEmployeeOrgPost.getOrgcode());
                            postPersonList.add(personVo);
                        }
                        //活动下的所有人   活动配置的所有人
                        if (bindPersonMap.containsKey(joblistPersonBind.getPid())) {
                            List<SysEmployeeInfoVo> sysEmployeeInfoVos = bindPersonMap.get(joblistPersonBind.getPid());
                            for (SysEmployeeInfoVo sysEmployeeInfoVo : postPersonList) {
                                long count =
                                        sysEmployeeInfoVos.stream().filter(
                                                i ->
                                                        Objects.equals(
                                                                i.getId()+"_"+i.getOrgId()+"_"+i.getPostId()
                                                                ,
                                                            sysEmployeeInfoVo.getId()+"_"+sysEmployeeInfoVo.getOrgId()+"_"+sysEmployeeInfoVo.getPostId())).count();
                                if (count == 0) {
                                    sysEmployeeInfoVos.add(sysEmployeeInfoVo);
                                }
                            }
                            bindPersonMap.put(joblistPersonBind.getPid(), sysEmployeeInfoVos);
                        } else {
                            bindPersonMap.put(joblistPersonBind.getPid(), postPersonList);
                        }
                    }

                }
            }
        }
        return bindPersonMap;
    }


    /**
     * 给任务实例分配人员
     *
     * @return
     * <AUTHOR>
     * @date 2024/7/4
     * @params
     */
    @Override
    public void allocationPersonToActivityExample(List<JobListExampleDutyPerson> dutyPersonList, List<JobListExampleDutyPerson> saveDutyPersonList, JoblistActivityExample activityExample, String orgcode) {
        //对于岗位活动 如果对应没有可以分配的人员 应该 放弃生成此活动
        if(StringUtils.isEmpty(dutyPersonList)){
            //为空  跳过此记录的生成
            activityExample.setSkipInsert(true);
            return;
        }
        //对任务任务分配人员
        //是否已经分配了人员
        Boolean isAllocation = false;
        for (JobListExampleDutyPerson person : dutyPersonList) {
            if(StringUtils.isNotEmpty(orgcode)){
                if(!person.getOrgCode().equals(orgcode)){
                    //不是本班组的
                    continue;
                }
            }
            JobListExampleDutyPerson person1 = ObjUtils.copyTo(person, JobListExampleDutyPerson.class);
            person1.setOrgCode(activityExample.getOrgCode());
            person1.setShiftCode(activityExample.getShiftClassCode());
            person1.setTbrq(activityExample.getTbrq());
            person1.setRowFlag(0);
            person1.setActivityExampleId(activityExample.getId());
            person1.setActivityPropertiesId(activityExample.getActivityId());
            person1.setPersonType(0);
            isAllocation = true;
            saveDutyPersonList.add(person1);
        }
        if(isAllocation){
            //已分配人员  此实例有效 需要生成
            activityExample.setSkipInsert(false);
        }else{
            //未分配人员无效不需要生成
            activityExample.setSkipInsert(true);
        }
    }

}
