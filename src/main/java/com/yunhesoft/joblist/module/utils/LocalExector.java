package com.yunhesoft.joblist.module.utils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.joblist.entity.dto.JobFinishDto;
import com.yunhesoft.joblist.entity.dto.JobListModuleConfigDto;
import com.yunhesoft.joblist.entity.dto.JoblistInputDto;
import com.yunhesoft.system.kernel.config.SysUserHolder;

import lombok.extern.log4j.Log4j2;


@Log4j2
public class LocalExector {
    public static void exec(JoblistInputDto param, JobListModuleConfigDto module, String url, ExecutorService executor, List<JobFinishDto> finishCaseResult, CountDownLatch latch) {
        Method getFinishCase = null;
        Class<?> aClass = null;
        FindClassAndExec classInfo = getFindClassAndExec(url,"getFinishCase");
        if (classInfo.method == null || classInfo.aClass == null) {
            //未找到方法
            return;
        }
        Method finalGetFinishCase = classInfo.method;
        Class<?> finalAClass = classInfo.aClass;
        SysUser currentUser = SysUserHolder.getCurrentUser();
        executor.execute(() -> {
            //防止线程没有当前登录用户
            SysUserHolder.setCurrentUser(currentUser);
            JobFinishDto result = new JobFinishDto();
            try {
                result = (JobFinishDto) finalGetFinishCase.invoke(finalAClass.newInstance(), param);
                result.setName(module.getName());
                result.setAlias(module.getAlias());
                result.setIconSrc(module.getIconSrc());
            } catch (IllegalAccessException | InvocationTargetException | InstantiationException e) {
                result.setName(module.getName());
                result.setAlias(module.getAlias());
                result.setTotal(0);
                result.setFinished(0);
                result.setUnfinish(0);
                result.setIconSrc(module.getIconSrc());
                log.error(e);
            } finally {
            	finishCaseResult.add(result);//防止计算之前，防止还没放进返回值，线程就停了，导致数据丢失
                //每执行一次计数器都会-1
                latch.countDown();
                //防止 threadLocal
                SysUserHolder.remove();
            }
          //  finishCaseResult.add(result);
          //  System.out.println(module.getName()+":"+result.getTotal()+":"+result.getFinished());
        });
    }

    public static FindClassAndExec getFindClassAndExec(String url,String methodName) {
        Method getFinishCase;
        Class<?> aClass;
        try {
            aClass = Class.forName(url);
            getFinishCase = aClass.getMethod(methodName, JoblistInputDto.class);
        } catch (ClassNotFoundException | NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
        FindClassAndExec result = new FindClassAndExec(getFinishCase, aClass);
        return result;
    }

    public static class FindClassAndExec {
        public final Method method;
        public final Class<?> aClass;

        public FindClassAndExec(Method getFinishCase, Class<?> aClass) {
            this.method = getFinishCase;
            this.aClass = aClass;
        }
    }
}
