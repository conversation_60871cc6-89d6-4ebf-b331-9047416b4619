package com.yunhesoft.joblist.operCard.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.joblist.operCard.entity.dto.OpercardInfoVersionDao;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfo;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInfoVersion;
import com.yunhesoft.joblist.operCard.entity.po.OpercardOperstep;
import com.yunhesoft.joblist.operCard.service.IParseWordInfoVersionService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/operCard/operCardVersion")
@Api(tags = "操作卡 - 操作卡接口")
public class ParseWordInfoVersionController extends BaseRestController {

    @Autowired
    private IParseWordInfoVersionService parseWordInfoVersionService;

    @ApiOperation("操作卡发布按钮 - 页面发布按钮，发布操作卡")
    @RequestMapping(value = "/publishOperCardVsersion", method = {RequestMethod.POST})
    public Res publishOperCardVsersion(@RequestBody OpercardInfo opercardInfo) {
        return Res.OK(parseWordInfoVersionService.publishOperCardVsersion(opercardInfo));
    }


    @ApiOperation("操作卡查询历史版本")
    @RequestMapping(value = "/queryOperCardVsersion", method = {RequestMethod.POST})
    public Res queryOperCardVsersion(@RequestBody OpercardInfoVersionDao parm) {
        Pagination<?> page = null;//前台做的分页，不需要后台分页
//        if (ObjUtils.notEmpty(parm.getPageSize()) && parm.getPageSize() > 0) {// 创建分页信息
//            page = Pagination.create(ObjUtils.isEmpty(parm.getPageNum()) ? 1 : parm.getPageNum(), parm.getPageSize());
//        }
        List<OpercardInfoVersion> result = parseWordInfoVersionService.queryOperCardVsersion(parm,page);
//		int total = 0;
//		if(page!=null) {
//			total=Long.valueOf(page.getTotal()).intValue();
//		}
//		return Res.OK(result).setTotal(total);
        return Res.OK(result);
    }

    @ApiOperation("操作卡查询版本内容的修改")
    @RequestMapping(value = "/checkVsersionUpdate", method = {RequestMethod.POST})
    public Res checkVsersionUpdate(@RequestBody OpercardInfoVersion opercardInfoVersion) {
        return Res.OK();
    }


    @ApiOperation("操作卡取消发布")
    @RequestMapping(value = "/cancelVsersion", method = {RequestMethod.POST})
    public Res cancelVsersion(@RequestBody OpercardInfo opercardInfo) {
        return Res.OK(parseWordInfoVersionService.cancelVsersion(opercardInfo));
    }

    @ApiOperation("操作卡预览返回数据 - 预览返回数据")
    @RequestMapping(value = "/previewOperCard", method = {RequestMethod.POST})
    public Res previewOperCard(@RequestBody OpercardOperstep opercardOperstep) {
        return Res.OK(parseWordInfoVersionService.previewOperCard(opercardOperstep));
    }

    @ApiOperation("操作卡发布按钮 - 预览节点返回数据")
    @RequestMapping(value = "/previewOperCardNode", method = {RequestMethod.POST})
    public Res previewOperCardNode(@RequestBody OpercardOperstep opercardOperstep) {
        return Res.OK(parseWordInfoVersionService.previewOperCardNode(opercardOperstep));
    }





}
