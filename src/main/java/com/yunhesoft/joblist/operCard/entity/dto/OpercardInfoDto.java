package com.yunhesoft.joblist.operCard.entity.dto;


import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.core.common.entity.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 操作卡信息表
 * 
 */
@Data
public class OpercardInfoDto  extends BaseQueryDto {
	
    private static final long serialVersionUID = 1L;


    /** 分类id */
    private String classid;
    
    /** 操作卡名称 */
    private String name;
    
    /** 机构编码 */
    private String orgCode;
    
    /** 是否使用 */
    //1：使用；0：不使用
    private Integer tmUsed;
    
    /** 排序 */
    private Integer tmSort;
    
    /** 注释 */
    private String memo;
    
    /** 操作卡编号 */
    private String cardno;
    
    /** 操作卡级别 */
    //数据字典：company:公司级；department:部门级;shiftteam:班组级
    private String cardLevel;
    
    /** 状态 */
    //1：已发布；0：未发布；2:审核中
    private Integer cardStatus;
    
    /** 审核状态 */
    //1：审核通过；0:未提交；2：审核中；-1：否决
    private Integer auditStatus;
    
    /** 是否为使用中 */
    private Integer cardUsing;
    
    /** 操作卡最新版本 */
    private String cardVer;
    
    /** 发布时间 */
    private Date publishDt;
    
    /** 发布人 */
    private String publishPerson;
    
    /** 发布人id */
    private String publishId;
    
    /** 操作卡当前生效版本ID */
    private String currentVerId;
    
}
