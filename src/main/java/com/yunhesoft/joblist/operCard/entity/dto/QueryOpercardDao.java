package com.yunhesoft.joblist.operCard.entity.dto;

import com.yunhesoft.joblist.operCard.entity.po.OpercardCatalog;
import com.yunhesoft.joblist.operCard.entity.po.OpercardInstrument;
import com.yunhesoft.joblist.operCard.entity.po.OpercardOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardSteppost;
import lombok.Data;

import java.util.List;

/**
 * @Author: gzz
 * @Date: 2025/4/1 09:13
 **/
@Data
public class QueryOpercardDao {


    private List<OpercardCatalog> opercardCatalogList;

    private List<OpercardOperstep> opercardOperstepList;

    private List<OpercardInstrument> opercardInstrumentList;

    private List<OpercardSteppost> opercardSteppostList ;


}
