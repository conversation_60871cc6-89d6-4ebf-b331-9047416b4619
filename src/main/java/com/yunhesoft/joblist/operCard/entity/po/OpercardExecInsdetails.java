package com.yunhesoft.joblist.operCard.entity.po;


import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 操作卡执行_仪表数据_仪表值
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "OPERCARD_EXEC_INSDETAILS")
public class OpercardExecInsdetails extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 实例ID */
    @Column(name="EXECID", length=50)
    private String execId;
    
    /** 步骤ID */
    @Column(name="STEPID", length=50)
    private String stepId;
    
    /** 执行实例步骤ID */
    @Column(name="EXECSTEPID", length=50)
    private String execStepId;
    
    /** 仪表ID */
    @Column(name="INSTRUMENTID", length=50)
    private String instrumentId;
    
    /** 仪表名称 */
    @Column(name="TAGNAME", length=255)
    private String tagName;
    
    /** 仪表位号 */
    @Column(name="TAGCODE", length=255)
    private String tagCode;
    
    /** 上限值 */
    @Column(name="UPLIMIT")
    private Double upLimit;
    
    /** 下限值 */
    @Column(name="LOWLIMIT")
    private Double lowLimit;
    
    /** 仪表值 */
    @Column(name="TAGVALUE", length=4000)
    private String tagValue;
    
    /** 仪表状态 */
    @Column(name="TAGSTATUS")//1：超限；0:正常
    private Integer tagStatus;
    
    /** 录入方式 */
    @Column(name="INPUTTYPE")//1自动采集 2手动录入
    private Integer inputType;
    
    /** 录入时间 */
    @Column(name="INPUTDT")
    private Date inputDt;
    
    /** 录入人 */
    @Column(name="INPUTPERSON", length=200)
    private String inputPerson;
    
    /** 录入人id */
    @Column(name="INPUTPERSONID", length=50)
    private String inputPersonId;
    
}
