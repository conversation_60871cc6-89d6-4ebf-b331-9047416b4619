package com.yunhesoft.joblist.operCard.entity.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OperCardExecInstrumentVo {

    @ApiModelProperty(value = "执行实例ID")
    private String execId;
    
	@ApiModelProperty(value = "执行实例步骤ID")
	private String execStepId;

    @ApiModelProperty(value = "操作卡模型步骤ID")
    private String stepId;
    
    @ApiModelProperty(value = "仪表ID")
    private String instrumentId;

    @ApiModelProperty(value = "注释")
    private String memo;

    @ApiModelProperty(value = "仪表名称")
    private String tagName;

    @ApiModelProperty(value = "仪表位号")
    private String tagCode;
    
    @ApiModelProperty(value = "上限值")
    private Double upLimit;
    
    @ApiModelProperty(value = "下限值")
    private Double lowLimit;
    
    @ApiModelProperty(value = "控件类型")//raw：实时仪表；number：数字框；combo:下拉框；img:图片；tip：风险提示  textfield：文本框
    private String comType;
    
    @ApiModelProperty(value = "排序")
    private Integer tmSort;
    
    @ApiModelProperty(value = "扩展内容")
    private String propertyCode;
    
    @ApiModelProperty(value = "仪表值")
    private String tagValue;
    
    @ApiModelProperty(value = "仪表状态")//1：超限；0:正常
    private Integer tagStatus;
    
    @ApiModelProperty(value = "录入方式")//1自动采集 2手动录入
    private Integer inputType;
    
    @ApiModelProperty(value = "录入时间")
    private String inputDt;
    
    @ApiModelProperty(value = "录入人")
    private String inputPerson;
    
    @ApiModelProperty(value = "录入人id ")
    private String inputPersonId;

}
