package com.yunhesoft.joblist.operCard.entity.vo;

import java.util.List;

import com.yunhesoft.joblist.operCard.entity.po.OpercardExec;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInsdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecInstrument;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecJump;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecOperstep;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepdetails;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecSteppost;
import com.yunhesoft.joblist.operCard.entity.po.OpercardExecStepread;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OperCardInstanceVo {

    @ApiModelProperty(value = "主信息")
    private OpercardExec info;
    
    @ApiModelProperty(value = "步骤信息")
    private List<OpercardExecOperstep> opStep;
    
    @ApiModelProperty(value = "步骤岗位信息")
    private List<OpercardExecSteppost> opPost;
    
    @ApiModelProperty(value = "步骤岗位操作信息")
    private List<OpercardExecStepdetails> opStepdetails;
    
    @ApiModelProperty(value = "步骤仪表信息")
    private List<OpercardExecInstrument> opInstrument;

    @ApiModelProperty(value = "步骤仪表值信息")
    private List<OpercardExecInsdetails> opInstrumentdetails;
    
    @ApiModelProperty(value = "阅读人员信息")
    private List<OpercardExecStepread> opRead;
    
    @ApiModelProperty(value = "跳步申请信息")
    private List<OpercardExecJump> opJump;
    
}
