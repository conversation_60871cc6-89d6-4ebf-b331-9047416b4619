package com.yunhesoft.joblist.operCard.entity.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 操作卡信息表
 * 
 */
@Data
public class OperWordRowVo {
	
    @ApiModelProperty(value = "操作标识 完整的标识 [P] [I] (M)")
    private String opSign;
   
    @ApiModelProperty(value = "操作标识类型 完整的标识 []:操作 ():确认 <>:安全")
    private String opSignType;
    
    @ApiModelProperty(value = "操作标识岗位 I:内操 P:外操 M班长")
    private String opSignPost;
    
    @ApiModelProperty(value = "步骤类型 1：文本；2：警示；3：操作步骤")
    private Integer opType;

    @ApiModelProperty(value = "操作内容（文本）")
    private String opText;
    
    @ApiModelProperty(value = "操作内容（html）")
    private String opHtml;
    
}
