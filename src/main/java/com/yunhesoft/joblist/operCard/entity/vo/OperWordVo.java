package com.yunhesoft.joblist.operCard.entity.vo;


import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.core.common.model.Res;
import lombok.Data;

/**
 * 操作卡信息表
 * 
 */
@Data
public class OperWordVo {

    public static OperWordVo geStart(int start,int stop, Boolean isContain) {
        OperWordVo operWordVo = new OperWordVo();
        operWordVo.setStart(start);
        operWordVo.setStop(stop);
        operWordVo.setIsContain(isContain);
        return operWordVo;
    }

    public static OperWordVo getStop(int stop, Boolean isContain) {
        OperWordVo operWordVo = new OperWordVo();
        operWordVo.setStop(stop);
        operWordVo.setIsContain(isContain);
        return operWordVo;
    }
	

    //段落开始
    private int start;
    
    //段落结果
    private int stop;

    //是否包含特殊标签
    private Boolean isContain;
    

    
}
