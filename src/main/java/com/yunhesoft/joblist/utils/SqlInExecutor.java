package com.yunhesoft.joblist.utils;


import com.yunhesoft.core.common.utils.EntityUtils;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description: 本类提供静态方法用于提升数据库性能
 * tableName  表名
 * keyAlial in条件字段
 * keyList in条件字段值
 * otherWhereStr 其他条件  需要使用占位符 不允许直接拼接
 * orderString 排序语句
 * cls 结果集实体类
 * paramsList 其他列表
 * <AUTHOR>
 * @date 2025/5/17
 */
public class SqlInExecutor {
    public static <T> List<T> inSql(String tableName,String keyAlias,List<String> keyList,
                                    String otherWhereStr,String orderString,Class<T> cls,List<Object> paramsList){
        StringBuffer buffer = new StringBuffer();
        buffer.append("select * from ");
        buffer.append(tableName);
        buffer.append(" A where exists (");
        buffer.append("select 1 from (values");
        int index = 0;
        for (int i = 0; i < keyList.size(); i++) {
            String propertyId = keyList.get(i);
            buffer.append("('");
            buffer.append(propertyId);
            buffer.append("')");
            if(i != keyList.size() - 1){
                //不是最后一个都要拼接逗号
                buffer.append(",");
            }
        }
        buffer.append(") as ids(");
        buffer.append(keyAlias);
        buffer.append(") where A.");
        buffer.append(keyAlias);
        buffer.append("=ids.");
        buffer.append(keyAlias);
        buffer.append(" )");
        if(StringUtils.isNotEmpty(otherWhereStr)){
            buffer.append(" and ");
            buffer.append(otherWhereStr);
        }
        if(StringUtils.isNotEmpty(orderString)){
            buffer.append(" ");
            buffer.append(orderString);
        }
        EntityService dao = SpringUtils.getBean(EntityService.class);
        if(StringUtils.isEmpty(paramsList)){
            paramsList = Collections.emptyList();
        }
        SqlRowSet rs = dao.rawQuery(buffer.toString(), paramsList.toArray());
        if(rs!=null){
            return EntityUtils.convertTo(rs, cls);
        }
        return Collections.emptyList();
    }
}
