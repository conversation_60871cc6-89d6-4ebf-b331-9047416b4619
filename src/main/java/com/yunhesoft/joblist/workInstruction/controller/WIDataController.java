package com.yunhesoft.joblist.workInstruction.controller;


import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.joblist.entity.vo.JoblistActivityPropertiesVo;
import com.yunhesoft.joblist.workInstruction.entity.dto.*;
import com.yunhesoft.joblist.workInstruction.entity.po.WIData;
import com.yunhesoft.joblist.workInstruction.entity.po.WIDataFile;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIDataVo;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIFeedbackItemVo;
import com.yunhesoft.joblist.workInstruction.service.IWIDataFeedbackService;
import com.yunhesoft.joblist.workInstruction.service.IWIDataService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.tools.dict.entity.SysDictData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/joblist/wi/data")
@Api(tags = "工作指令数据接口")
public class WIDataController extends BaseRestController {

    @Autowired
    private IWIDataService srv;

    @Autowired
    private IWIDataFeedbackService feedbackSrv;

    @ApiOperation("查询指令库列表")
    @RequestMapping(value = "/queryLibDataList", method = {RequestMethod.POST})
    public Res<?> queryLibDataList(@RequestBody WIDataQueryDto dto) {
        Pagination<?> page = null;
        if (dto.getPageSize() > 0) {
            page = Pagination.create(dto.getPageNum(), dto.getPageSize());
        }
        Res res = Res.OK(srv.queryLibDataList(dto, page));
        if (page != null) {
            res.setTotal(page.getTotal());
        }
        return res;
    }

    @ApiOperation("查询指令数据列表")
    @RequestMapping(value = "/queryDataList", method = {RequestMethod.POST})
    public Res<?> queryDataList(@RequestBody WIDataQueryDto dto) {
        return Res.OK(srv.queryDataList(dto));
    }

    @ApiOperation("查询指令数据列表")
    @RequestMapping(value = "/queryDataTypeList", method = {RequestMethod.POST})
    public Res<?> queryDataTypeList(@RequestBody(required = false) WIDataDto dto) {
        List<SysDictData> result = new ArrayList<>();
        SysDictData bean1 = new SysDictData();
        bean1.setDictLabel("生产指令");
        bean1.setDictValue("prod");
        result.add(bean1);
        SysDictData bean2 = new SysDictData();
        bean2.setDictLabel("操作指令");
        bean2.setDictValue("oper");
        result.add(bean2);
        return Res.OK(result);
    }

    @ApiOperation("查询指令数据")
    @RequestMapping(value = "/queryData/{id}", method = {RequestMethod.GET})
    public Res<?> queryDataById(@PathVariable String id) {
        return Res.OK(srv.queryDataById(id));
    }

    @ApiOperation("查询指令数据")
    @RequestMapping(value = "/queryDataInfo", method = {RequestMethod.POST})
    public Res<?> queryDataInfo(@RequestBody  WIDataDto dto) {
        return Res.OK(srv.queryData(dto));
    }

//    @ApiOperation("保存数据")
//    @RequestMapping(value = "/saveData", method = {RequestMethod.POST})
//    public Res<?> saveData(@RequestBody WIData dto) {
//        return Res.OK(srv.saveData(dto, null));
//    }

    @ApiOperation("保存数据")
    @RequestMapping(value = "/saveData", method = {RequestMethod.POST})
    public Res<?> saveDataInfo(@RequestParam("uploadFiles") MultipartFile[] uploadFiles, @RequestParam("data") String data, @RequestParam(value = "mobile",required = false) String mobile, HttpServletRequest request) {
        if ("true".equals(mobile)) {
            uploadFiles = this.getUploadFiles(uploadFiles, request);
        }
        WIDataDto dto = null;
        try {
            dto = JSONObject.parseObject(data).toJavaObject(WIDataDto.class);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        String msg = srv.saveData(dto, uploadFiles);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("指令删除")
    @RequestMapping(value = "/dataDelete", method = {RequestMethod.POST})
    public Res<?> dataDelete(@RequestBody WIData data) {
        String msg = srv.dataDelete(data);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("指令撤回")
    @RequestMapping(value = "/dataWithdraw", method = {RequestMethod.POST})
    public Res<?> dataWithdraw(@RequestBody WIDataDto data) {
        String msg = srv.dataWithdraw(data);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("指令终止")
    @RequestMapping(value = "/dataStop", method = {RequestMethod.POST})
    public Res<?> dataStop(@RequestBody WIDataDto data) {
        String msg = srv.dataStop(data);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("指令变更")
    @RequestMapping(value = "/dataChange", method = {RequestMethod.POST})
    public Res<?> dataChange(@RequestBody WIDataDto data) {
        String msg = srv.changeData(data);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    private MultipartFile[] getUploadFiles(MultipartFile[] uploadFiles, HttpServletRequest request) {
        CommonsMultipartResolver commonsMultipartResolver =new CommonsMultipartResolver(request.getSession().getServletContext());
        commonsMultipartResolver.setDefaultEncoding("utf-8");
        if (commonsMultipartResolver.isMultipart(request)){
            MultipartHttpServletRequest mulReq = (MultipartHttpServletRequest) request;
            Map<String,MultipartFile> map = mulReq.getFileMap();
            List<MultipartFile> resultList = new ArrayList<>();
            if (!map.isEmpty()){
                for(Map.Entry<String,MultipartFile> result : map.entrySet()){
                    if(!result.getValue().getContentType().equals("text/html")){
                        resultList.add(result.getValue());
                    }

                }
                uploadFiles = resultList.toArray(new MultipartFile[0]);
            }
        }
        return uploadFiles;
    }

    @ApiOperation("下达提交")
    @RequestMapping(value = "/startProcess", method = {RequestMethod.POST})
    public Res<?> startProcess(@RequestParam("uploadFiles") MultipartFile[] uploadFiles, @RequestParam("data") String data, @RequestParam(value = "mobile",required = false) String mobile, HttpServletRequest request) {
        if ("true".equals(mobile)) {
            uploadFiles = this.getUploadFiles(uploadFiles, request);
        }
        WIDataDto dto = null;
        try {
            dto = JSONObject.parseObject(data).toJavaObject(WIDataDto.class);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        String msg = srv.startProcess(uploadFiles, dto);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("接收或拒收指令")
    @RequestMapping(value = "/handleStep", method = {RequestMethod.POST})
    public Res<?> handleStep(@RequestParam("uploadFiles") MultipartFile[] uploadFiles, @RequestParam("data") String data, @RequestParam(value = "mobile",required = false) String mobile, HttpServletRequest request) {
        if ("true".equals(mobile)) {
            uploadFiles = this.getUploadFiles(uploadFiles, request);
        }
        WIDataDto dto = null;
        try {
            dto = JSONObject.parseObject(data).toJavaObject(WIDataDto.class);
        } catch (Exception e) {
//            e.printStackTrace();
        }
//        return Res.OK(srv.handleStep(uploadFiles, dto));
        String msg = srv.handleStep(uploadFiles, dto);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("结转")
    @RequestMapping(value = "/dataTransfer", method = {RequestMethod.POST})
    public Res<?> dataTransfer(@RequestBody WIDataDto data) {
        String msg = srv.dataTransfer(data);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation("判断是否能添加记录")
    @RequestMapping(value = "/canAddData", method = {RequestMethod.GET})
    public Res<?> canAddData() {
        return Res.OK(srv.canAddData());
    }
    @ApiOperation("获取能下达的流程")
    @RequestMapping(value = "/queryCanAddProcessList", method = {RequestMethod.GET})
    public Res<?> queryCanAddProcessList() {
        return Res.OK(srv.queryCanAddProcessList());
    }

//    @ApiOperation(value = "附件上传")
//    @RequestMapping(value = "/filesUpload", method = { RequestMethod.POST })
//    public Res<?> fileUpload(@RequestParam("files") MultipartFile[] files, @RequestParam("dataId") String dataId) {
//        String msg = srv.fileUpload(files, dataId, null);
//        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
//    }

    @ApiOperation(value = "删除附件")
    @RequestMapping(value = "/deleteUploadFile", method = { RequestMethod.POST })
    public Res<?> deleteUploadFile(@RequestBody WIDataFile file) {
        String msg = srv.deleteUploadFile(file);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

//    @ApiOperation(value = "查询流程信息")
//    @RequestMapping(value = "/queryProcessInfo", method = { RequestMethod.POST })
//    public Res<?> queryProcessInfo(@RequestBody WIDataDto dto) {
//        return Res.OK(srv.queryProcessInfo(dto));
//    }

    @ApiOperation(value = "反馈")
    @RequestMapping(value = "/feedback", method = { RequestMethod.POST })
    public Res<?> feedback(@RequestParam("uploadFiles") MultipartFile[] uploadFiles, @RequestParam("data") String data, @RequestParam(value = "mobile",required = false) String mobile, HttpServletRequest request) {
        if ("true".equals(mobile)) {
            uploadFiles = this.getUploadFiles(uploadFiles, request);
        }
        WIDataFeedbackDto dto = null;
        try {
            dto = JSONObject.parseObject(data).toJavaObject(WIDataFeedbackDto.class);
        } catch (Exception e) {
//            e.printStackTrace();
        }
        String msg = feedbackSrv.feedback(uploadFiles, dto);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation(value = "查询汇总反馈列表")
    @RequestMapping(value = "/queryFeedbackItemList", method = { RequestMethod.POST })
    public Res<?> queryFeedbackItemList(@RequestBody WIDataFeedbackQueryDto dto) {
        return Res.OK(feedbackSrv.queryFeedbackItemList(dto));
    }
    @ApiOperation(value = "查询当前步骤的反馈信息")
    @RequestMapping(value = "/queryCurrentStepFeedbackInfo", method = { RequestMethod.POST })
    public Res<?> queryCurrentStepFeedbackInfo(@RequestBody WIDataFeedbackDto dto) {
        return Res.OK(feedbackSrv.queryCurrentStepFeedbackInfo(dto));
    }
    @ApiOperation(value = "查询机构反馈信息")
    @RequestMapping(value = "/queryOrgFeedbackInfo", method = { RequestMethod.POST })
    public Res<?> queryOrgFeedbackInfo(@RequestBody WIFeedbackItemVo dto) {
        return Res.OK(feedbackSrv.queryOrgFeedbackInfo(dto));
    }
    @ApiOperation(value = "修改反馈列表内容")
    @RequestMapping(value = "/modifyFeedbackContent", method = { RequestMethod.POST })
    public Res<?> modifyFeedbackContent(@RequestBody List<WIFeedbackItemVo> list) {
        String msg = feedbackSrv.modifyFeedbackContent(list);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation(value = "汇总反馈提交")
    @RequestMapping(value = "/submitFeedbackItemList", method = { RequestMethod.POST })
    public Res<?> submitFeedbackItemList(@RequestBody WIDataFeedbackSubmitDto dto) {
        String msg = feedbackSrv.submitFeedbackItemList(dto);
        return StringUtils.isNotEmpty(msg) ? Res.FAIL(msg) : Res.OK();
    }

    @ApiOperation(value = "查询当前节点抄送人")
    @RequestMapping(value = "/queryStepCarbonCopy", method = { RequestMethod.POST })
    public Res<?> queryStepCarbonCopy(@RequestBody WIDataDto dto) {
        return Res.OK(srv.queryStepCarbonCopy(dto));
    }

    @ApiOperation(value = "生产新的指令编号")
    @RequestMapping(value = "/generateNewDataNo", method = { RequestMethod.POST })
    public Res<?> generateNewDataNo(@RequestBody WIDataQueryDto dto) {
        return Res.OK(srv.generateNewDataNo(dto));
    }

    @ApiOperation(value = "查询操作卡默认填写人")
    @RequestMapping(value = "/queryOperCardDefaultInputUser", method = { RequestMethod.POST })
    public Res<?> queryOperCardDefaultInputUser(@RequestBody WIDataDto dto) {
        return Res.OK(srv.queryOperCardDefaultInputUser(dto));
    }


}
