package com.yunhesoft.joblist.workInstruction.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@ApiModel(value = "流程数据实例")
@Getter
@Setter
@Entity
@Table(name = "WI_PROCESS_DATA")
public class WIProcessData extends BaseEntity {

	@ApiModelProperty(value = "数据id")
	@Column(name = "DATA_ID", length = 50)
	private String dataId;

	@ApiModelProperty(value = "流程设置id")
	@Column(name = "PROCESS_ID", length = 50)
	private String processId;

	@ApiModelProperty(value = "流程名称")
	@Column(name = "PROCESS_NAME", length = 500)
	private String processName;

	@ApiModelProperty(value = "状态 1待审核 2待接收 3执行中 10已完成 -1审核否决 -2拒绝接收 -3撤回 -4终止")
	@Column(name = "PROCESS_STATUS")
	private int processStatus;

	@ApiModelProperty(value = "是否删除 1使用 0删除")
	@Column(name = "TMUSED")
	private int tmused;

}
