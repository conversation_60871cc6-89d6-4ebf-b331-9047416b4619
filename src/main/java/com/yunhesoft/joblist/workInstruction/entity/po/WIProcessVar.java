package com.yunhesoft.joblist.workInstruction.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@ApiModel(value = "流程变量")
@Getter
@Setter
@Entity
@Table(name = "WI_PROCESS_VAR", uniqueConstraints = {@UniqueConstraint(columnNames = {"VAR_NAME"})})
public class WIProcessVar extends BaseEntity {


	@ApiModelProperty(value = "变量名称")
	@Column(name = "VAR_NAME", length = 100)
	private String varName;

	@ApiModelProperty(value = "排序")
	@Column(name = "TMSORT")
	private int tmsort;

}
