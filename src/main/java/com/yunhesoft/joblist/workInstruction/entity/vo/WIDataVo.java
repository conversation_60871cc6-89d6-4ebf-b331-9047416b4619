package com.yunhesoft.joblist.workInstruction.entity.vo;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.yunhesoft.joblist.workInstruction.entity.po.WIData;
import com.yunhesoft.joblist.workInstruction.entity.po.WIDataFile;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessStep;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import java.util.List;

@ApiModel(value = "流程设置")
@Getter
@Setter
public class WIDataVo extends WIData {

	@ApiModelProperty(value = "ID")
	private String id;

	@ApiModelProperty(value = "指令编号")
	private String dataNo;

	@ApiModelProperty(value = "指令名称")
	private String dataName;

	@ApiModelProperty(value = "指令内容")
	private String dataContent;

	@ApiModelProperty(value = "类型 prod=生产指令，oper=操作指令")
	private String dataType;

	@ApiModelProperty(value = "开始时间")
	private String startDt;

	@ApiModelProperty(value = "结束时间")
	private String endDt;

	@ApiModelProperty(value = "操作卡名称")
	private String cardName;

	@ApiModelProperty(value = "操作卡id")
	private String cardId;

	@ApiModelProperty(value = "操作卡目录id")
	private String catalogAlias;

	@ApiModelProperty(value = "操作卡执行方式 1同步执行 2并行执行")
	private int cardExecType;

	@ApiModelProperty(value = "执行机构代码")
	private List<String> acceptOrgCodeArray;
	@ApiModelProperty(value = "执行机构代码")
	private String acceptOrgCode;
	@ApiModelProperty(value = "执行机构名称")
	private String acceptOrgName;


	@ApiModelProperty(value = "记录状态 1待审核 2待接收 3执行中 4待反馈 10已完成")
	private int dataStatus;

	@ApiModelProperty(value = "是否抄送 0否 1是")
	private int carbonCopy;
	@ApiModelProperty(value = "抄送对象编码")
	private String carbonCopyCode;
	@ApiModelProperty(value = "抄送对象名")
	private String carbonCopyText;

	@ApiModelProperty(value = "当前节点id")
	private String dataStepId;

	@ApiModelProperty(value = "审核节点id")
	private String auditStepId;

	@ApiModelProperty(value = "填写步骤节点id")
	private String inputStepId;
	@ApiModelProperty(value = "操作卡实例id")
	private String cardInstanceId;
	@ApiModelProperty(value = "反馈信息id")
	private String feedbackId;

	@ApiModelProperty(value = "可接收指令")
	private boolean canAccept;
	@ApiModelProperty(value = "修改接收结果")
	private boolean acceptModify;
	@ApiModelProperty(value = "可审核")
	private boolean canAudit;
	@ApiModelProperty(value = "可撤回")
	private boolean canWithdraw;
	@ApiModelProperty(value = "可终止")
	private boolean canStop;
	@ApiModelProperty(value = "可重新开始")
	private boolean canRestart;
	@ApiModelProperty(value = "可修改指令（未提交时修改）")
	private boolean canModify;
	@ApiModelProperty(value = "可删除指令（未提交时修改）")
	private boolean canDelete;
	@ApiModelProperty(value = "可变更指令（提交后只能变更指令内容）")
	private boolean canChange;
	@ApiModelProperty(value = "可反馈")
	private boolean canFeedback;
	@ApiModelProperty(value = "可填写反馈")
	private boolean canInputFeedback;
	@ApiModelProperty(value = "查询按钮")
	private boolean queryBtnShow;
	@ApiModelProperty(value = "可填卡")
	private boolean canInputCard;
	@ApiModelProperty(value = "可结转")
	private boolean canTransfer;

	@ApiModelProperty(value = "反馈信息")
	private WIDataFeedbackInfo feedbackInfo;

	@ApiModelProperty(value = "节点操作类型 0提交 1审核 2接收 3填写 4反馈 5结转接收 6结转填写")
	private int stepHandle;

	@ApiModelProperty(value = "重新获取抄送人")
	private boolean reloadCc;

	@ApiModelProperty(value = "当前节点所属机构")
	private String dataStepOrgCode;

	@ApiModelProperty(value = "当前人所在班组")
	private String currentUserOrgCode;

	@ApiModelProperty(value = "填写人")
	private String inputUserId;
	@ApiModelProperty(value = "填写人")
	private String inputUserName;
	@ApiModelProperty(value = "审核人id")
	private String auditUserId;
	@ApiModelProperty(value = "审核人姓名")
	private String auditUserName;

//	@ApiModelProperty(value = "选择执行人")
//	private boolean selectAcceptUser;
//	@ApiModelProperty(value = "选择执行单位")
//	private boolean selectAcceptOrg;
//	@ApiModelProperty(value = "选择操作卡")
//	private boolean selectCard = true;

	private JSONArray orgOpCardArray;

	private List<WIProcessStep> stepList;
	private List<WIDataFile> fileList;

	private JSONObject itemConfig;

	//时间线
	private JSONArray dataTimeline;


	@ApiModelProperty(value = "操作 1通过 -1否决")
	private int handleResult;
	@ApiModelProperty(value = "操作内容")
	private String handleMessage;
	
}
