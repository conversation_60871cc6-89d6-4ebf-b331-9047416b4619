package com.yunhesoft.joblist.workInstruction.service;


import com.yunhesoft.joblist.workInstruction.entity.dto.WIProcessVarDto;
import com.yunhesoft.joblist.workInstruction.entity.po.WIProcessVar;
import com.yunhesoft.joblist.workInstruction.entity.vo.WIProcessVarConfigVo;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.org.entity.po.SysOrg;

import java.util.List;
import java.util.Set;


public interface IWIProcessVarService {

	/**
	 * 查询变量机构列表
	 * @return
	 */
	List<SysOrg> queryProcessVarOrgList();

	/**
	 * 查询变量列表
	 * @return
	 */
	List<WIProcessVar> queryProcessVarList();


	/**
	 * 查询变量设置
	 * @param dto
	 * @return
	 */
	WIProcessVarConfigVo queryProcessVarSetting(WIProcessVarDto dto);

	/**
	 * 保存变量设置
	 * @param dto
	 * @return
	 */
	String saveProcessVarSetting(WIProcessVarDto dto);


	/**
	 * 根据变量获取对应的人员id
	 * @param varName
	 * @param currentOrgCode
	 * @return
	 */
	Set<String> getUserIdSetByVar (String varName, String currentOrgCode);

	List<EmployeeVo> getUserByVar (String varName, String currentOrgCode);

	Set<String> getCurrentUserVars ();
}
