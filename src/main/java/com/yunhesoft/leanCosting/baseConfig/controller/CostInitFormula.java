package com.yunhesoft.leanCosting.baseConfig.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.unitConf.service.ICostFormulaService;
import com.yunhesoft.leanCosting.unitConf.service.ICostuintService;
import com.yunhesoft.system.kernel.controller.BaseRestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/baseConfig/costInitFormula")
@Api(tags = "补充公式")
public class CostInitFormula extends BaseRestController {

	@Autowired
	private ICostFormulaService costFormulaService;
	
	@Autowired
	private ICostuintService costuintService;
	
	@RequestMapping(value = "/initCostFormulaService", method = RequestMethod.POST)
	@ApiOperation("补充公式")
	public Res<?> initCostFormulaService() {
		Res<String> res = new Res<String>();
		res.setResult(costFormulaService.initCostFormulaService());
		return res;
	}
	
}
