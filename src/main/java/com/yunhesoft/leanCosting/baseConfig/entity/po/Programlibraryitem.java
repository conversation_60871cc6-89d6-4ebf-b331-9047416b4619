package com.yunhesoft.leanCosting.baseConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("方案库项目")
@Table(name = "PROGRAMLIBRARYITEM")
public class Programlibraryitem  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="父分类ID", example="8a1244a772565c34")
    @Column(name = "PID",length=100)
    private String pid;
    
    @ApiModelProperty(value="方案名称", example="方案名称1")
    @Column(name = "PINAME",length=200)
    private String piname;
    
    @ApiModelProperty(value="注释", example="注释1")
    @Column(name = "MEMO",length=1000)
    private String memo;
    
    @ApiModelProperty(value="排序号", example="排序号1")
    @Column(name = "ORDERNO",length=1000)
    private String orderno;
    
    @ApiModelProperty(value="设备类型", example="设备类型1")
    @Column(name = "DEVICETYPE",length=100)
    private String devicetype;
    
    @ApiModelProperty(value="设备状态", example="设备状态1")
    @Column(name = "DEVICESTATUS",length=100)
    private String devicestatus;
    
    @ApiModelProperty(value="无用", example="无用1")
    @Column(name = "USELESS")
    private String useless;
    

}