package com.yunhesoft.leanCosting.costReport.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutBzyhzdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutFsdExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CostOutRbExcelDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.costReport.service.ICostExcelService;
import com.yunhesoft.rtdb.core.utils.Res;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@Api(tags = "核算报表导出")
@RequestMapping("/exportExcel")
public class CostOutRbCxExcelConreoller {

	@Autowired
	private ICostExcelService costExcelService;
	@Autowired
	private HttpServletResponse response;

	@ApiOperation(value = "导出文件-班组日报", notes = "导出文件-班组日报")
	@RequestMapping(value = "/reportRbCx", method = { RequestMethod.POST })
	public void reportRbCx(@RequestBody CostOutRbExcelDto dto) {
		costExcelService.exportRbCxExcel(dto, response);
	}

	@ApiOperation(value = "导出文件-班组月报", notes = "导出文件-班组月报")
	@RequestMapping(value = "/costMonthReportExcel", method = { RequestMethod.POST })
	public void costMonthReportExcel(@RequestBody CostOutBzyhzdExcelDto param) {
		costExcelService.costMonthReportExcel(param, response);
	}

	@ApiOperation(value = "导出文件-装置月汇总报表", notes = "导出文件-班组月汇总报表")
	@RequestMapping(value = "/costReportExcel", method = { RequestMethod.POST })
	public void reportYbCx(@RequestBody CostOutRbExcelDto dto) {
		costExcelService.costReportExcel(dto, response);
	}

	@ApiOperation(value = "导出文件-分时段", notes = "导出文件-分时段")
	@RequestMapping(value = "/reportFsdCx", method = { RequestMethod.POST })
	public void reportFsdCx(@RequestBody CostOutFsdExcelDto dto) {
		costExcelService.reportFsdCxExcel(dto, response);
	}

	@ApiOperation(value = "方案查询-班组日报", notes = "方案查询-班组日报")
	@RequestMapping(value = "/reportRbCxContent", method = { RequestMethod.POST })
	public Res<?> reportRbCxContent(@RequestBody CostOutRbExcelDto dto) {
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		res.setResult(costExcelService.reportRbCxContent(dto));
		return res;
	}

	@ApiOperation(value = "方案查询-班组月报", notes = "方案查询-班组月报")
	@RequestMapping(value = "/costMonthReportContent", method = { RequestMethod.POST })
	public Res<?> costMonthReportContent(@RequestBody CostOutBzyhzdExcelDto dto) {
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		res.setResult(costExcelService.costMonthReportContent(dto));
		return res;
	}

	@ApiOperation(value = "方案查询-装置月汇总报表", notes = "方案查询-班组月汇总报表")
	@RequestMapping(value = "/costReportContent", method = { RequestMethod.POST })
	public Res<?> costReportContent(@RequestBody CostOutRbExcelDto dto) {
		Res<List<ComboVo>> res = new Res<List<ComboVo>>();
		res.setResult(costExcelService.costReportContent(dto));
		return res;
	}

}
