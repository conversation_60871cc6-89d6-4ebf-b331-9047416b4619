package com.yunhesoft.leanCosting.costReport.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.leanCosting.calcLogic.ICalcMonthCostReportService;
import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateMonthParamDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.AutoCreateShiftTaskDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.CalculateTimeDto;
import com.yunhesoft.leanCosting.costReport.entity.dto.InstallationMonthReportSaveDto;
import com.yunhesoft.leanCosting.costReport.entity.vo.InstallationMonthReportVo;
import com.yunhesoft.leanCosting.costReport.entity.vo.ProjectDataVo;
import com.yunhesoft.leanCosting.costReport.service.IInstallationMonthReportService;
import com.yunhesoft.system.kernel.config.SysUserHolder;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

//@CrossOrigin(origins = "*", maxAge = 3600, methods = { RequestMethod.GET, RequestMethod.POST }) // 支持跨域
@RestController
@RequestMapping("/installationMonthReport")
@Api(tags = "装置月报数据录入")
public class InstallationMonthReportController{

	@Autowired
	public HttpServletRequest request;
	@Autowired
	private IInstallationMonthReportService serv;
	@Autowired
	private ICalcMonthCostReportService ICMCRS;
	
	/**
	 * 获取当前时间
	 * @category 
	 * <AUTHOR> 
	 * @return String
	 */
	@ApiOperation(value = "获取当前时间", notes = "获取当前时间")
	@RequestMapping(value = "/getNowTime", method = { RequestMethod.POST })
	public Res<String> getNowTime() {		
		Res<String> res = new Res<String>();
		String result = DateTimeUtils.getDate();
		res.setResult(result);
    	return res;
	}
	
	/**
	 * 获取统计时段
	 * @category 
	 * <AUTHOR> 
	 * @param zzdm 装置代码
	 * @param yf 月份
	 * @param projectId 方案id
	 * @return CalculateTimeDto
	 */
	@ApiOperation(value = "获取统计时段", notes = "获取统计时段")
	@RequestMapping(value = "/getCalculateTime", method = { RequestMethod.POST })
	public Res<CalculateTimeDto> getCalculateTime(@RequestBody CalculateTimeDto param) {		
		Res<CalculateTimeDto> res = new Res<CalculateTimeDto>();
    	if (param != null) {
    		CalculateTimeDto result = serv.getCalculateTime(param.getZzdm(), param.getYf(),param.getProjectId());
//    		res.ok(result);
    		res.setResult(result);
        }
    	return res;
	}
	
	/**
	 * 保存统计时段
	 * @category 
	 * <AUTHOR> 
	 * @param calBean 统计时段信息bean
	 * @return boolean 成功true 失败false
	 */
	@ApiOperation(value = "保存统计时段", notes = "保存统计时段")
	@RequestMapping(value = "/saveCalculateTime", method = { RequestMethod.POST })
	public Res<Boolean> saveCalculateTime(@RequestBody CalculateTimeDto param) {		
		Res<Boolean> res = new Res<Boolean>();
    	if (param != null) {
    		boolean result = serv.saveCalculateTime(param);
//    		res.ok(result);
    		res.setResult(result);
        }
    	return res;
	}

    /**
     * 获取方案列表
     * @category 
     * <AUTHOR> 
     * @param zzdm 装置代码
     * @param ksrq 开始日期
     * @param jzrq 截止日期
     * @return
     */
	@ApiOperation(value = "获取方案列表", notes = "获取方案列表")
	@RequestMapping(value = "/getProjectList", method = { RequestMethod.POST })
	public Res<List<ProjectDataVo>> getProjectList(@RequestBody CalculateTimeDto param) {		
		Res<List<ProjectDataVo>> res = new Res<List<ProjectDataVo>>();
    	if (param != null) {
    		List<ProjectDataVo> result = serv.getProjectList(param.getZzdm());
    		res.setResult(result);
        }
    	return res;
	}	
	/**
	 * 获取装置月报数据
	 * @category 
	 * <AUTHOR> 
	 * @param dto 统计时段
	 * @param init 是否读取初始化值（仅清理时使用true，正常读取使用false）
	 * @return List<InstallationMonthReportVo>
	 */
	@ApiOperation(value = "获取装置月报数据", notes = "获取装置月报数据")
	@RequestMapping(value = "/getDataList", method = { RequestMethod.POST })
	public Res<List<InstallationMonthReportVo>> getDataList(@RequestBody CalculateTimeDto param) {		
		Res<List<InstallationMonthReportVo>> res = new Res<List<InstallationMonthReportVo>>();
    	if (param != null) {
    		List<InstallationMonthReportVo> result = serv.getDataList(param,false);
//    		res.ok(result);
    		res.setResult(result);
        }
    	return res;
	}
	/**
	 * 保存装置月报数据
	 * @category 
	 * <AUTHOR> 
	 * @param calBean 填表日期
	 * @param dataList 数据列表
	 * @return String 成功success 失败 返回失败原因
	 */
	@ApiOperation(value = "保存装置月报数据", notes = "保存装置月报数据")
	@RequestMapping(value = "/saveDataList", method = { RequestMethod.POST })
	public Res<String> saveDataList(@RequestBody InstallationMonthReportSaveDto param) {		
		Res<String> res = new Res<String>();
    	if (param != null) {
    		CalculateTimeDto paramCalculateTimeDto=param.getSaveParam();
    		if(paramCalculateTimeDto!=null) {
    			if(paramCalculateTimeDto.getUserName()==null || paramCalculateTimeDto.getUserName().length()==0) {//操作人员为空，从user中获取
    				SysUser user = SysUserHolder.getCurrentUser();
    				paramCalculateTimeDto.setUserName(user.getRealName());
    			}
    		}
    		String result = serv.saveDataList(param.getSaveParam(), param.getDataList());
//    		res.ok(result);
    		res.setResult(result);
        }
    	return res;
	}
	/**
	 * 清除数据
	 * @category 
	 * <AUTHOR> 
	 * @param param.keepData 是否保留数据，true为保留
	 * @return String 成功success 失败 返回失败原因
	 */
	@ApiOperation(value = "清除数据", notes = "清除数据")
	@RequestMapping(value = "/clearData", method = { RequestMethod.POST })
	public Res<List<InstallationMonthReportVo>> clearData(@RequestBody CalculateTimeDto param) {		
		Res<List<InstallationMonthReportVo>> res = new Res<List<InstallationMonthReportVo>>();
    	if (param != null) {
    		List<InstallationMonthReportVo> result = serv.clearData(param);
//    		res.ok(result);
    		res.setResult(result);
        }
    	return res;
	}
	
	/**
	 * @category	班组月汇总表自动生成
	 * 
	 * @param param
	 * @return
	 */
	@ApiOperation(value = "班组月汇总表自动生成")
	@RequestMapping(value = "/autoCreateTeamMonthReport", method = { RequestMethod.POST })
	public Res<?> autoCreateTeamMonthReport(@RequestBody AutoCreateMonthParamDto param) {
		this.ICMCRS.autoTeamCalc(param);
		return Res.OK();
	}
	
	@ApiOperation(value = "班组月汇总表自动生成")
	@RequestMapping(value = "/autoCreateDeviceMonthReport", method = { RequestMethod.POST })
	public Res<?> autoCreateDeviceMonthReport(@RequestBody AutoCreateMonthParamDto param) {
		this.ICMCRS.autoDeviceCalc(param);
		return Res.OK();
	}
	
	@ApiOperation(value = "生成交接班自动计算任务")
	@RequestMapping(value = "/autoCreateShiftTask", method = { RequestMethod.POST })
	public Res<?> autoCreateShiftTask(@RequestBody AutoCreateShiftTaskDto param) {
		this.ICMCRS.autoCreateShiftData(param);
		return Res.OK();
	}
	
	@ApiOperation(value = "交接班计算任务")
	@RequestMapping(value = "/calcCreateShiftTask", method = { RequestMethod.POST })
	public Res<?> calcCreateShiftTask(@RequestBody AutoCreateShiftTaskDto param) {
		this.ICMCRS.calcCreateShiftTask(param);
		return Res.OK();
	}
	
	@ApiOperation(value = "价格导入计算任务")
	@RequestMapping(value = "/calcPriceChangeTask", method = { RequestMethod.POST })
	public Res<?> calcPriceChangeTask(@RequestBody AutoCreateShiftTaskDto param) {
		this.ICMCRS.calcPriceChangeTask(param);
		return Res.OK();
	}
	
	@ApiOperation(value = "删除错误的批次数据")
	@RequestMapping(value = "/DelBatchErrData", method = { RequestMethod.POST })
	public Res<?> delBatchErrData() {
		this.ICMCRS.delBatchErrData();
		return Res.OK();
	}
	
}
