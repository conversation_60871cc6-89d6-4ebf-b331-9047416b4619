package com.yunhesoft.leanCosting.deliverGoods.service;


import com.yunhesoft.leanCosting.deliverGoods.entity.dto.QueryDeliverGoodsDto;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.DeliverGoods;
import com.yunhesoft.leanCosting.deliverGoods.entity.po.DeliverGoodsHeaderSet;
import com.yunhesoft.leanCosting.order.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.order.entity.po.ProductControl;

import javax.servlet.http.HttpServletResponse;

import com.yunhesoft.system.kernel.service.model.Pagination;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description: 发货产品记录服务接口
 * <AUTHOR>
 * @date 2023/8/3
 */
public interface IDeliverGoodsService {
    /**
     * 根据产品列表查询发货记录
     * <AUTHOR>
     * @return
     * @params
    */
    List<DeliverGoods> getDelverGoods(List<ProductControl> productList, QueryDeliverGoodsDto param, Pagination page) throws Exception;
    /**
     * 保存发货记录
     * <AUTHOR>
     * @return
     * @params
    */
    Boolean saveDeliverGoods(SaveDto param);


    Boolean saveDeliverGoodsHeader(SaveDto param);

    /**
     * 获取表头
     * <AUTHOR>
     * @return
     * @params
    */

    List<DeliverGoodsHeaderSet> getDeliverGoodsHeader();
    /**
     * 导出
     * <AUTHOR>
     * @return
     * @params
    */

    void exportDeliverGoods(QueryDeliverGoodsDto param, HttpServletResponse response) throws Exception;
    
    /**
	 * 导入
	 *
	 * @return
	 * <AUTHOR>
	 * @throws Exception 
	 * @throws IOException
	 * @params
	 */
	Boolean importDeliverGoods(MultipartFile file) throws IOException, Exception;
}
