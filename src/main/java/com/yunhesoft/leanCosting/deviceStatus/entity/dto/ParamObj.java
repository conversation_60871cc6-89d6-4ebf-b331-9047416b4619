package com.yunhesoft.leanCosting.deviceStatus.entity.dto;

import java.util.List;

import com.yunhesoft.core.common.dto.BaseQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;
import com.yunhesoft.system.kernel.service.model.Pagination;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ParamObj extends BaseQueryDto {
	
	/** 操作类型 */
	private String opType; //start stop overhaul correct
	
	/** 操作时间 */
	private String opTime; //yyyy-MM-dd hh:mm:ss
	
	/** 分页 */
	private Pagination<?> page;
	
	private List<CostKeyDeviceConf> data;
	
	
	
	private String deviceId; //设备id
	
	private List<String> deviceIdList; //设备id列表
	
	private String orgId; //机构id
	
	private String unitId; //核算对象id
	
	private String searchName; //名称
	
	private Integer statusNum; //启停状态
	
	private Integer sumType; //累计类型：0、整体累计；1、检修后累计；
	
	private Integer selType; //查询类型：1、启停；2、检修；0、修正；
	
	private Boolean isAddSubVal = false; //是否加浮动值（设备开启状态时，开启时间至当前时间的差值）
	
	
	private Double sumHour; //累计小时
	
	private Double currStartSumHour; //本次启动累计小时
	
	private Double correctHour; //修正小时
	
	private String deviceState; //设备状态
	
}
