package com.yunhesoft.leanCosting.deviceStatus.service;


import java.util.List;

import com.yunhesoft.accountTools.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.deviceStatus.entity.dto.ParamObj;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostDeviceStatusData;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostDeviceStatusDataVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostKeyDeviceConfVo;

/**
 *	设备启停服务接口
 * <AUTHOR>
 *
 */
public interface IDeviceStatusService {
    
	
	/**
	 *	获取核算对象数据
	 * @param param
	 * @return
	 */
	public List<ComboVo> getUnitList(ParamObj param);
	
	/**
	 *	获取关键设备数据
	 * @param param
	 * @return
	 */
	public List<CostKeyDeviceConfVo> getDeviceList(ParamObj param);
	
	/**
	 *	获取设备启停明细数据
	 * @param param
	 * @return
	 */
	public List<CostDeviceStatusDataVo> getDeviceItemData(ParamObj param);
	
	/**
	 *	设备启动、停止、检修、修正等操作
	 * @param param
	 * @return
	 */
	public String deviceStartStop(ParamObj param);
	
	/**
	 *	获取累计修正数据
	 * @param param
	 * @return
	 */
	public ParamObj getSumItemData(ParamObj param);
	
	/**
	 *	是否有检修记录
	 * @param param
	 * @return
	 */
	public Boolean getIsHasOverhaul(ParamObj param);
	
	/**
	 *	保存设备状态数据
	 * @param param
	 * @return
	 */
	public String saveDeviceStateData(ParamObj param);
	
	/**
	 *	获取设备状态明细数据
	 * @param param
	 * @return
	 */
	public List<CostDeviceStatusData> getDeviceStatusDataList(ParamObj param);
	
}
