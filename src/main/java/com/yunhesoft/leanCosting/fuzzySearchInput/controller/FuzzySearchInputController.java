package com.yunhesoft.leanCosting.fuzzySearchInput.controller;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.leanCosting.fuzzySearchInput.service.IFuzzySearchInputService;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/cost/fuzzySearch")
@Api(tags = "产品辅助填入设置")
public class FuzzySearchInputController extends BaseRestController {

    @Autowired
    private IFuzzySearchInputService IFuzzySearchInputService;
    
    /**
	 * 获取模糊查询数据.
	 *
	 * @param
	 * @return
	 */
    @ApiOperation("获取数据")
    @RequestMapping(value = "/getData", method = RequestMethod.POST)
    public Res<?> getData(@RequestParam("product") @Nullable String product) {
        return Res.OK(IFuzzySearchInputService.getData(product));
    }
}
