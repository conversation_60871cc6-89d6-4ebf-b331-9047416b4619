package com.yunhesoft.leanCosting.priceManage.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 *	项目价格信息表
 */
@Entity
@Setter
@Getter
@Table(name = "ITEMPRICECHANGEINFO")
public class ItemPriceChangeInfo extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    
    @ApiModelProperty(name="执行日期")
    @Column(name="EXECDATE", length=50)
    private String execDate;
    
    @ApiModelProperty(name="数据类型：priceConfig-价格维护；costConfig-费用维护；processPrice-单位加工费；priceRatio-单价系数；")
    @Column(name="DATATYPE", length=100)
    private String dataType;
    
    @ApiModelProperty(name="备注")
    @Column(name="REMARK", length=2000)
    private String remark;
    
    @ApiModelProperty(name="是否使用：1、使用；0、不使用")
    @Column(name="TMUSED")
    private Integer tmUsed;
    
}
