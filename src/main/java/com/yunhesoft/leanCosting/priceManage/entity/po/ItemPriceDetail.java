package com.yunhesoft.leanCosting.priceManage.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;


/**
 *	项目的单价明细表
 */
@Entity
@Setter
@Getter
@Table(name = "ITEMPRICEDETAIL")
public class ItemPriceDetail extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(name="父ID",example="ITEMPRICECHANGEINFO.ID")
    @Column(name="PID", length=50)
    private String pId;
    
    @Excel(name = "itemId")
    @ApiModelProperty(name="项目ID")
    @Column(name="ITEMID", length=50)
    private String itemId;
    
    @Excel(name = "itemPrice")
    @ApiModelProperty(name="项目单价")
    @Column(name="ITEMPRICE")
    private Double itemPrice;
    
    @Excel(name = "remark")
    @ApiModelProperty(name="备注")
    @Column(name="REMARK", length=2000)
    private String remark;
    
    @ApiModelProperty(name="是否使用：1、使用；0、不使用")
    @Column(name="TMUSED")
    private Integer tmUsed;
    
    @ApiModelProperty(name="排序")
    @Column(name="TMSORT")
    private Integer tmSort;
    
    @Excel(name = "classId")
    @ApiModelProperty(name="分类ID")
    @Column(name="CLASSID", length=50)
    private String classId;
    
    @Excel(name = "erpcode")
    @ApiModelProperty(name="ERP编码")
    @Column(name="ERPCODE", length=200)
    private String erpcode;
    
    @Excel(name = "costCode")
    @ApiModelProperty(name="成本编码")
    @Column(name="COSTCODE", length=200)
    private String costCode;
    
    @Excel(name = "costName")
    @ApiModelProperty(name="成本名称")
    @Column(name="COSTNAME", length=200)
    private String costName;
    
    @Excel(name = "meterUnit")
    @ApiModelProperty(name="计量单位")
    @Column(name="METERUNIT", length=200)
    private String meterUnit;
    
    @Excel(name = "itemName")
    @ApiModelProperty(name="项目名称")
    @Column(name="ITEMNAME", length=200)
    private String itemName;
    
    @ApiModelProperty(name="是否为继承数据")
    @Column(name="isExtend")
    private Integer isExtend;
    
}
