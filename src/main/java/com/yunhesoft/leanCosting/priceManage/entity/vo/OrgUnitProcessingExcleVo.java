package com.yunhesoft.leanCosting.priceManage.entity.vo;

import com.yunhesoft.system.kernel.utils.excel.ExcelExt;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class OrgUnitProcessingExcleVo {

	
	@Excel(name = "核算对象名称", width = 20, orderNum = "1")
	@ExcelExt(align = "left") // 导出列居左对齐
	@ApiModelProperty(name = "核算对象名称")
	private String name;

	@Excel(name = "维护机构", width = 20, orderNum = "2")
	@ExcelExt(align = "left") // 导出列居左对齐
	@ApiModelProperty(name = "维护机构")
	private String orgName;

	@Excel(name = "单位加工费", width = 20, orderNum = "3")
	@ExcelExt(align = "left") // 导出列居左对齐
	@ApiModelProperty(name = "单位加工费")
	private Double unitProcessing;

	@Excel(name = "对照机构", width = 20, orderNum = "4")
	@ExcelExt(align = "left") // 导出列居左对齐
	@ApiModelProperty(name = "对照机构")
	private String compareOrgName;

}
