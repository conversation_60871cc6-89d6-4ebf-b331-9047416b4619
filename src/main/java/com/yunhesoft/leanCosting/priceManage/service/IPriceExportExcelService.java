package com.yunhesoft.leanCosting.priceManage.service;


import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.leanCosting.priceManage.entity.dto.PriceManageQueryDto;
import com.yunhesoft.leanCosting.priceManage.entity.vo.ItemPriceConfigVo;

/**
 *	价格管理导入、导出服务接口
 * <AUTHOR>
 * @date 2023-09-27
 */
public interface IPriceExportExcelService {
	
	
	/**
	 *	价格管理导出Excel
	 * @param queryDto
	 * @param response
	 */
	public void priceManageExportExcel(PriceManageQueryDto queryDto, HttpServletResponse response);
	
	
	/**
	 *	价格管理导入Excel
	 * @param queryDto
	 * @param list
	 * @return
	 */
	public String priceManageImportExcel(PriceManageQueryDto queryDto, List<ItemPriceConfigVo> list);
	
	/**
	 *	价格管理--解析出导入的Excel数据
	 */
	public List<ItemPriceConfigVo> getPriceExcelList(MultipartFile file);
	
	/**
	 *	价格管理--导入Excel
	 * @param queryDto
	 * @param list
	 * @return
	 */
	public String importPriceExcel(PriceManageQueryDto queryDto, List<ItemPriceConfigVo> list);
	
}
