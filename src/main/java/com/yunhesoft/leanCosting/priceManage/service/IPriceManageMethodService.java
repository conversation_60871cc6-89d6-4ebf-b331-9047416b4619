package com.yunhesoft.leanCosting.priceManage.service;


import java.util.List;

import com.yunhesoft.leanCosting.priceManage.entity.dto.PriceManageQueryDto;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceChangeInfo;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceDetail;
import com.yunhesoft.leanCosting.priceManage.entity.po.ItemPriceLog;

/**
 *	价格管理相关方法服务接口
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface IPriceManageMethodService {

	//————————————————————————————————————————— 执行日期 ↓ ———————————————————————————————————————————
	
	/**
	 *	获取项目价格信息数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceChangeInfo> getItemPriceChangeInfoList(PriceManageQueryDto queryDto);
	
	/**
	 *	项目价格信息最大版本
	 * @param dataType
	 * @param execDate
	 * @param compareType  比较类型：<：小于（默认）；      <=：小于等于；
	 * @return
	 */
	public ItemPriceChangeInfo getMaxVersionPriceChangeObj(String dataType,String execDate,String compareType);
	
	/**
	 *	保存项目价格信息数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveItemPriceChangeInfoData(List<ItemPriceChangeInfo> addList,List<ItemPriceChangeInfo> updList,
		List<ItemPriceChangeInfo> delList);
	
	//————————————————————————————————————————— 执行日期 ↑ ———————————————————————————————————————————
	
	
	
	
	
	//————————————————————————————————————————— 单价明细 ↓ ———————————————————————————————————————————
	
	/**
	 *	获取项目的单价明细数据（从redis中获取）
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceDetail> getItemPriceDetailListByRedis(String pid);
	
	/**
	 *	获取项目的单价明细数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceDetail> getItemPriceDetailList(PriceManageQueryDto queryDto);
	
	/**
	 *	保存项目的单价明细数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveItemPriceDetailData(List<ItemPriceDetail> addList,List<ItemPriceDetail> updList,
		List<ItemPriceDetail> delList);
	
	//————————————————————————————————————————— 单价明细 ↑ ———————————————————————————————————————————
	
	
	
	
	
	//————————————————————————————————————————— 单价日志 ↓ ———————————————————————————————————————————
	
	/**
	 *	获取项目的单价日志数据
	 * @param queryDto
	 * @return
	 */
	public List<ItemPriceLog> getItemPriceLogList(PriceManageQueryDto queryDto);
	
	/**
	 *	保存项目的单价日志数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	public String saveItemPriceLogData(List<ItemPriceLog> addList,List<ItemPriceLog> updList,
		List<ItemPriceLog> delList);
	
	//————————————————————————————————————————— 单价日志 ↑ ———————————————————————————————————————————
	
	
}
