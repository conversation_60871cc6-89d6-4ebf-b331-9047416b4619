package com.yunhesoft.leanCosting.priceManage.service;

import java.util.List;

import com.yunhesoft.leanCosting.priceManage.entity.dto.CoefficientQueryDto;
import com.yunhesoft.leanCosting.priceManage.entity.vo.UnitPriceCoefficientVo;

public interface IUnitPriceCoefficientService {

	/**
	 * 查询
	 * @param dto
	 * @return
	 */
	public List<UnitPriceCoefficientVo> getData(CoefficientQueryDto dto);

	/**
	 * 更新数据
	 * @param list
	 * @return
	 */
	public String saveData(List<UnitPriceCoefficientVo> list);
	
}
