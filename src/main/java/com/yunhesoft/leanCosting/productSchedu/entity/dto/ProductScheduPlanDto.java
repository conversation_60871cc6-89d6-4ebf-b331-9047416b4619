package com.yunhesoft.leanCosting.productSchedu.entity.dto;



import com.yunhesoft.core.common.dto.BaseQueryDto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProductScheduPlanDto extends BaseQueryDto{
	/** 核算对象编码 */
    @ApiModelProperty(value = "核算对象编码（all:全部）")
    private String unitId;
    
    /** 核算对象名称 */
    @ApiModelProperty(value = "核算对象名称（all:全部）")
    private String unitName;
    
    
    /** 方案编码 */
    @ApiModelProperty(value = "方案编码（all:全部）")
    private String programid;
    
    /** 产品名称 */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    
    /** 开始日期 */
    @ApiModelProperty(value = "开始日期")
    private String startDt;
    
    /** 截止日期 */
    @ApiModelProperty(value = "截止日期")
    private String endDt;
    
    /** 完成状态 */
    @ApiModelProperty(value = "完成状态(0：全部，1：已完成，-1：未完成")
    private Integer iscomplete;
    
    /** 数据状态 */
    @ApiModelProperty(value = "数据状态（0：全部，1：待审核，2：审核通过（待审批），3：审批通过，-1：审核否决，-2：审批否决")
    private Integer dataState;
    
    /** 完成状态 */
    @ApiModelProperty(value = "审核人员编码")
    private String shuserid;
    
    /** 完成状态 */
    @ApiModelProperty(value = "审批人员编码")
    private String spuserid;
    
    /** 完成状态 */
    @ApiModelProperty(value = "数据编码")
    private String mainId;
    
    /** 完成状态 */
    @ApiModelProperty(value = "是否待办查询（1：是，0：普通查询）")
    private Integer isToDo;
    
    /** 反馈日期 */
    private String feeddate;
    
    /** 查询类型（null 或1 查询手动添加的和确认的，0：只查询未确认的 */
    private Integer cxType;
   
    
}
