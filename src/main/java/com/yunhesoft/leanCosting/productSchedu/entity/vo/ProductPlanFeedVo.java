package com.yunhesoft.leanCosting.productSchedu.entity.vo;


import lombok.Data;

import java.util.Date;

@Data
public class ProductPlanFeedVo {
	
	/** 数据编码 */
    private String id;
    
	/** 计划编码 */
    private String planid;
    
    /** 订单ID  */
    private String indentid;
    
    /** 产品名称  */
    private String productname;
    
    /** 产品ID  */
    private String productid;
    
    /** 核算对象ID  */
    private String unitid;
    
    /** 核算对象名称  */
    private String unitname;
    
    /** 方案ID  */
    private String programid;
    
    /** 方案名称  */
    private String programname;
    
    /** 计划开始日期 */
    private String planStartDay;
    
    /** 计划结束日期 */
    private String planEndDay;
    
    /** 完成量  */
    private Double completeamount;
    
    
    /** 开始日期和时间 */
    private String startdatetime;
    
    /** 结束日期和时间 */
    private String enddatetime;
    
    /** 反馈机构代码 */
    private String feedorgcode;
    
    /** 反馈机构名称 */
    private String feedorgname;
    
    /** 反馈人员编码 */
    private String feeduserid;
    
    /** 反馈人员姓名 */
    private String feedusername;
    
    /** 反馈完成量 */
    private Double feedvalue;
    
    /** 批次号 */
    private String batchno;
    
    /** 反馈日期 */
    private String feeddate;
    
    /** 反馈日期+时间 */
    private String feeddatetime;
    
    /** 是否完成（0：未完成，1：已完成 ） */
    private Integer iscomplete;
    
    /** 客户单位名称  */
    private String clientname;
    
    /** 计划名称  */
    private String planname;
    
    /** 汇总反馈编码 */
    private String groupid;
    
    /** 汇总反馈时间 */
    private String feedtotaldate;
    
    /** 标识 */
	private String rowFlag;
	
	/** 产品名称(项目名称) */
    private String itemname;
    
    /** 产品编码(项目编码) */
    private String itemid;
    
    /** 管号 */
    private String pipeno;
    
    /** 炉号 */
    private String furnaceno;
    
    /** 下班时间 */
    private String xbsj;
    
    /** 上班时间 */
    private String sbsj;
    
    /** 班次代码 */
    private String shiftClassId;
    
    /** 表单编码 */
    private String fromId;
    
    /** 班次名称 */
    private String shiftClassName;
    
    /** 备用字段1  */
    private String byzd1;

    /** 备用字段2  */
    private String byzd2;

    /** 备用字段3  */
    private String byzd3;

    /** 备用字段4  */
    private String byzd4;

    /** 备用字段5  */
    private String byzd5;

    /** 备用字段6  */
    private String byzd6;

    /** 备用字段7  */
    private String byzd7;

    /** 备用字段8  */
    private String byzd8;

    /** 备用字段9  */
    private String byzd9;

    /** 备用字段10  */
    private String byzd10;
    
    /** 批次号计算部分 */
    private String batchnocalstr;
    
    /** 批次号自动序号 */
    private Integer batchnoautonum;
    
    /** 是否手动修改了批次号 */
    private Integer ischangebatchno;
    
    /** 是否手动修改了备用字段1 */
    private Integer ischangebyzd1;
    
    /** 是否手动修改了备用字段2 */
    private Integer ischangebyzd2;
    
    /** 是否手动修改了备用字段3 */
    private Integer ischangebyzd3;
    
    /** 是否手动修改了备用字段4 */
    private Integer ischangebyzd4;
    
    /** 是否手动修改了备用字段5 */
    private Integer ischangebyzd5;
    
    /** 是否手动修改了备用字段6 */
    private Integer ischangebyzd6;
    
    /** 是否手动修改了备用字段7 */
    private Integer ischangebyzd7;
    
    /** 是否手动修改了备用字段8 */
    private Integer ischangebyzd8;
    
    /** 是否手动修改了备用字段9 */
    private Integer ischangebyzd9;
    
    /** 是否手动修改了备用字段10 */
    private Integer ischangebyzd10;

    private Date createTime;
    
    /** 项目价格 */
    private Double itemprice;
}
