package com.yunhesoft.leanCosting.programConfig.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.programConfig.entity.vo.IndexProgramVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	指标方案保存类
 */
@Setter
@Getter
public class IndexSaveDto {
	
	
	private String pvId; //方案版本ID
	
	private String editType; //del-删除；save-保存；
    
    private List<IndexProgramVo> indexProgramList; //指标方案保存数据
    
    
}
