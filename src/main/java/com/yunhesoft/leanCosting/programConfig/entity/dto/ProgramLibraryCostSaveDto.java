package com.yunhesoft.leanCosting.programConfig.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramVersion;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramLibraryCostVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	方案保存类
 */
@Setter
@Getter
public class ProgramLibraryCostSaveDto {
	
	
	private String pvId; //方案版本ID
	
	private String copyPvId; //复制数据的版本
	
	private String editType; //del-删除；save-保存；
    
    private ProgramVersion programVersionObj; //方案版本保存数据
    
    private List<ProgramLibraryCostVo> programLibraryCostList; //成本核算方案保存数据
    
    private String projectDataId; //方案ID
    
}
