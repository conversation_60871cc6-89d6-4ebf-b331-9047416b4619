package com.yunhesoft.leanCosting.programConfig.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 超限后的预案表
 */
@Entity
@Setter
@Getter
@Table(name = "PROGRAMCONTINGENCYPLAN")
public class ProgramContingencyPlan extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 方案版本ID */
	@Column(name = "PVID", length = 100)
	private String pvId;

	/** 指标ID */
	@Column(name = "PID", length = 100)
	private String pId;

	/** 超限的持续时间 */
	@Column(name = "DURATION")
	private Double duration;

	/** 超限预案 */
	@Column(name = "PLANINFO", length = 2000)
	private String planInfo;

	/** 计划类型：up 超上限；low 超下限 */
	@Column(name = "PLANTYPE", length = 20)
	private String planType;

	/** 限值类型：key 关键限制；operate 操作限制 */
	@Column(name = "LIMITTYPE", length = 20)
	private String limitType;

	/** 开始时段 */
	@Column(name = "STARTPERIOD", length = 10)
	private String startPeriod;

	/** 截止时段 */
	@Column(name = "ENDPERIOD", length = 10)
	private String endPeriod;

	/** 时段内接收岗位 */
	@Column(name = "INDURATIONPOS", length = 1000)
	private String inDurationPos;

	/** 时段内接收岗位名称 */
	@Column(name = "INDURATIONPOSNAME", length = 2000)
	private String inDurationPosName;

	/** 时段内仅当班 */
	@Column(name = "INONDUTY")
	private Integer inOnduty;

	/** 时段外接收岗位 */
	@Column(name = "OUTDURATIONPOS", length = 1000)
	private String outDurationPos;

	/** 时段外接收岗位名称 */
	@Column(name = "OUTDURATIONPOSNAME", length = 2000)
	private String outDurationPosName;

	/** 时段外仅当班 */
	@Column(name = "OUTONDUTY")
	private Integer outOnduty;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmSort;

}
