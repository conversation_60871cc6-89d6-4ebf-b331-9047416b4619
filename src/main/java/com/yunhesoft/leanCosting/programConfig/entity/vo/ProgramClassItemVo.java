package com.yunhesoft.leanCosting.programConfig.entity.vo;


import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 	方案分类-项目Vo类
 * <AUTHOR>
 * @date 2023/06/08
 */
@Setter
@Getter
public class ProgramClassItemVo {
	
	/** 数据ID */
    private String id;
    
    /** 父分类ID */
    private String pId;
    
    /** 分类名称 */
    private String pcName;
    
    /** 方案名称 */
    private String piName;
    
    /** 注释 */
    private String memo;
    
    /** 排序 */
    private Integer tmSort;
    
    /** 1、使用；0、不使用 */
    private Integer tmUsed;
    
    /** 设备类型 */
    private String deviceType;
    
    /** 设备状态 */
    private String deviceStatus;
    
    /** 方案版本日期 */
    private String pVersion;
    
    /** 被复制数据的唯一ID */
    private String copyId;
    
    /** 后续工序、方案、接收人（保存时用） */
    private List<ProgramNextInfoVo> nextInfoList;
    
    //--------------------------------
    
	
	private String nodeId; // 节点id
	
	private String nodeName; // 节点name
	
	private String nodeShowName; // 节点显示name
	
	private Integer isLeaf; // 是否为叶子节点
	
	private List<ProgramClassItemVo> children; // 子记录
	
	
}
