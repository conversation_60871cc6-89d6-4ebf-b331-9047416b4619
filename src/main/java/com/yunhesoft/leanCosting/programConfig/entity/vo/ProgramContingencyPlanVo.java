package com.yunhesoft.leanCosting.programConfig.entity.vo;


import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramContingencyPlan;

import lombok.Getter;
import lombok.Setter;

/**
 * 	超限预案Vo类
 * <AUTHOR>
 * @date 2023/06/30
 */
@Setter
@Getter
public class ProgramContingencyPlanVo extends ProgramContingencyPlan {
	
	
	private static final long serialVersionUID = 1L;
	
	/** 超限预案接收人 */
    //private List<ProgramAlertReceiver> personList; //暂时不用此表
    
    
}
