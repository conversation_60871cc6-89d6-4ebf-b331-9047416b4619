package com.yunhesoft.leanCosting.programConfig.service.impl;


import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.ProjectType;
import com.yunhesoft.leanCosting.baseConfig.service.ICostProjectTypeService;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.CostProgStartTimeDto;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.dto.CraftCardQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramLibraryCostQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramQueryDto;
import com.yunhesoft.leanCosting.programConfig.entity.dto.ProgramSaveDto;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramAcceptUser;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramClass;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramItem;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramNextInfo;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramVersion;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ComboVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.OperationCardFormVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramClassItemVo;
import com.yunhesoft.leanCosting.programConfig.entity.vo.ProgramNextInfoVo;
import com.yunhesoft.leanCosting.programConfig.service.IProgramCraftCardService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramService;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;
import com.yunhesoft.system.employee.entity.vo.EmployeeVo;
import com.yunhesoft.system.employee.service.IEmployeeBasicOperationService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 方案配置相关服务接口实现类
 * 
 * <AUTHOR>
 * @date 2023-06-19
 */
@Service
public class ProgramServiceImpl implements IProgramService {

	@Autowired
	private EntityService entityService;

	@Autowired
	private IProgramLibraryCostService programLibraryCostService;

	@Autowired
	private ICostService costService;

	@Autowired
	private ICostProjectTypeService projectTypeService;
	
	@Autowired
	private IUnitMethodService unitMethodService;
	
	@Autowired
    private IEmployeeBasicOperationService empService;
	
	@Autowired
    private IProgramCraftCardService craftCardService;
	
	@Autowired
	private IZzRunStateService unitProgChangeServ;
	

	/**
	 * 获取方案项目树形数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<ProgramClassItemVo> getProgramClassItemTreeData(ProgramQueryDto queryDto) {
		List<ProgramClassItemVo> result = new ArrayList<ProgramClassItemVo>();
		String rootId = "root";
		String rootName = "方案信息";
		ProgramClassItemVo vo = new ProgramClassItemVo();
		vo.setNodeId(rootId);
		vo.setNodeName(rootName);
		vo.setNodeShowName(rootName);
		vo.setId(rootId);
		vo.setPcName(rootName);
		vo.setIsLeaf(0);
		result.add(vo);
		Map<String, List<ProgramClass>> classMap = new HashMap<String, List<ProgramClass>>();
		Map<String, List<ProgramItem>> itemMap = new HashMap<String, List<ProgramItem>>();
		// 方案分类
		List<ProgramClass> classList = this.getProgramClassList(queryDto);
		if (StringUtils.isNotEmpty(classList)) {
			classMap = classList.stream().collect(Collectors.groupingBy(ProgramClass::getPId, Collectors.toList()));
		}
		// 方案项目
		Map<String, ProjectType> typeMap = new HashMap<String, ProjectType>();
		List<ProgramItem> itemList = this.getProgramItemList(queryDto);
		if (StringUtils.isNotEmpty(itemList)) {
			itemMap = itemList.stream().collect(Collectors.groupingBy(ProgramItem::getPId, Collectors.toList()));
			
			// 有项目再查询运行状态
			List<ProjectType> typeList = projectTypeService.getData();
			if (StringUtils.isNotEmpty(typeList)) {
				typeMap = typeList.stream().collect(Collectors.toMap(ProjectType::getId, Function.identity()));
			}
		}
		this.setTreeChildData(vo, classMap, itemMap, typeMap);
		return result;
	}

	// 设置树形子节点
	private void setTreeChildData(ProgramClassItemVo vo, Map<String, List<ProgramClass>> classMap,
		Map<String, List<ProgramItem>> itemMap, Map<String, ProjectType> typeMap) {
		if (StringUtils.isNotNull(vo)) {
			String pId = vo.getId();
			List<ProgramClassItemVo> childTreeList = new ArrayList<ProgramClassItemVo>();
			// 分类
			if (StringUtils.isNotEmpty(classMap) && classMap.containsKey(pId)) {
				List<ProgramClass> childClassList = classMap.get(pId);
				if (StringUtils.isNotEmpty(childClassList)) {
					for (int i = 0; i < childClassList.size(); i++) {
						ProgramClass childClassObj = childClassList.get(i);
						// 赋值属性
						ProgramClassItemVo childClassVo = ObjUtils.copyTo(childClassObj, ProgramClassItemVo.class);
						childClassVo.setNodeId(childClassVo.getId());
						childClassVo.setNodeName(childClassVo.getPcName());
						childClassVo.setNodeShowName(childClassVo.getPcName());
						childClassVo.setIsLeaf(0);
						this.setTreeChildData(childClassVo, classMap, itemMap, typeMap);
						childTreeList.add(childClassVo);
					}
				}
			}
			// 项目
			if (StringUtils.isNotEmpty(itemMap) && itemMap.containsKey(pId)) {
				List<ProgramItem> childItemList = itemMap.get(pId);
				if (StringUtils.isNotEmpty(childItemList)) {
					for (int i = 0; i < childItemList.size(); i++) {
						ProgramItem childItemObj = childItemList.get(i);
						// 赋值属性
						ProgramClassItemVo childItemVo = ObjUtils.copyTo(childItemObj, ProgramClassItemVo.class);
						String programId = childItemVo.getId();
						childItemVo.setNodeId(programId);
						childItemVo.setNodeName(childItemVo.getPiName());
						childItemVo.setIsLeaf(1);
						String deviceStatus = childItemVo.getDeviceStatus();
						String nodeShowName = childItemVo.getNodeName();
						if (StringUtils.isNotEmpty(deviceStatus) && StringUtils.isNotEmpty(typeMap)
								&& typeMap.containsKey(deviceStatus)) {
							String dsname = typeMap.get(deviceStatus).getDsname();
							if (StringUtils.isNotEmpty(dsname)) {
								nodeShowName += "【" + dsname.trim() + "】";
							}
						}
						childItemVo.setNodeShowName(nodeShowName);
						childTreeList.add(childItemVo);
					}
				}
			}
			if (StringUtils.isNotEmpty(childTreeList)) {
				vo.setChildren(childTreeList);
			}
		}
	}

	/**
	 * 获取方案分类数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<ProgramClass> getProgramClassList(ProgramQueryDto queryDto) {
		List<ProgramClass> result = new ArrayList<ProgramClass>();
		try {
			String deviceType = ""; // 设备类型
			String pId = ""; // 父ID
			String id = ""; // 数据ID
			if (StringUtils.isNotNull(queryDto)) {
				deviceType = queryDto.getDeviceType();
				pId = queryDto.getPId();
				id = queryDto.getId();
			}

			// 检索条件
			Where where = Where.create();
			where.eq(ProgramClass::getTmUsed, 1);

			if (StringUtils.isNotEmpty(deviceType)) {
				where.eq(ProgramClass::getDeviceType, deviceType);
			}
			if (StringUtils.isNotEmpty(pId)) {
				where.eq(ProgramClass::getPId, pId);
			}
			if (StringUtils.isNotEmpty(id)) {
				where.eq(ProgramClass::getId, id);
			}

			// 排序
			Order order = Order.create();
			order.orderByAsc(ProgramClass::getTmSort);

			List<ProgramClass> list = entityService.queryData(ProgramClass.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 获取方案分类对象
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public ProgramClass getProgramClassObjById(String id) {
		ProgramClass result = new ProgramClass();
		if (StringUtils.isNotEmpty(id)) {
			ProgramClass queryObj = entityService.queryObjectById(ProgramClass.class, id);
			if (StringUtils.isNotNull(queryObj)) {
				result = queryObj;
			}
		}
		return result;
	}

	/**
	 * 获取方案项目数据
	 * 
	 * @param queryDto
	 * @return
	 */
	@Override
	public List<ProgramItem> getProgramItemList(ProgramQueryDto queryDto) {
		List<ProgramItem> result = new ArrayList<ProgramItem>();
		try {
			String deviceType = ""; // 设备类型
			List<String> deviceTypeList = null;
			String pId = ""; // 父ID
			String id = ""; // 数据ID
			List<String> idList = null; //数据ID列表
			if (StringUtils.isNotNull(queryDto)) {
				deviceType = queryDto.getDeviceType();
				deviceTypeList = queryDto.getDeviceTypeList();
				pId = queryDto.getPId();
				id = queryDto.getId();
				idList = queryDto.getIdList();
			}

			// 检索条件
			Where where = Where.create();
			where.eq(ProgramItem::getTmUsed, 1);

			if (StringUtils.isNotEmpty(deviceType)) {
				where.eq(ProgramItem::getDeviceType, deviceType);
			}
			if (StringUtils.isNotEmpty(deviceTypeList)) {
				where.in(ProgramItem::getDeviceType, deviceTypeList.toArray());
			}
			if (StringUtils.isNotEmpty(pId)) {
				where.eq(ProgramItem::getPId, pId);
			}
			if (StringUtils.isNotEmpty(id)) {
				where.eq(ProgramItem::getId, id);
			}
			if (StringUtils.isNotEmpty(idList)) {
				where.in(ProgramItem::getId, idList.toArray());
			}

			// 排序
			Order order = Order.create();
			order.orderByAsc(ProgramItem::getDeviceType);
			order.orderByAsc(ProgramItem::getPId);
			order.orderByAsc(ProgramItem::getTmSort);
			List<ProgramItem> list = entityService.queryData(ProgramItem.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}

	/**
	 * 获取方案分类对象
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public ProgramItem getProgramItemObjById(String id) {
		ProgramItem result = new ProgramItem();
		if (StringUtils.isNotEmpty(id)) {
			ProgramItem queryObj = entityService.queryObjectById(ProgramItem.class, id);
			if (StringUtils.isNotNull(queryObj)) {
				result = queryObj;
			}
		}
		return result;
	}

	/**
	 * 保存方案项目树形数据
	 * 
	 * @param saveDto
	 * @return
	 */
	@Override
	public String saveProgramClassItemTreeData(ProgramSaveDto saveDto) {
		String result = "";
		if (saveDto != null) {
			String editType = saveDto.getEditType();
			ProgramClassItemVo saveObj = saveDto.getProgramClassItemObj();
			if (StringUtils.isNotEmpty(editType) && StringUtils.isNotNull(saveObj)) {
				int isLeaf_save = saveObj.getIsLeaf() == null ? 0 : saveObj.getIsLeaf();
				if (isLeaf_save == 0) { // 分类
					result = this.saveProgramClassData(editType, saveObj);
				} else { // 项目
					result = this.saveProgramItemData(editType, saveObj);
				}
			}
		}
		return result;
	}

	/**
	 *	保存方案分类数据
	 * @param editType
	 * @param saveObj
	 * @return
	 */
	private String saveProgramClassData(String editType, ProgramClassItemVo saveObj) {
		String result = "";
		List<ProgramClass> addClassList = new ArrayList<ProgramClass>();
		List<ProgramClass> updClassList = new ArrayList<ProgramClass>();
		String id_save = saveObj.getId();
		String deviceType_save = saveObj.getDeviceType(); // 设备类型
		String pVersion = saveObj.getPVersion();
		if ("save".equals(editType) || "copy".equals(editType)) { // 保存、另存为
			String copyPvId = ""; // 复制数据的版本
			if ("copy".equals(editType)) { // 另存为
				String copyId = saveObj.getCopyId();
				String maxPvId = programLibraryCostService.getMaxProgramVersionId(copyId, null,""); // 获取该方案全部数据的最新版本
				if (StringUtils.isNotEmpty(maxPvId)) {
					copyPvId = maxPvId;
				}
			}

			String nodeName_save = saveObj.getNodeName() == null ? "" : saveObj.getNodeName().trim();
			String pId_save = saveObj.getPId();

			int maxNum = 0; // 最大序号
			Map<String, ProgramClass> sameNameMap = new HashMap<String, ProgramClass>();
			Map<String, ProgramClass> dataMap = new HashMap<String, ProgramClass>();
			ProgramQueryDto queryDto = new ProgramQueryDto();
			queryDto.setDeviceType(deviceType_save);
			queryDto.setPId(pId_save);
			List<ProgramClass> dataList = this.getProgramClassList(queryDto);
			if (StringUtils.isNotEmpty(dataList)) {
				sameNameMap = dataList.stream().collect(Collectors.toMap(ProgramClass::getPcName, Function.identity()));
				dataMap = dataList.stream().collect(Collectors.toMap(ProgramClass::getId, Function.identity()));
				Integer maxSort = dataList.get(dataList.size() - 1).getTmSort();
				if (maxSort != null) {
					maxNum = maxSort;
				}
			}

			if (StringUtils.isNotEmpty(dataMap) && StringUtils.isNotEmpty(id_save) && dataMap.containsKey(id_save)) { // 修改
				if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(nodeName_save)
						&& sameNameMap.containsKey(nodeName_save)) {
					String sameId = sameNameMap.get(nodeName_save).getId();
					if (StringUtils.isNotEmpty(sameId) && !sameId.equals(id_save)) {
						result = "方案分类名称【" + nodeName_save + "】已存在，不能重复设置！";
						return result;
					}
				}
				ProgramClass dataObj = dataMap.get(id_save);
				String id = dataObj.getId();
				BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
				dataObj.setId(id);
				dataObj.setPcName(nodeName_save);
				updClassList.add(dataObj);
				if (entityService.updateByIdBatch(updClassList) == 0) {
					result = "修改失败！";
				}
			} else { // 新增
				if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(nodeName_save)
						&& sameNameMap.containsKey(nodeName_save)) {
					result = "方案分类名称【" + nodeName_save + "】已存在，不能重复设置！";
					return result;
				}
				maxNum += 1;
				if (StringUtils.isEmpty(id_save)) {
					id_save = TMUID.getUID(); // 方案ID
				}
				String projectDataId = id_save;
				ProgramClass dataObj = new ProgramClass();
				BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
				dataObj.setId(projectDataId);
				dataObj.setPcName(nodeName_save);
				dataObj.setTmSort(maxNum);
				addClassList.add(dataObj);
				if (entityService.insertBatch(addClassList) == 0) {
					result = "添加失败！";
				}else {
					// 新增版本数据
					programLibraryCostService.editProgramVersion("save", projectDataId, pVersion, copyPvId);
				}
			}
		} else if ("del".equals(editType)) { // 删除
			if (StringUtils.isNotEmpty(id_save)) {
				ProgramClass dataObj = this.getProgramClassObjById(id_save);
				if (StringUtils.isNotNull(dataObj)) {
					dataObj.setTmUsed(0);
					updClassList.add(dataObj);
					if (entityService.updateByIdBatch(updClassList) == 0) {
						result = "删除失败！";
					}else {
						// 删除版本数据
						programLibraryCostService.editProgramVersion("del", id_save, null, null);
					}
				}
			}
		}
		return result;
	}

	/**
	 *	保存方案项目数据
	 * @param editType
	 * @param saveObj
	 * @return
	 */
	private String saveProgramItemData(String editType, ProgramClassItemVo saveObj) {
		String result = "";
		List<ProgramItem> addItemList = new ArrayList<ProgramItem>();
		List<ProgramItem> updItemList = new ArrayList<ProgramItem>();
		String id_save = saveObj.getId();
		String deviceType_save = saveObj.getDeviceType(); // 设备类型
		String pVersion = saveObj.getPVersion();
		if ("save".equals(editType) || "copy".equals(editType)) { // 保存、另存为
			String copyPvId = ""; // 复制数据的版本
			if ("copy".equals(editType)) { // 另存为
				String copyId = saveObj.getCopyId();
				String maxPvId = programLibraryCostService.getMaxProgramVersionId(copyId, null,""); // 获取该方案全部数据的最新版本
				if (StringUtils.isNotEmpty(maxPvId)) {
					copyPvId = maxPvId;
				}
			}

			String nodeName_save = saveObj.getNodeName() == null ? "" : saveObj.getNodeName().trim();
			String pId_save = saveObj.getPId();
			List<ProgramNextInfoVo> nextInfoList = saveObj.getNextInfoList();

			int maxNum = 0; // 最大序号
			Map<String, ProgramItem> sameNameMap = new HashMap<String, ProgramItem>();
			Map<String, ProgramItem> dataMap = new HashMap<String, ProgramItem>();
			ProgramQueryDto queryDto = new ProgramQueryDto();
			queryDto.setDeviceType(deviceType_save);
			queryDto.setPId(pId_save);
			List<ProgramItem> dataList = this.getProgramItemList(queryDto);
			if (StringUtils.isNotEmpty(dataList)) {
				sameNameMap = dataList.stream().collect(Collectors.toMap(ProgramItem::getPiName, Function.identity()));
				dataMap = dataList.stream().collect(Collectors.toMap(ProgramItem::getId, Function.identity()));
				Integer maxSort = dataList.get(dataList.size() - 1).getTmSort();
				if (maxSort != null) {
					maxNum = maxSort;
				}
			}

			if (StringUtils.isNotEmpty(dataMap) && StringUtils.isNotEmpty(id_save) && dataMap.containsKey(id_save)) { // 修改
				if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(nodeName_save)
						&& sameNameMap.containsKey(nodeName_save)) {
					String sameId = sameNameMap.get(nodeName_save).getId();
					if (StringUtils.isNotEmpty(sameId) && !sameId.equals(id_save)) {
						result = "方案项目名称【" + nodeName_save + "】已存在，不能重复设置！";
						return result;
					}
				}
				ProgramItem dataObj = dataMap.get(id_save);
				String id = dataObj.getId();
				BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
				dataObj.setId(id);
				dataObj.setPiName(nodeName_save);
				updItemList.add(dataObj);
				if (entityService.updateByIdBatch(updItemList) == 0) {
					result = "更新失败（方案）！";
				}
				if(StringUtils.isEmpty(result)) {
					result = this.saveProgramNextInfoData(id, nextInfoList);
				}
			} else { // 新增
				if (StringUtils.isNotEmpty(sameNameMap) && StringUtils.isNotEmpty(nodeName_save)
						&& sameNameMap.containsKey(nodeName_save)) {
					result = "方案项目名称【" + nodeName_save + "】已存在，不能重复设置！";
					return result;
				}
				maxNum += 1;
				if (StringUtils.isEmpty(id_save)) {
					id_save = TMUID.getUID(); // 方案ID
				}
				String projectDataId = id_save;
				ProgramItem dataObj = new ProgramItem();
				BeanUtils.copyProperties(saveObj, dataObj); // 赋予返回对象
				dataObj.setId(projectDataId);
				dataObj.setPiName(nodeName_save);
				dataObj.setTmSort(maxNum);
				addItemList.add(dataObj);
				if (entityService.insertBatch(addItemList) == 0) {
					result = "添加失败（方案）！";
				}
				if(StringUtils.isEmpty(result)) {
					result = this.saveProgramNextInfoData(id_save, nextInfoList);
				}
				if(StringUtils.isEmpty(result)) {
					// 新增版本数据
					programLibraryCostService.editProgramVersion("save", projectDataId, pVersion, copyPvId);
				}
			}
		} else if ("del".equals(editType)) { // 删除
			if (StringUtils.isNotEmpty(id_save)) {
				ProgramItem dataObj = this.getProgramItemObjById(id_save);
				if (StringUtils.isNotNull(dataObj)) {
					dataObj.setTmUsed(0);
					updItemList.add(dataObj);
					boolean isCanDel = true;
					CostProgStartTimeDto progChangeDto = new CostProgStartTimeDto();
					progChangeDto.setProgramid(id_save);
					isCanDel = unitProgChangeServ.isCanDelUnitProg(progChangeDto);
					if(isCanDel) {
						if (entityService.updateByIdBatch(updItemList) == 0) {
							result = "删除失败！";
						}
						if(StringUtils.isEmpty(result)) {
							result = this.saveProgramNextInfoData(id_save, null);
						}
					}else {
						result = "当前方案已被使用，不允许删除！";
						return result;
					}
					if(StringUtils.isEmpty(result)) {
						// 删除版本数据
						programLibraryCostService.editProgramVersion("del", id_save, null, null);
					}
				}
			}
		}
		return result;
	}

	/**
	 * 获取方案列表（根据核算对象ID）
	 * 
	 * @param unitid 核算对象ID
	 * @return
	 */
	@Override
	public List<ProgramItem> getProgramItemListByUnitid(String unitid) {
		List<ProgramItem> result = new ArrayList<ProgramItem>();
		if (StringUtils.isNotEmpty(unitid)) {
			Costuint costunitObj = costService.getCostuintObjById(unitid);
			if (StringUtils.isNotNull(costunitObj)) {
				String unittype = costunitObj.getUnittype();
				if (StringUtils.isNotEmpty(unittype)) {
					ProgramQueryDto queryDto = new ProgramQueryDto();
					queryDto.setDeviceType(unittype);
					List<ProgramItem> queryList = this.getProgramItemList(queryDto);
					if (StringUtils.isNotEmpty(queryList)) {
						result = queryList;
					}
				}
			}
		}
		return result;
	}

	/**
	 * 获取新数据ID
	 * 
	 * @return
	 */
	@Override
	public String getNewDataId() {
		return TMUID.getUID();
	}

	/**
	 * 批量获取方案的运行状态
	 * 
	 * @param id
	 * @return
	 */
	@Override
	public HashMap<String, ProjectType> getProgramStateByIdList(List<String> id) {
		HashMap<String, ProjectType> rtn = new HashMap<String, ProjectType>();
		// 得到运行状态的ID与名称对照
		HashMap<String, ProjectType> ztm = projectTypeService.getProjectTypeM();
		// 根据传入的方案ID获取方案对象
		Where where = Where.create();
		where.in(ProgramItem::getId, id.toArray());
		List<ProgramItem> pil = entityService.queryList(ProgramItem.class, where, null);
		if (pil != null) {
			String faid;
			for (ProgramItem x : pil) {
				faid = x.getDeviceStatus();
				if (ztm.containsKey(faid)) {
					ProjectType zt = ztm.get(faid);
					rtn.put(x.getId(), zt);
				}
			}
		}
		return rtn;
	}
	
	
	/**
	 *	初始化默认方案（新增核算对象时调用）
	 * @param deviceType 设备类型
	 * @return
	 */
	@Override
	public String initDefaultProgramItem(String deviceType) {
		String result = "";
		if(StringUtils.isNotEmpty(deviceType)) {
			ProgramQueryDto queryDto = new ProgramQueryDto();
			queryDto.setDeviceType(deviceType);
			List<ProgramItem> queryList = this.getProgramItemList(queryDto);
			if (queryList!=null) { //无异常
				if(queryList.size()==0) { //没有方案数据
					// 查询运行状态
					String deviceStatus = "";
					List<ProjectType> typeList = projectTypeService.getData();
					if(StringUtils.isNotEmpty(typeList)) {
						for (int i = 0; i < typeList.size(); i++) {
							ProjectType typeObj = typeList.get(i);
							int isDef = typeObj.getISDefault();
							if(isDef==1) {
								deviceStatus = typeObj.getId();
							}
						}
					}
					//生成默认方案
					ProgramClassItemVo saveObj = new ProgramClassItemVo();
					saveObj.setPId("root");
					saveObj.setPiName("默认方案");
					saveObj.setNodeName(saveObj.getPiName());
					saveObj.setMemo("");
					saveObj.setDeviceType(deviceType);
					saveObj.setDeviceStatus(deviceStatus); //设备状态
					saveObj.setTmUsed(1);
					saveObj.setTmSort(1);
					result = this.saveProgramItemData("save", saveObj);	
				}
			}
		}
		return result;
	}
	
	
	/**
	 *	获取后续工序下拉框数据
	 * @param unitId  刨除核算对象的单元类型
	 * @return
	 */
	@Override
	public List<ComboVo> getNextUnitIdList(String unitId) {
		List<ComboVo> result = new ArrayList<ComboVo>();
		if(StringUtils.isNotEmpty(unitId)) {
			Costuint obj = unitMethodService.getCostuintById(unitId);
			if(StringUtils.isNotNull(obj)) {
				String unittype_not = obj.getUnittype();
				if(StringUtils.isNotEmpty(unittype_not)) {
					MethodQueryDto dto = new MethodQueryDto();
					dto.setUnittype_not(unittype_not);
					dto.setProductive(0);
					List<Costuint> unitList = unitMethodService.getCostuintList(dto);
					if(StringUtils.isNotEmpty(unitList)) {
						for (int i = 0; i < unitList.size(); i++) {
							Costuint unitObj = unitList.get(i);
							String unitId_o = unitObj.getId();
							String unitName_o = unitObj.getName();
							String unittype_o = unitObj.getUnittype();
							ComboVo vo = new ComboVo();
							vo.setValue(unitId_o);
							vo.setLabel(unitName_o);
							vo.setUnittype(unittype_o);
							result.add(vo);
						}
					}
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取后续方案下拉框数据
	 * @param unittype  核算单元类型
	 * @return
	 */
	@Override
	public List<ComboVo> getNextProgramIdList(String unittype) {
		List<ComboVo> result = new ArrayList<ComboVo>();
		if(StringUtils.isNotEmpty(unittype)) {
			ProgramQueryDto dto = new ProgramQueryDto();
			dto.setDeviceType(unittype);
			List<ProgramItem> programList = this.getProgramItemList(dto);
			if(StringUtils.isNotEmpty(programList)) {
				for (int i = 0; i < programList.size(); i++) {
					ProgramItem programObj = programList.get(i);
					String programId = programObj.getId();
					String programName = programObj.getPiName();
					ComboVo vo = new ComboVo();
					vo.setValue(programId);
					vo.setLabel(programName);
					result.add(vo);
				}
			}
		}
		return result;
	}
	
	
	/**
	 *	获取方案的后续工序相关信息
	 * @param unitId  核算对象id
	 * @param programId  方案id
	 * @param dateStr  日期
	 * @return
	 */
	@Override
	public List<ProgramNextInfoVo> getProgramNextInfoList(String unitId, String programId, String dateStr) {
		List<ProgramNextInfoVo> result = new ArrayList<ProgramNextInfoVo>();
		if(StringUtils.isNotEmpty(programId)) {
			//获取后续工序、方案、接收人等信息
			List<String> pIdList = new ArrayList<String>();
			pIdList.add(programId);
			List<ProgramNextInfoVo> nextInfoVoList = this.getProgramNextInfoVoList(pIdList);
			if(StringUtils.isNotEmpty(nextInfoVoList)) {
				//方案版本Map
				HashMap<String, String> programId_ver_map = new HashMap<String, String>();
				//工艺卡数据Map
				Map<String, List<OperationCardFormVo>> craftCardMap = new HashMap<String, List<OperationCardFormVo>>();
				//根据后续方案获取工艺卡
				List<String> nextProgramIdList = new ArrayList<String>();
				for (int i = 0; i < nextInfoVoList.size(); i++) {
					ProgramNextInfoVo vo = nextInfoVoList.get(i);
					String nextProgramId = vo.getNextProgramId();
					if(!nextProgramIdList.contains(nextProgramId)) {
						nextProgramIdList.add(nextProgramId);
					}
				}
				if(StringUtils.isNotEmpty(nextProgramIdList)) { //获取各方案最大版本号
					List<String> verList = new ArrayList<String>();
					ProgramLibraryCostQueryDto dto = new ProgramLibraryCostQueryDto();
					dto.setProgramIdList(nextProgramIdList);
					dto.setMaxPversion(dateStr);
					List<ProgramVersion> list = programLibraryCostService.getProgramVersionList(dto);
					if (StringUtils.isNotEmpty(list)) {
						for (int i = 0; i < list.size(); i++) {
							ProgramVersion verObj = list.get(i);
							String projectDataId = verObj.getProjectDataId();
							String verId = verObj.getId();
							if(!programId_ver_map.containsKey(projectDataId)) {
								programId_ver_map.put(projectDataId, verId);
								verList.add(verId);
							}
						}
					}
					if (StringUtils.isNotEmpty(verList)) { //查询工艺卡数据
						CraftCardQueryDto cardDto = new CraftCardQueryDto();
						cardDto.setPvIdList(verList);
						cardDto.setSelType("pic");
						List<OperationCardFormVo> craftCardList = craftCardService.getCraftCardList(cardDto);
						if(StringUtils.isNotEmpty(craftCardList)) {
							craftCardMap = craftCardList.stream().collect(Collectors.groupingBy(OperationCardFormVo::getPvId, Collectors.toList()));
						}
					}
				}
				if(StringUtils.isNotEmpty(craftCardMap)&&StringUtils.isNotEmpty(programId_ver_map)) {
					for (int i = 0; i < nextInfoVoList.size(); i++) {
						ProgramNextInfoVo vo = nextInfoVoList.get(i);
						String nextProgramId = vo.getNextProgramId();
						if(programId_ver_map.containsKey(nextProgramId)) {
							String verId = programId_ver_map.get(nextProgramId);
							if(craftCardMap.containsKey(verId)) {
								List<OperationCardFormVo> craftCardList = craftCardMap.get(verId);
								if(StringUtils.isNotEmpty(craftCardList)) {
									vo.setCraftCardList(craftCardList);
								}
							}
						}
						result.add(vo);
					}
				}else {
					result.addAll(nextInfoVoList);
				}
			}
		}
		return result;
	}
	
	/**
	 *	获取后续方案数据（单表查询）
	 * @param programIdList  方案id列表
	 * @return
	 */
	@Override
	public List<ProgramNextInfo> getProgramNextInfoDataList(List<String> programIdList) {
		List<ProgramNextInfo> result = new ArrayList<ProgramNextInfo>();
		try {
			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(programIdList)) {
				if(programIdList.size()==1) {
					where.eq(ProgramNextInfo::getPId, programIdList.get(0));
				}else {
					where.in(ProgramNextInfo::getPId, programIdList.toArray());
				}
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(ProgramNextInfo::getPId);
			order.orderByAsc(ProgramNextInfo::getTmSort);
			List<ProgramNextInfo> list = entityService.queryData(ProgramNextInfo.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				result = list;
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	/**
	 *	获取后续方案数据（包括接收人等数据）
	 * @param programIdList  方案id列表
	 * @return
	 */
	@Override
	public List<ProgramNextInfoVo> getProgramNextInfoVoList(List<String> programIdList) {
		List<ProgramNextInfoVo> result = new ArrayList<ProgramNextInfoVo>();
		try {
			if (StringUtils.isNotEmpty(programIdList)) {
				List<ProgramNextInfo> list = this.getProgramNextInfoDataList(programIdList);
				if (StringUtils.isNotEmpty(list)) {
					List<String> nextUnitIdList = new ArrayList<String>();
					List<String> nextProgramIdList = new ArrayList<String>();
					List<String> nextIdList = new ArrayList<String>();
					for (int i = 0; i < list.size(); i++) {
						ProgramNextInfo obj = list.get(i);
						String nextUnitId = obj.getNextUnitId();
						String nextProgramId = obj.getNextProgramId();
						String nextId = obj.getId();
						if(!nextUnitIdList.contains(nextUnitId)) {
							nextUnitIdList.add(nextUnitId);
						}
						if(!nextProgramIdList.contains(nextProgramId)) {
							nextProgramIdList.add(nextProgramId);
						}
						if(!nextIdList.contains(nextId)) {
							nextIdList.add(nextId);
						}
					}
					//核算单元
					Map<String, Costuint> unitMap = new HashMap<String, Costuint>();
					if(StringUtils.isNotEmpty(nextUnitIdList)) {
						MethodQueryDto dto = new MethodQueryDto();
						dto.setIdList(nextUnitIdList);
						dto.setProductive(0);
						List<Costuint> unitList = unitMethodService.getCostuintList(dto);
						if(StringUtils.isNotEmpty(unitList)) {
							unitMap = unitList.stream().collect(Collectors.toMap(Costuint::getId, Function.identity()));
						}
					}
					//方案
					Map<String, ProgramItem> programMap = new HashMap<String, ProgramItem>();
					if(StringUtils.isNotEmpty(nextProgramIdList)) {
						ProgramQueryDto dto = new ProgramQueryDto();
						dto.setIdList(nextProgramIdList);
						List<ProgramItem> programList = this.getProgramItemList(dto);
						if(StringUtils.isNotEmpty(programList)) {
							programMap = programList.stream().collect(Collectors.toMap(ProgramItem::getId, Function.identity()));
						}
					}
					//接收人
					Map<String, List<ProgramAcceptUser>> acceptUserMap = new HashMap<String, List<ProgramAcceptUser>>();
					if(StringUtils.isNotEmpty(nextIdList)) {
						List<ProgramAcceptUser> acceptUserList = this.getProgramAcceptUserList(nextIdList);
						if(StringUtils.isNotEmpty(acceptUserList)) {
							acceptUserMap = acceptUserList.stream().collect(Collectors.groupingBy(ProgramAcceptUser::getPId, Collectors.toList()));
						}
					}
					
					for (int i = 0; i < list.size(); i++) {
						ProgramNextInfo obj = list.get(i);
						ProgramNextInfoVo vo = new ProgramNextInfoVo();
						BeanUtils.copyProperties(obj, vo); // 赋予返回对象
						String nextId = vo.getId();
						String nextUnitId = vo.getNextUnitId();
						String nextProgramId = vo.getNextProgramId();
						if(StringUtils.isNotEmpty(unitMap)&&unitMap.containsKey(nextUnitId)) {
							String name = unitMap.get(nextUnitId).getName();
							vo.setNextUnitName(name);
						}
						if(StringUtils.isNotEmpty(programMap)&&programMap.containsKey(nextProgramId)) {
							String name = programMap.get(nextProgramId).getPiName();
							vo.setNextProgramName(name);
						}
						if(StringUtils.isNotEmpty(acceptUserMap)&&acceptUserMap.containsKey(nextId)) {
							List<ProgramAcceptUser> userList = acceptUserMap.get(nextId);
							vo.setAcceptUserList(userList);
						}
						result.add(vo);
					}
				}
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	/**
	 *	获取后续方案的接收人
	 * @param pIdList  后续方案的数据id列表
	 * @return
	 */
	@Override
	public List<ProgramAcceptUser> getProgramAcceptUserList(List<String> pIdList) {
		List<ProgramAcceptUser> result = new ArrayList<ProgramAcceptUser>();
		try {
			// 检索条件
			Where where = Where.create();
			if (StringUtils.isNotEmpty(pIdList)) {
				if(pIdList.size()==1) {
					where.eq(ProgramAcceptUser::getPId, pIdList.get(0));
				}else {
					where.in(ProgramAcceptUser::getPId, pIdList.toArray());
				}
			}
			// 排序
			Order order = Order.create();
			order.orderByAsc(ProgramAcceptUser::getPId);
			order.orderByAsc(ProgramAcceptUser::getTmSort);
			List<ProgramAcceptUser> list = entityService.queryData(ProgramAcceptUser.class, where, order, null);
			if (StringUtils.isNotEmpty(list)) {
				HashMap<String, EmployeeVo> empMap = new HashMap<String, EmployeeVo>();
				List<String> userIdList = list.stream().filter(item -> StringUtils.isNotEmpty(item.getUserId())).map(item -> item.getUserId()).collect(Collectors.toList());
				if (StringUtils.isNotEmpty(userIdList)) {
		            List<EmployeeVo> employeeList = empService.getEmployee(userIdList);
		            if (StringUtils.isNotEmpty(employeeList)) {
		                for (int i = 0; i < employeeList.size(); i++) {
		                    EmployeeVo empVo = employeeList.get(i);
		                    String empTmuid = empVo.getEmpTmuid();
		                    if(!empMap.containsKey(empTmuid)) {
		                        empMap.put(empTmuid, empVo);
		                    }
		                }
		            }
				}
				if(StringUtils.isNotEmpty(empMap)) {
					for (int i = 0; i < list.size(); i++) {
						ProgramAcceptUser userObj = list.get(i);
	                    String userId = userObj.getUserId();
	                    if(empMap.containsKey(userId)) {
	                    	String userName = empMap.get(userId).getEmpname();
	                    	userObj.setUserName(userName);
	                    }
	                    result.add(userObj);
	                }
				}else {
					result = list;
				}
			}
		} catch (Exception e) {
			result = null;
		}
		return result;
	}
	
	/**
	 *	保存后续工序、方案、接收人信息
	 * @param pId 方案id
	 * @param saveList  保存数据
	 * @return
	 */
	@Override
	public String saveProgramNextInfoData(String pId, List<ProgramNextInfoVo> saveList) {
		String result = "";
		List<ProgramNextInfo> addInfoList = new ArrayList<ProgramNextInfo>();
		List<ProgramNextInfo> updInfoList = new ArrayList<ProgramNextInfo>();
		List<ProgramNextInfo> delInfoList = new ArrayList<ProgramNextInfo>();
		List<ProgramAcceptUser> addUserList = new ArrayList<ProgramAcceptUser>();
		List<ProgramAcceptUser> updUserList = new ArrayList<ProgramAcceptUser>();
		List<ProgramAcceptUser> delUserList = new ArrayList<ProgramAcceptUser>();
		if(StringUtils.isNotEmpty(pId)) {
			Map<String, ProgramNextInfoVo> nextInfoVoMap = new HashMap<String, ProgramNextInfoVo>();
			List<String> pIdList = new ArrayList<String>();
			pIdList.add(pId);
			List<ProgramNextInfoVo> nextInfoVoList = this.getProgramNextInfoVoList(pIdList);
			if (StringUtils.isNotEmpty(nextInfoVoList)) {
				nextInfoVoMap = nextInfoVoList.stream().collect(Collectors.toMap(ProgramNextInfoVo::getId, Function.identity()));
			}
			if (StringUtils.isNotEmpty(saveList)) {
				for (int i = 0; i < saveList.size(); i++) {
					ProgramNextInfoVo saveObj = saveList.get(i);
					String id = saveObj.getId();
					if(StringUtils.isNotEmpty(nextInfoVoMap)&&nextInfoVoMap.containsKey(id)) { //修改
						//已存在记录
						ProgramNextInfoVo nextInfoVo = nextInfoVoMap.get(id);
						List<ProgramAcceptUser> acceptUserList = nextInfoVo.getAcceptUserList();
						Map<String, ProgramAcceptUser> acceptUserMap = new HashMap<String, ProgramAcceptUser>();
						if (StringUtils.isNotEmpty(acceptUserList)) {
							acceptUserMap = acceptUserList.stream().collect(Collectors.toMap(ProgramAcceptUser::getId, Function.identity()));
						}
						//后续工序、方案等信息
						ProgramNextInfo infoObj = new ProgramNextInfo();
						BeanUtils.copyProperties(saveObj, infoObj); // 赋予返回对象
						infoObj.setTmSort(i+1);
						updInfoList.add(infoObj);
						//接收人
						List<ProgramAcceptUser> saveUserList = saveObj.getAcceptUserList();
						if(StringUtils.isNotEmpty(saveUserList)) {
							for (int j = 0; j < saveUserList.size(); j++) {
								ProgramAcceptUser saveUserObj = saveUserList.get(j);
								String userSaveId = saveUserObj.getId();
								if(StringUtils.isNotEmpty(acceptUserMap)&&StringUtils.isNotEmpty(userSaveId)&&acceptUserMap.containsKey(userSaveId)) { //修改
									ProgramAcceptUser userObj = new ProgramAcceptUser();
									BeanUtils.copyProperties(saveUserObj, userObj); // 赋予返回对象
									userObj.setTmSort(j+1);
									userObj.setPId(id);
									updUserList.add(userObj);
									acceptUserMap.remove(userSaveId);
								}else {
									ProgramAcceptUser userObj = new ProgramAcceptUser();
									BeanUtils.copyProperties(saveUserObj, userObj); // 赋予返回对象
									userObj.setId(TMUID.getUID());
									userObj.setTmSort(j+1);
									userObj.setPId(id);
									addUserList.add(userObj);
								}
							}
						}
						if(StringUtils.isNotEmpty(acceptUserMap)) { //删除剩余的接收人数据
							Iterator<Map.Entry<String, ProgramAcceptUser>> iterMap = acceptUserMap.entrySet().iterator();
							while (iterMap.hasNext()) {
								Map.Entry<String, ProgramAcceptUser> entryMap = iterMap.next();
								ProgramAcceptUser delUserObj = entryMap.getValue();
								delUserList.add(delUserObj);
							}
						}
						nextInfoVoMap.remove(id);
					}else { //新增
						//后续工序、方案等信息
						ProgramNextInfo infoObj = new ProgramNextInfo();
						BeanUtils.copyProperties(saveObj, infoObj); // 赋予返回对象
						String newId = TMUID.getUID();
						infoObj.setId(newId);
						infoObj.setTmSort(i+1);
						infoObj.setPId(pId);
						addInfoList.add(infoObj);
						//接收人
						List<ProgramAcceptUser> saveUserList = saveObj.getAcceptUserList();
						if(StringUtils.isNotEmpty(saveUserList)) {
							for (int j = 0; j < saveUserList.size(); j++) {
								ProgramAcceptUser saveUserObj = saveUserList.get(j);
								saveUserObj.setId(TMUID.getUID());
								saveUserObj.setTmSort(j+1);
								saveUserObj.setPId(newId);
								addUserList.add(saveUserObj);
							}
						}
					}
				}
			}
			if(StringUtils.isNotEmpty(nextInfoVoMap)) {
				Iterator<Map.Entry<String, ProgramNextInfoVo>> iterMap = nextInfoVoMap.entrySet().iterator();
				while (iterMap.hasNext()) {
					Map.Entry<String, ProgramNextInfoVo> entryMap = iterMap.next();
					ProgramNextInfoVo infoObj = entryMap.getValue();
					List<ProgramAcceptUser> userList = infoObj.getAcceptUserList();
					ProgramNextInfo delObj = new ProgramNextInfo();
					BeanUtils.copyProperties(infoObj, delObj); // 赋予返回对象
					delInfoList.add(delObj);
					if(StringUtils.isNotEmpty(userList)) {
						delUserList.addAll(userList);
					}
				}
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(addInfoList)) {
			if (entityService.insertBatch(addInfoList) == 0) {
				result = "添加失败（后续工序、方案）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(updInfoList)) {
			if (entityService.updateByIdBatch(updInfoList) == 0) {
				result = "修改失败（后续工序、方案）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(delInfoList)) {
			if (entityService.deleteByIdBatch(delInfoList) == 0) {
				result = "删除失败（后续工序、方案）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(addUserList)) {
			if (entityService.insertBatch(addUserList) == 0) {
				result = "添加失败（后续方案接收人）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(updUserList)) {
			if (entityService.updateByIdBatch(updUserList) == 0) {
				result = "修改失败（后续方案接收人）！";
			}
		}
		if(StringUtils.isEmpty(result) && StringUtils.isNotEmpty(delUserList)) {
			if (entityService.deleteByIdBatch(delUserList) == 0) {
				result = "删除失败（后续方案接收人）！";
			}
		}
		return result;
	}
	
	/**
	 *	保存方案数据
	 * @param addList
	 * @param updList
	 * @param delList
	 * @return
	 */
	@Override
	public String saveDataProgramItem(List<ProgramItem> addList, List<ProgramItem> updList, List<ProgramItem> delList) {
		String result = "";
		if ("".equals(result) && StringUtils.isNotEmpty(addList)) {
			if (entityService.insertBatch(addList) == 0) {
				result = "添加失败（方案）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(updList)) {
			if (entityService.updateByIdBatch(updList) == 0) {
				result = "更新失败（方案）！";
			}
		}
		if ("".equals(result) && StringUtils.isNotEmpty(delList)) {
			if (entityService.deleteByIdBatch(delList) == 0) {
				result = "删除失败（方案）！";
			}
		}
		return result;
	}
	
}
