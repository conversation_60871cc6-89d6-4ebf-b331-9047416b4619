package com.yunhesoft.leanCosting.rtdbShow.scheduler;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.leanCosting.programConfig.service.IProgramLibraryCostService;
import com.yunhesoft.leanCosting.rtdbShow.service.IRtdbShowService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.system.employee.service.impl.EmployeeBasicOperationImpl;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.msg.service.IMessageService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 实时数据预警调度
 * 
 * <AUTHOR>
 *
 */
@Component
@Log4j2
public class RtdbWarningScheduleJobHandler {

	@Autowired
	private IRtdbShowService rtdbSrv;

	@Autowired
	private RedisUtil redis;

	@Autowired
	private EntityService dao;

	@Autowired
	private IProductScheduPlanService proScheduSrv;


	@Autowired
	private UnitItemInfoService uiSrv;


	@Autowired
	private IProgramLibraryCostService prolibSrv;

	@Autowired
	private EmployeeBasicOperationImpl empSrv;

	@Autowired
	private IMessageService msgSer;

	@XxlJob("tm4CommCostRtdbWarningScheduleJobHandler")
	public void tm4CommCostRtdbWarningScheduleJobHandler() throws Exception {}

}