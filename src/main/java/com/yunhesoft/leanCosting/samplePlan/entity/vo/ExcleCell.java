package com.yunhesoft.leanCosting.samplePlan.entity.vo;

import lombok.Data;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 样品计划导出表头$
 * @date 2022/2/10
 */
@Data
public class ExcleCell {
    // 内容名称
    private String value;
    //表头属性
    private String property;
    //单元格类别   0主表头  1子表头 2主数据 3子数据
    private int type;
    //单元格位置
    private int index;
    private HSSFWorkbook workbook;
    //表头样式
    private HSSFCellStyle style;
    private HSSFFont font;
    private static final List<String> centerList = Arrays.asList("no", "orderNumber", "unit", "status");

    public ExcleCell(String value, String property, int type, int index, HSSFWorkbook workbook, HSSFCellStyle style, HSSFFont font) {
        this.value = value;
        this.property = property;
        this.type = type;
        this.index = index;
        this.workbook = workbook;
        //设置单元格样式
        if (type == 0) {
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            //设置背景色
            style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //设置字体名称
            font.setFontName("黑体");
            //设置字号
            font.setFontHeightInPoints((short) 14);
            font.setBold(true);
            style.setFont(font);

        } else if (type == 1) {
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            //设置背景色
            style.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //设置字体名称
            font.setFontName("黑体");
            //设置字号
            font.setFontHeightInPoints((short) 12);
            font.setBold(true);
            style.setFont(font);
        } else if (type == 2) {
            //设置背景色
            style.setFillForegroundColor(IndexedColors.LIGHT_TURQUOISE1.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setWrapText(true);
            //设置字体名称
            font.setFontName("宋体");
            //设置不同状态的字体颜色
            if ("status".equals(property)) {
                font.setBold(true);
                switch (value) {
                    case "已下发":
                        font.setColor(IndexedColors.LIGHT_BLUE.getIndex());
                        break;
                    case "生产中":
                        font.setColor(IndexedColors.GOLD.getIndex());
                        break;
                    case "已完成":
                        font.setColor(IndexedColors.BRIGHT_GREEN.getIndex());
                        break;
                    default:
                        break;
                }
            }
            //设置字号
            font.setFontHeightInPoints((short) 10);
            font.setBold(false);
            style.setFont(font);
        } else if (type == 3) {
            //设置背景色
            style.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            style.setAlignment(HorizontalAlignment.CENTER);
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setWrapText(true);
            //设置字体名称
            font.setFontName("宋体");
            //设置不同状态的字体颜色
            if ("status".equals(property)) {
                font.setBold(true);
                switch (value) {
                    case "审核驳回":
                        font.setColor(IndexedColors.RED.getIndex());
                        break;
                    case "待设计":
                        font.setColor(IndexedColors.LIGHT_ORANGE.getIndex());
                        break;
                    case "待审核":
                        font.setColor(IndexedColors.ORANGE.getIndex());
                        break;
                    case "待生产":
                        font.setColor(IndexedColors.PINK.getIndex());
                        break;
                    case "已完成":
                        font.setColor(IndexedColors.BRIGHT_GREEN.getIndex());
                        break;
                    default:
                        break;
                }
            }
            //设置字号
            font.setFontHeightInPoints((short) 9);
            font.setBold(false);
            style.setFont(font);
        }
        this.style = style;
    }
}
