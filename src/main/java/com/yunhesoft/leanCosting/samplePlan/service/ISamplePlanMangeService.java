package com.yunhesoft.leanCosting.samplePlan.service;

import com.yunhesoft.leanCosting.samplePlan.entity.dto.SaveDto;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.SearchSamplePlanDto;
import com.yunhesoft.leanCosting.samplePlan.entity.dto.SelectSamplePlanDto;
import com.yunhesoft.leanCosting.samplePlan.entity.po.SamplePlanManagement;
import com.yunhesoft.system.kernel.service.model.Pagination;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @Description: 样品计划管理接口服务
 * @date 2021/09/10
 */
public interface ISamplePlanMangeService {

    /**
     * 批量添加样品计划.
     *
     * @param
     * @return
     */
    Boolean addSamplePlan(List<SamplePlanManagement> orders);

    /**
     * 批量删除样品计划.
     *
     * @param
     * @return
     */
    Boolean delSamplePlan(List<SamplePlanManagement> dels);

    /**
     * 批量更新样品计划.
     *
     * @param
     * @return
     */
    Boolean updateSamplePlan(List<SamplePlanManagement> orders);

    /**
     * 分页查询样品计划.
     *
     * @param
     * @return
     */
    List<SamplePlanManagement> selectPageSamplePlan(Pagination<?> page, SelectSamplePlanDto param);

    /**
     * 保存数据.
     *
     * @param
     * @return
     */
    Boolean saveSamplePlan(SaveDto param);

    /**
     * 查询总数.
     *
     * @param
     * @return
     */
    int samplePlanTotal();

    /**
     * 搜索样品计划.
     *
     * @param
     * @return
     */
    List<SamplePlanManagement> searchSamplePlan(Pagination<?> page, SearchSamplePlanDto searchDto);

    /**
     * 按样品计划日期月份搜索.
     *
     * @param
     * @return
     */
    List<SamplePlanManagement> searchSamplePlanByMonth(String month);

    /**
     * 查询搜索条件下总数.
     *
     * @param
     * @return
     */
    int samplePlanTotalSearch(SearchSamplePlanDto searchDto);

    /**
     * 验重.
     *
     * @param
     * @return
     */
    Boolean isRepeat(String[] planNo);

    /**
     * 切换状态.
     *
     * @param
     * @return
     */
    Boolean switchSamplePlanStatus(String id, Integer statusCode);

    /**
     * 产品状态改变时 修改样品计划状态
     *
     * @param
     * @return
     * <AUTHOR>
     */
    public Boolean changeSamplePlanStatusBypro(String id);

    /**
     * 导出样品计划数据
     *
     * @param
     * @param searchDto
     * @return
     */
    void exportSamplePlanData(SearchSamplePlanDto searchDto, HttpServletResponse response);

}
