package com.yunhesoft.leanCosting.steadyRate.scheduler;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.system.kernel.druid.MultiTenantUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.multiTenant.entity.po.SysMultiTenant;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * 平稳率调度
 * 
 * <AUTHOR>
 *
 */
@Component
@Log4j2
public class SteadyRateScheduleJobHandler {

	@Autowired
	private EntityService dao;
	@Autowired
	private ISteadyRateServise steadyRateSrv;

	@XxlJob("tm4SteadyRateScheduleJobHandler")
	public void tm4SteadyRateScheduleJobHandler() throws Exception {
		String param = XxlJobHelper.getJobParam();
		//每几分钟执行一次

//		String now = DateTimeUtils.getNowDateTimeStr();
//		int lastIndex = now.lastIndexOf(":");
//		now = now.substring(0, lastIndex) + ":00";


		SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.SECOND,0); //这是将当天的【秒】设置为0
		String now = sdfYMD.format(calendar.getTime());

		if (MultiTenantUtils.enalbe()) {
			//开启多租户
			List<SysMultiTenant> list = dao.queryListDisableTenant(SysMultiTenant.class, Where.create().eq(SysMultiTenant::getTmused, 1), Order.create().orderByDesc(SysMultiTenant::getCreateTime));
			if (StringUtils.isEmpty(list)) {
				return;
			}
			for (SysMultiTenant t : list) {
				steadyRateSrv.calcSteadyRate(t.getTenant_id(), now);
			}
		} else {
			steadyRateSrv.calcSteadyRate(null, now);
		}

	}

	public void tm4SteadyRateScheduleJobHandler(String tenantId) throws Exception {
		String now = DateTimeUtils.getNowDateTimeStr();
		steadyRateSrv.calcSteadyRate(tenantId, now);
	}

//	public static void main(String[] args) {
//		SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//		Calendar calendar = Calendar.getInstance();
//		calendar.set(Calendar.SECOND,0); //这是将当天的【秒】设置为0
//		String ymd = sdfYMD.format(calendar.getTime());
//		System.out.println(ymd);
//	}

}