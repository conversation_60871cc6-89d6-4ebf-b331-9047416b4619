package com.yunhesoft.leanCosting.steadyRate.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.productSchedu.entity.dto.CostProgStartTimeDto;
import com.yunhesoft.leanCosting.productSchedu.entity.vo.ProductUnitProgShift;
import com.yunhesoft.leanCosting.productSchedu.service.IProductScheduPlanService;
import com.yunhesoft.leanCosting.productSchedu.service.IZzRunStateService;
import com.yunhesoft.leanCosting.programConfig.entity.po.ProgramContingencyPlan;
import com.yunhesoft.leanCosting.steadyRate.entity.po.CostWarningDataInfo;
import com.yunhesoft.leanCosting.steadyRate.entity.po.CostWarningDataMsg;
import com.yunhesoft.leanCosting.steadyRate.entity.po.SteadyRateData;
import com.yunhesoft.leanCosting.steadyRate.entity.vo.TeamSteadyRateTotalVo;
import com.yunhesoft.leanCosting.steadyRate.service.ISteadyRateServise;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.IndicatorInfo;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.employee.service.impl.EmployeeBasicOperationImpl;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.msg.entity.dto.MsgObject;
import com.yunhesoft.system.msg.entity.dto.MsgObjectRet;
import com.yunhesoft.system.msg.service.IMessageService;
import com.yunhesoft.system.tds.service.IRtdbService;
import com.yunhesoft.system.tools.todo.service.TodoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SteadyRateServiceImpl implements ISteadyRateServise {

    @Autowired
    private EntityServiceImpl dao;

    @Autowired
    private IProductScheduPlanService proScheduSrv; //方案

    @Autowired
    private UnitItemInfoService itemSrv;  //指标

    @Autowired
    private IRtdbService rtdbSrv;

//    @Autowired
//    private IRtdbShowService rtdbShowSrv;

    @Autowired
    private EmployeeBasicOperationImpl empSrv;

    @Autowired
    private RedisUtil redis;

    @Autowired
    public RedisTemplate redisTemplate;

    @Autowired
    private IMessageService msgSrv;

    @Autowired
    private TodoService todoService;
    
    @Autowired
	private IZzRunStateService runStateService; // 运行状态


    /**
     * 获取班组平稳率统计
     * @param unitId    核算对象
     * @param startDt   开始时间
     * @param endDt     结束时间
     * @return
     */
    public HashMap<String,List<TeamSteadyRateTotalVo>> getTeamSteadyRateTotal (String unitId, String startDt, String endDt,
    		HashMap<String,Costunitsampledot> dotm) {
        if (StringUtils.isAnyEmpty(unitId, startDt, endDt)) {
            //校验参数，任何一个为空都直接返回
            return null;
        }
        //班组的上班时间>=开始时间 and 下班时间<=截止时间
        Where where = Where.create();
        where.eq(SteadyRateData::getUnitId, unitId);
        where.gt(SteadyRateData::getShiftStartTime, startDt);
        where.lt(SteadyRateData::getShiftEndTime, endDt);
        List<SteadyRateData> list = dao.queryData(SteadyRateData.class, where, null, null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        Integer zpwl;
        String ybid;
        HashMap<String,List<TeamSteadyRateTotalVo>> rtn = new HashMap<String,List<TeamSteadyRateTotalVo>>();
        HashMap<String,HashMap<String,TeamSteadyRateTotalVo>> valm = new HashMap<String,HashMap<String,TeamSteadyRateTotalVo>>();
        LinkedHashMap<String, TeamSteadyRateTotalVo> map = new LinkedHashMap<>();
        for (SteadyRateData data : list) {
        	ybid=data.getItemId();
        	zpwl=0;
        	if (dotm.containsKey(ybid)) {
        		Costunitsampledot di=dotm.get(ybid);
        		if (di!=null) {
        			zpwl=di.getUseTo();
        			if (zpwl==null) {
        				zpwl=1;
        			}
        		}
        	}
        	String teamId = data.getOrgCode();
        	Double fetchValue=Optional.ofNullable(data.getSumValue()).orElse(0.0);
        	Integer totalCount = Optional.ofNullable(data.getTotalCount()).orElse(0);
        	Integer overUpLimit = Optional.ofNullable(data.getOverUpLimit()).orElse(0);
        	Integer overLowLimit = Optional.ofNullable(data.getOverLowLimit()).orElse(0);
        	Integer delUpLimit = Optional.ofNullable(data.getDelUpLimit()).orElse(0);
        	Integer delLowLimit = Optional.ofNullable(data.getDelLowLimit()).orElse(0);
        	if (valm.containsKey(ybid)) {
        		HashMap<String,TeamSteadyRateTotalVo> mm=valm.get(ybid);
        		if (mm!=null) {
        			if (mm.containsKey(teamId)) {
        				TeamSteadyRateTotalVo vo=mm.get(teamId);
        				vo.setFetchValue(vo.getFetchValue()+fetchValue);
        				vo.setTotalCount(vo.getTotalCount()+totalCount);
                        vo.setOverUpLimit(vo.getOverUpLimit()+overUpLimit);
                        vo.setOverLowLimit(vo.getOverLowLimit()+overLowLimit);
                        vo.setDelUpLimit(vo.getDelUpLimit()+delUpLimit);
                        vo.setDelLowLimit(vo.getDelLowLimit()+delLowLimit);
        			} else {
        				TeamSteadyRateTotalVo vo=new TeamSteadyRateTotalVo();
                		vo.setUnitId(unitId);
                		vo.setOrgCode(teamId);
                		vo.setFetchValue(fetchValue);
                		vo.setTotalCount(0.0+totalCount);
                        vo.setOverUpLimit(0.0+overUpLimit);
                        vo.setOverLowLimit(0.0+overLowLimit);
                        vo.setDelUpLimit(0.0+delUpLimit);
                        vo.setDelLowLimit(0.0+delLowLimit);
                        mm.put(teamId, vo);
        			}
        		}
        	} else {
        		TeamSteadyRateTotalVo vo = new TeamSteadyRateTotalVo();
        		vo.setUnitId(unitId);
        		vo.setOrgCode(teamId);
        		vo.setFetchValue(fetchValue);
        		vo.setTotalCount(0.0+totalCount);
                vo.setOverUpLimit(0.0+overUpLimit);
                vo.setOverLowLimit(0.0+overLowLimit);
                vo.setDelUpLimit(0.0+delUpLimit);
                vo.setDelLowLimit(0.0+delLowLimit);
                HashMap<String,TeamSteadyRateTotalVo> mm=new HashMap<String,TeamSteadyRateTotalVo>();
                mm.put(teamId, vo);
                valm.put(ybid, mm);
        	}
        	if (zpwl==1) {
				// 只要用于总平稳率计算的指标
                TeamSteadyRateTotalVo vo = map.computeIfAbsent(teamId, k -> {
                    TeamSteadyRateTotalVo bean = new TeamSteadyRateTotalVo();
                    bean.setUnitId(data.getUnitId());
                    bean.setOrgCode(teamId);
                    bean.setTotalCount(0.0);
                    bean.setOverUpLimit(0.0);
                    bean.setOverLowLimit(0.0);
                    bean.setDelUpLimit(0.0);
                    bean.setDelLowLimit(0.0);
                    return bean;
                });
                vo.setTotalCount(vo.getTotalCount()+totalCount);
                vo.setOverUpLimit(vo.getOverUpLimit()+overUpLimit);
                vo.setOverLowLimit(vo.getOverLowLimit()+overLowLimit);
                vo.setDelUpLimit(vo.getDelUpLimit()+delUpLimit);
                vo.setDelLowLimit(vo.getDelLowLimit()+delLowLimit);
        	}
        }
        //普通采集点
        for (Map.Entry<String, HashMap<String,TeamSteadyRateTotalVo>> xx : valm.entrySet()) {
        	ybid=xx.getKey();
        	HashMap<String,TeamSteadyRateTotalVo> mm=xx.getValue();
        	if (mm!=null) {
        		for (Map.Entry<String, TeamSteadyRateTotalVo> yy : mm.entrySet()) {
        			TeamSteadyRateTotalVo vo = yy.getValue();
        			if (rtn.containsKey(ybid)) {
        				rtn.get(ybid).add(vo);
        			}else {
        				List<TeamSteadyRateTotalVo> rl = new ArrayList<>();
        				rl.add(vo);
        				rtn.put(ybid, rl);
        			}
        		}
        	}
        }
        //总平稳率
        List<TeamSteadyRateTotalVo> result = new ArrayList<>();
        Double zds,csxd,cxxd,pcsxd,pcxxd;
        for (Map.Entry<String, TeamSteadyRateTotalVo> entry : map.entrySet()) {
            TeamSteadyRateTotalVo vo = entry.getValue();
            zds = vo.getTotalCount();
            csxd = vo.getOverUpLimit();
            cxxd = vo.getOverLowLimit();
            pcsxd = vo.getDelUpLimit();
            pcxxd = vo.getDelLowLimit();
            double rate,cxd;
            cxd=csxd + cxxd - pcsxd - pcxxd;
            //if(总点数=0,1,1-(超上限点+超下限点-抛超上限点-抛超下限点)/总点数)
            if (zds.compareTo(0.0)==0) {
                rate = 1.0;
            } else {
            	if (cxd<=0) {
            		 rate = 1.0;
            	}else {
            		 rate = 1.0 - cxd / zds;
            	}
            }
            vo.setOverLowLimit(cxd);
            vo.setRate(rate);
            result.add(vo);
        }
        rtn.put("TotalSteadyRate", result);
        return rtn;
    }

    @Override
    public void calcSteadyRate(String tenantId, String dt) {

        //dt = "2024-03-21 20:29:59";
        //获取当前时间点的核算对象和方案数据和所有的倒班数据
        List<ProductUnitProgShift> shiftList = proScheduSrv.getUnitProgByDatetime(dt, tenantId);
        if (StringUtils.isEmpty(shiftList)) {
            return;
        }

        for (ProductUnitProgShift productUnitProgShift : shiftList) {
            if (StringUtils.isEmpty(productUnitProgShift.getStartTime())) {
                productUnitProgShift.setStartTime(productUnitProgShift.getSbsj());
            }
            if (StringUtils.isEmpty(productUnitProgShift.getEndTime())) {
                productUnitProgShift.setEndTime(dt);
            }
//            if (!"ZQF7SXAB507E41WFMV1259".equalsIgnoreCase(productUnitProgShift.getUnitId())) {
//                continue;
//            }
            //遍历计算每个班次平稳率
            calcShiftSteadyRate(tenantId, productUnitProgShift, dt, true, true);
        }

    }

    /**
     * 获取转发消息待办
     * @param todoType 1 待转发 2 已转发
     * @return
     */
    @Override
    public JSONArray queryWarningMsgTodoDataList(int todoType) {
        SysUser currentUser = SysUserHolder.getCurrentUser();
        if (currentUser == null) {
            return null;
        }

        String userId = currentUser.getId();
        Where msgWhere = Where.create().eq(CostWarningDataMsg::getMsgReceiveUser, userId);
//        msgWhere.eq(CostWarningDataMsg::getMsgSended, 1);
        if (todoType == 1 || todoType == 2) {
            //1待转发 2已转发
            msgWhere.eq(CostWarningDataMsg::getForwardState, todoType);
        }
        List<CostWarningDataMsg> costWarningDataMsgs = dao.queryData(CostWarningDataMsg.class, msgWhere, null, null);
        if (StringUtils.isEmpty(costWarningDataMsgs)) {
            return null;
        }
        Map<String, CostWarningDataMsg> msgMap = new HashMap<>();
        List<String> infoIdList = new ArrayList<>();
        for (CostWarningDataMsg msg : costWarningDataMsgs) {
            String infoId = msg.getInfoId();
            msgMap.put(infoId, msg);
            infoIdList.add(infoId);
        }
        Where where = Where.create().in(CostWarningDataInfo::getId, infoIdList.toArray());
        List<CostWarningDataInfo> list = dao.queryData(CostWarningDataInfo.class, where, Order.create().orderByAsc(CostWarningDataInfo::getCreateTime), null);
        if (StringUtils.isEmpty(list)) {
            return null;
        }
        JSONArray result = new JSONArray();
        for (CostWarningDataInfo info : list) {
            JSONObject obj = new JSONObject();
            obj.put("infoId", info.getId());
            CostWarningDataMsg msg = msgMap.get(info.getId());
            obj.put("msgId", msg.getId());
            obj.put("forwardReceiveUserName", "");
            obj.put("forwardReceiveUser", "");
            obj.put("warningMessage", msg.getWarningMessage());
            obj.put("warningMessageTime", info.getWarningMessageTime());
            int forwardState = msg.getForwardState();
            obj.put("forwardState", forwardState == 1 ? "待转发": (forwardState == 2 ? "已转发" : ""));
            result.add(obj);
        }
        return result;
    }

    /**
     * 转发消息
     * @param id
     * @param userIdList
     * @return
     */
    @Override
    public String forwardWarningMsg(String infoId, String msgId, List<String> userIdList, List<String> userNameList) {
        CostWarningDataInfo info = dao.queryObjectById(CostWarningDataInfo.class, infoId);
        CostWarningDataMsg msgBean = dao.queryObjectById(CostWarningDataMsg.class, msgId);
        if (info == null || msgBean == null) {
            return "找不到记录";
        }

        MsgObject msgObject = new MsgObject();
        msgObject.setModulecode("leanCosting");
        msgObject.setFuncode("CostWarningInfo");
        msgObject.setRecvEmpIds(StringUtils.join(userIdList, ","));
        msgObject.setTitle("预警消息提醒");
        msgObject.setContent(msgBean.getWarningMessage());

        MsgObjectRet ret = msgSrv.sendMsg(msgObject);

        if (!ret.isSuccess()) {
            return "发送失败";
        }
        msgBean.setForwardState(2);
        msgBean.setForwardTime(DateTimeUtils.getNowDate());
        msgBean.setForwardReceiveUser(StringUtils.join(userIdList, ","));
        msgBean.setForwardReceiveUserName(userNameList == null ? null : StringUtils.join(userNameList, ","));
        dao.updateById(msgBean);

        //清除待办缓存
        clearWarningTotoCache();
        return null;
    }


    /**
     * 平稳率补录
     * @param tenantId
     * @param shiftList
     * @return
     */
    @Override
    public String reCalcShiftSteadyRate (String tenantId, List<ProductUnitProgShift> shiftList) {
        String unitid;
    	if (StringUtils.isEmpty(shiftList)) {
            return "传入参数为空";
        } else {
        	unitid=shiftList.get(0).getUnitId();
        	if (unitid==null) {
        		return "传入核算对象为空";
        	}
        }
        Where where = Where.create();
        Set<String> unitIdSet = new HashSet<>();
        CostProgStartTimeDto dtox = new CostProgStartTimeDto();
		dtox.setUnitId(unitid);
		dtox.setProgChangeFlagStr("正在删除历史平稳率数据");
		runStateService.setProrChangeRedisValue(dtox);
        for (int i=0; i<shiftList.size(); i++) {
            ProductUnitProgShift shift = shiftList.get(i);
            if (shift.getStartTime() == null) {
                shift.setStartTime(shift.getSbsj());
            }
            if (shift.getEndTime() == null) {
                shift.setEndTime(shift.getXbsj());
            }
            String unitId = shift.getUnitId();
            if (!unitIdSet.contains(unitId)) {
                //删除对应核算对象的redis缓存
                String redisKey = "STEADYRATE:"+unitId;
                redis.delete(redisKey);
                unitIdSet.add(unitId);
            }
            if (i > 0) {
                where.or();
            }
            where.lb();
            where.eq(SteadyRateData::getUnitId, shift.getUnitId());
            where.eq(SteadyRateData::getShiftDate, shift.getTbrq());
            where.eq(SteadyRateData::getShiftCode, shift.getShiftClassCode());
            where.eq(SteadyRateData::getOrgCode, shift.getOrgCode());
            where.rb();
        }

        // TODO 先把涉及到的日期班次全删除掉，后面重新采数
        dao.rawDeleteByWhere(SteadyRateData.class, where);
        // 按方案生效时间排序
        shiftList.sort(Comparator.comparing(ProductUnitProgShift::getStartTime));
        String tbrq,bcmc;
        for (ProductUnitProgShift shift : shiftList) {
        	tbrq = shift.getTbrq();
        	if (tbrq==null) {
        		continue;
        	}
        	bcmc = shift.getShiftClassName();
        	if (bcmc==null) {
        		bcmc="";
        	}
        	CostProgStartTimeDto dto1 = new CostProgStartTimeDto();
			dto1.setUnitId(unitid);
			dto1.setProgChangeFlagStr("正在计算" + tbrq + bcmc + "的平稳率");
			runStateService.setProrChangeRedisValue(dto1);
//            this.calcShiftSteadyRate(tenantId, shift, null, false, false);
            this.calcShiftAllSteadyRate(tenantId, shift, DateTimeUtils.getNowDateTimeStr());
        }
        return null;
    }


    /**
     * 按班次计算平稳率数据
     * @param tenantId
     * @param shift
     * @param dt
     * @param calcWarning
     */
    private void calcShiftSteadyRate (String tenantId, ProductUnitProgShift shift, String dt, boolean calcWarning, boolean useCache) {

        List<SteadyRateData> insertSteadyDataList = new ArrayList<>();
        List<SteadyRateData> updateSteadyDataList = new ArrayList<>();
        List<SteadyRateData> deleteSteadyDataList = new ArrayList<>();

        //方案id
        String programid = shift.getProgramid();
        //核算对象id
        String unitId = shift.getUnitId();
        //机构代码
        String orgCode = shift.getOrgCode();
        String shiftClassCode = shift.getShiftClassCode();
        //上班时间
        String shiftStartTime = shift.getSbsj();
        //下班时间
        String shiftEndTime = shift.getXbsj();

        //方案开始时间
        String kssj = Optional.ofNullable(shift.getStartTime()).orElse(shiftStartTime);
        //方案结束时间
        String jssj = Optional.ofNullable(shift.getEndTime()).orElse(shiftEndTime);

        if (StringUtils.isEmpty(dt)) {
            dt = jssj;
        }

        //查询方案下核算对象的指标
        List<IndicatorInfo> indicatorList = this.getUnitProgramItems(tenantId, shift, dt, useCache);
        if (StringUtils.isEmpty(indicatorList)) {
            return;
        }

        Where where = Where.create();
        where.eq(SteadyRateData::getProgramId, programid);
        where.eq(SteadyRateData::getUnitId, unitId);
        where.eq(SteadyRateData::getOrgCode, orgCode);
        where.eq(SteadyRateData::getShiftCode, shiftClassCode);
        where.eq(SteadyRateData::getShiftDate, shift.getTbrq());
        where.eq(SteadyRateData::getStartTime, kssj);
        where.eq(SteadyRateData::getEndTime, jssj);
        List<SteadyRateData> oldList;
        if (StringUtils.isEmpty(tenantId)) {
            oldList = dao.queryData(SteadyRateData.class, where, null, null);
        } else {
            oldList = dao.rawQueryListByWhereWithTenant(tenantId, SteadyRateData.class, where, null);
        }

        Map<String, String> lastEndTimeMap = new HashMap<>();
        //采集的开始时间
        String rtdbKssj = null;
        Map<String, SteadyRateData> oldDataMap = new HashMap<>();
        if (StringUtils.isNotEmpty(oldList)) {
            for (SteadyRateData v : oldList) {
                oldDataMap.put(v.getUnitId()+"_"+v.getProgramId()+"_"+v.getOrgCode()+"_"+v.getShiftCode()+"_"+v.getStartTime()+"_"+v.getEndTime()+"_"+v.getItemId(), v);
                String lastTime = v.getLastTime();
                if (StringUtils.isEmpty(lastTime)) {
                    continue;
                }
                lastEndTimeMap.put(v.getUnitId() + "_" + v.getItemId(), lastTime);
                //取最小时间作为取数的开始时间
                if (StringUtils.isEmpty(rtdbKssj)) {
                    rtdbKssj = lastTime;
                } else {
                    rtdbKssj = lastTime.compareTo(rtdbKssj) < 0 ? lastTime : rtdbKssj;
                }
            }
        }
        //如果取数开始时间为空则为班次方案开始时间
        rtdbKssj = StringUtils.isEmpty(rtdbKssj) ? kssj : rtdbKssj;

        //获取实时数据（从上次最小时间取到现在）
        Map<String, List<TagData>> rtdbValueMap = this.getRtdbValueMap(indicatorList, rtdbKssj, dt);

        //遍历读取超限和总点数
        for (IndicatorInfo indicator : indicatorList) {
            //采集点id
            String sdid = indicator.getSdid();
            String name = indicator.getName();
            String dataSource = indicator.getDatasource();
            if (StringUtils.isEmpty(dataSource)) {
                continue;
            }

            SteadyRateData data = oldDataMap.get(unitId+"_"+programid+"_"+orgCode+"_"+shiftClassCode+"_"+kssj+"_"+jssj+"_"+sdid);
            String lastTime = Optional.ofNullable(data).map(SteadyRateData::getLastTime).orElse(null);
            String newLastTime = null;
            Double keyUpLimit = indicator.getKeyUpLimit();
            Double keyLowLimit = indicator.getKeyLowLimit();
//            Double operateUpLimit = indicator.getOperateUpLimit();
//            Double operateLowLimit = indicator.getOperateLowLimit();

            int totalCount = Optional.ofNullable(data).map(SteadyRateData::getTotalCount).orElse(0); //总点数
            int overUpLimit = Optional.ofNullable(data).map(SteadyRateData::getOverUpLimit).orElse(0);    //超上限点数
            int overLowLimit = Optional.ofNullable(data).map(SteadyRateData::getOverLowLimit).orElse(0);   //超下限点数
            double overUpLimitDuration = Optional.ofNullable(data).map(SteadyRateData::getOverUpLimitDuration).orElse(0.0);
            double overLowLimitDuration = Optional.ofNullable(data).map(SteadyRateData::getOverLowLimitDuration).orElse(0.0);
            double sumValue = Optional.ofNullable(data).map(SteadyRateData::getSumValue).orElse(0.0);
//            JSONArray jsonArray = rtdbValueMap.get(dataSource);
            List<TagData> jsonArray = rtdbValueMap.get(dataSource.toUpperCase());
            if (StringUtils.isNotEmpty(jsonArray)) {
                String lastOverUpTime = null;
                String lastOverLowTime = null;
                for (int i = 0; i < jsonArray.size(); i++) {
//                    JSONObject jsonObject = jsonArray.getJSONObject(i);
//                    String datetime = jsonObject.getString("datetime").substring(0, 19);
                    TagData jsonObject = jsonArray.get(i);
                    String datetime = jsonObject.getDatetime();
                    if (StringUtils.isNotEmpty(lastTime) && datetime.compareTo(lastTime) <= 0) {
                        //小于上次最后时间的过滤掉
                        continue;
                    }
                    if (datetime.compareTo(kssj) < 0 || datetime.compareTo(jssj) > 0) {
                        //开始结束时间之间的视为有效，否则跳过
                        continue;
                    }
//                    Double rtdbValue = jsonObject.getDouble("value");
                    Double rtdbValue = null;
                    try {
                        rtdbValue = jsonObject.getValue() == null ? null : Double.parseDouble(jsonObject.getValue().toString());
                    } catch (Exception e) {}
                    if (keyUpLimit != null && rtdbValue != null && rtdbValue > keyUpLimit) {
                        //超上限
                        overUpLimit++;
                        if (StringUtils.isNotEmpty(lastOverUpTime) && datetime.compareTo(lastOverUpTime) >= 0) {
                            //累计超限时间，换算成分钟
                            long diff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(datetime), DateTimeUtils.parseDate(lastOverUpTime));
                            overUpLimitDuration += (double) diff / 60.0;
                        }
                        lastOverUpTime = datetime;
                    } else {
                        lastOverUpTime = null;
                    }

                    if (keyLowLimit != null && rtdbValue != null && rtdbValue < keyLowLimit) {
                        //超下限
                        overLowLimit++;
                        if (StringUtils.isNotEmpty(lastOverLowTime) && datetime.compareTo(lastOverLowTime) >= 0) {
                            //累计超限时间，换算成分钟
                            long diff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(datetime), DateTimeUtils.parseDate(lastOverLowTime));
                            overLowLimitDuration += (double) diff / 60.0;
                        }
                        lastOverLowTime = datetime;
                    } else {
                        lastOverLowTime = null;
                    }
                    totalCount++;
                    newLastTime = datetime.substring(0, 19);
                    //累加合计值
                    sumValue += Optional.ofNullable(rtdbValue).orElse(0.0);
                }
            }

            newLastTime = newLastTime == null ? dt : newLastTime;

            if (data == null) {
                data = new SteadyRateData();
                data.setId(TMUID.getUID());
                data.setUnitId(unitId);
                data.setProgramId(programid);
                data.setOrgCode(orgCode);
                data.setShiftCode(shiftClassCode);
                data.setItemId(sdid);
                data.setStartTime(kssj);
                data.setEndTime(jssj);
                data.setShiftStartTime(shiftStartTime);
                data.setShiftEndTime(shiftEndTime);
                data.setShiftDate(shift.getTbrq());
                insertSteadyDataList.add(data);
            } else {
                updateSteadyDataList.add(data);
            }

            data.setLastTime(newLastTime);
            data.setUpLimit(keyUpLimit);
            data.setLowLimit(keyLowLimit);
            data.setDataTime(dt);
            data.setTagCode(indicator.getDatasource());
            data.setItemName(name);
            data.setTotalCount(totalCount);
            data.setOverUpLimit(overUpLimit);
            data.setOverLowLimit(overLowLimit);
            data.setOverUpLimitDuration(overUpLimitDuration);
            data.setOverLowLimitDuration(overLowLimitDuration);
            data.setSumValue(sumValue);
        }


        //保存数据
        if (StringUtils.isNotEmpty(insertSteadyDataList)) {
            if (StringUtils.isEmpty(tenantId)) {
                dao.insertBatch(insertSteadyDataList);
            } else {
                dao.rawInsertBatchWithTenant(tenantId, insertSteadyDataList);
            }
        }
        if (StringUtils.isNotEmpty(updateSteadyDataList)) {
            if (StringUtils.isEmpty(tenantId)) {
                dao.updateBatch(updateSteadyDataList);
            } else {
                for (SteadyRateData data : updateSteadyDataList) {
                    dao.rawUpdateByIdWithTenant(tenantId, data, true);
                }
            }
        }

        //计算预警信息
        if (calcWarning) {
            calcWarningData(tenantId, shift, dt, rtdbValueMap, lastEndTimeMap);
        }
    }


    /**
     * 补录平稳率数据
     * @param tenantId
     * @param shift
     * @param dt
     */
    private void calcShiftAllSteadyRate (String tenantId, ProductUnitProgShift shift, String dt) {

        List<SteadyRateData> insertSteadyDataList = new ArrayList<>();

        //方案id
        String programid = shift.getProgramid();
        //核算对象id
        String unitId = shift.getUnitId();
        //机构代码
        String orgCode = shift.getOrgCode();
        String shiftClassCode = shift.getShiftClassCode();
        //上班时间
        String shiftStartTime = shift.getSbsj();
        //下班时间
        String shiftEndTime = shift.getXbsj();
        //方案开始时间
        String kssj = Optional.ofNullable(shift.getStartTime()).orElse(shiftStartTime);
        //方案结束时间
        String jssj = Optional.ofNullable(shift.getEndTime()).orElse(shiftEndTime);


        //查询方案下核算对象的指标
        List<IndicatorInfo> indicatorList = this.getUnitProgramItems(tenantId, shift, jssj, false);
        if (StringUtils.isEmpty(indicatorList)) {
            return;
        }

        //获取实时数据
//        Map<String, JSONArray> rtdbValueMap = this.getRtdbValueMap(indicatorList, kssj, jssj);
        Map<String, List<TagData>> rtdbValueMap = this.getRtdbValueMap(indicatorList, kssj, jssj);

        //遍历读取超限和总点数
        for (IndicatorInfo indicator : indicatorList) {
            //采集点id
            String sdid = indicator.getSdid();
            String name = indicator.getName();
            String dataSource = indicator.getDatasource();
            if (StringUtils.isEmpty(dataSource)) {
                continue;
            }

            String newLastTime = null;
            Double keyUpLimit = indicator.getKeyUpLimit();
            Double keyLowLimit = indicator.getKeyLowLimit();
//            Double operateUpLimit = indicator.getOperateUpLimit();
//            Double operateLowLimit = indicator.getOperateLowLimit();

            int totalCount = 0; //总点数
            int overUpLimit = 0;    //超上限点数
            int overLowLimit = 0;   //超下限点数
            double overUpLimitDuration = 0.0;
            double overLowLimitDuration = 0.0;
            double sumValue = 0.0;
//            JSONArray jsonArray = rtdbValueMap.get(dataSource);
            List<TagData> jsonArray = rtdbValueMap.get(dataSource.toUpperCase());
            if (StringUtils.isNotEmpty(jsonArray)) {
                String lastOverUpTime = null;
                String lastOverLowTime = null;
                for (int i = 0; i < jsonArray.size(); i++) {
//                    JSONObject jsonObject = jsonArray.getJSONObject(i);
//                    String datetime = jsonObject.getString("datetime").substring(0, 19);
//                    Double rtdbValue = jsonObject.getDouble("value");
                    TagData jsonObject = jsonArray.get(i);
                    String datetime = jsonObject.getDatetime().substring(0, 19);
                    Double rtdbValue = jsonObject.getValue() == null ? null : Double.parseDouble(jsonObject.getValue().toString());

                    if (keyUpLimit != null && rtdbValue != null && rtdbValue > keyUpLimit) {
                        //超上限
                        overUpLimit++;
                        if (StringUtils.isNotEmpty(lastOverUpTime) && datetime.compareTo(lastOverUpTime) >= 0) {
                            //累计超限时间，换算成分钟
                            long diff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(datetime), DateTimeUtils.parseDate(lastOverUpTime));
                            overUpLimitDuration += (double) diff / 60.0;
                        }
                        lastOverUpTime = datetime;
                    } else {
                        lastOverUpTime = null;
                    }

                    if (keyLowLimit != null && rtdbValue != null && rtdbValue < keyLowLimit) {
                        //超下限
                        overLowLimit++;
                        if (StringUtils.isNotEmpty(lastOverLowTime) && datetime.compareTo(lastOverLowTime) >= 0) {
                            //累计超限时间，换算成分钟
                            long diff = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(datetime), DateTimeUtils.parseDate(lastOverLowTime));
                            overLowLimitDuration += (double) diff / 60.0;
                        }
                        lastOverLowTime = datetime;
                    } else {
                        lastOverLowTime = null;
                    }
                    totalCount++;
                    newLastTime = datetime.substring(0, 19);
                    //累加合计值
                    sumValue += Optional.ofNullable(rtdbValue).orElse(0.0);
                }
            }
            newLastTime = newLastTime == null ? jssj : newLastTime;

            SteadyRateData data = new SteadyRateData();
            data.setId(TMUID.getUID());
            data.setUnitId(unitId);
            data.setProgramId(programid);
            data.setOrgCode(orgCode);
            data.setShiftCode(shiftClassCode);
            data.setItemId(sdid);
            data.setShiftStartTime(shiftStartTime);
            data.setShiftEndTime(shiftEndTime);
            data.setStartTime(kssj);
            data.setEndTime(jssj);
            data.setShiftDate(shift.getTbrq());

            data.setLastTime(newLastTime);
            data.setUpLimit(keyUpLimit);
            data.setLowLimit(keyLowLimit);
            data.setDataTime(dt);
            data.setTagCode(indicator.getDatasource());
            data.setItemName(name);
            data.setTotalCount(totalCount);
            data.setOverUpLimit(overUpLimit);
            data.setOverLowLimit(overLowLimit);
            data.setOverUpLimitDuration(overUpLimitDuration);
            data.setOverLowLimitDuration(overLowLimitDuration);
            data.setSumValue(sumValue);

            insertSteadyDataList.add(data);
        }


        //保存数据
        if (StringUtils.isNotEmpty(insertSteadyDataList)) {
            if (StringUtils.isEmpty(tenantId)) {
                dao.insertBatch(insertSteadyDataList);
            } else {
                dao.rawInsertBatchWithTenant(tenantId, insertSteadyDataList);
            }
        }

    }

    /**
     * 获取实时数据
     * @param indicatorList
     * @param kssj
     * @param jzsj
     * @return key：真实表号 value：[{datetime: '2023-01-01 12:12:12.000', value: 20.2123}]
     */
    private Map<String, List<TagData>> getRtdbValueMap(List<IndicatorInfo> indicatorList, String kssj, String jzsj) {
//        Map<String, JSONArray> rtdbValueMap = new HashMap<>();
        Map<String, List<TagData>> rtdbValueMap = new HashMap<>();
        if (StringUtils.isAnyEmpty(kssj, jzsj)) {
            return rtdbValueMap;
        }
        //取数时截止时间减去1秒，防止两端重复取数
        jzsj = DateTimeUtils.formatDate(DateTimeUtils.doSecond(DateTimeUtils.parseDate(jzsj), -1));

        //要一次性查出的实时表号
        Map<Integer, List<String>> toGetValueTagCodeGroup = new HashMap<>();
        for (IndicatorInfo indicator : indicatorList) {
            String dataSource = indicator.getDatasource();
            if (StringUtils.isEmpty(dataSource)) {
                continue;
            }
            Integer sampleInterval = indicator.getSampleInterval();
            sampleInterval = sampleInterval == null || sampleInterval <= 0 ? 5 : sampleInterval;
            List<String> strings = toGetValueTagCodeGroup.computeIfAbsent(sampleInterval, v -> new ArrayList<>());
            if (!strings.contains(dataSource)) {
                strings.add(dataSource);
            }
        }
        if (StringUtils.isEmpty(toGetValueTagCodeGroup)) {
            return rtdbValueMap;
        }

        Set<Integer> integers = toGetValueTagCodeGroup.keySet();
        for (Integer interval : integers) {
            List<String> tagCodes = toGetValueTagCodeGroup.get(interval);
//            JSONObject jsonObject = rtdbSrv.queryRtdbData(tagCodes, kssj, jzsj, interval*60);
//            JSONArray rtdbResult = jsonObject.getJSONArray("result");
            List<Tag> rtdbResult = rtdbSrv.queryRtdbTagData(tagCodes, kssj, jzsj, interval*60);
            if (StringUtils.isEmpty(rtdbResult)) {
                continue;
            }
            for (int i=0; i<rtdbResult.size(); i++) {
//                JSONObject each = rtdbResult.getJSONObject(i);
//                String tagCode = each.getString("tagCode");
//                JSONArray datas = each.getJSONArray("datas");
                Tag each = rtdbResult.get(i);
                String tagCode = each.getTagCode();
                List<TagData> datas = each.getDatas();
                if (StringUtils.isNotEmpty(datas)) {
                    rtdbValueMap.put(tagCode.toUpperCase(), datas);
                }
            }
        }
        return rtdbValueMap;
    }

    /**
     * 计算预警信息
     * @param tenantId
     * @param shift
     * @param dt
     * @param rtdbValueMap
     */
    private void calcWarningData (String tenantId, ProductUnitProgShift shift, String dt, Map<String, List<TagData>> rtdbValueMap, Map<String, String> lastEndTimeMap) {
        List<IndicatorInfo> indicatorList = this.getUnitProgramItems(tenantId, shift, dt, true);
        if (StringUtils.isEmpty(indicatorList)) {
            return;
        }

//        List<MsgObject> msgList = new ArrayList<>();


        String redisKey = "STEADYRATE:WARNING:LASTTIME:"+shift.getUnitId();
        String lasttime = redis.getString(redisKey);
        if (rtdbValueMap == null && StringUtils.isNotEmpty(dt)) {
            //没传实时数据，需重新查询实时数据
            //要一次性查出的实时表号
            rtdbValueMap = this.getRtdbValueMap(indicatorList, StringUtils.isEmpty(lasttime) ? Optional.ofNullable(shift.getStartTime()).orElse(shift.getSbsj()) : lasttime, dt);
        }

        //没有实时数据直接返回
        if (rtdbValueMap == null) {
            return;
        }

        redis.set(redisKey, dt);

        //查询历史正在超限的预警信息
        List<CostWarningDataInfo> usingWarnings = dao.rawQueryListByWhereDisableTenant(CostWarningDataInfo.class, Where.create().eq(CostWarningDataInfo::getTmused, 1).eq(CostWarningDataInfo::getUnitId, shift.getUnitId()), Order.create().orderByAsc(CostWarningDataInfo::getId));
        Map<String, CostWarningDataInfo> usingWarningMap = StringUtils.isEmpty(usingWarnings) ? new HashMap<>() : usingWarnings.stream().collect(Collectors.toMap(CostWarningDataInfo::getWarningPlanId, v -> v, (v1, v2) -> v2));

//        String stime = lasttime;
//        if (StringUtils.isEmpty(stime)) {
//            SqlRowSet maxRes = dao.rawQueryDisableTenant("select max(END_WARNING_TIME) from COST_WARNING_DATA_INFO where UNIT_ID=?", shift.getUnitId());
//            if (maxRes.next()) {
//                String maxValue = maxRes.getString(1);
//                stime = StringUtils.isEmpty(maxValue) ? stime : DateTimeUtils.formatDate(DateTimeUtils.parseDate(maxValue));
//            }
//        }
//        String filterStartTime = stime;

        List<CostWarningDataInfo> insertWarningList = new ArrayList<>();
        List<CostWarningDataInfo> updateWarningList = new ArrayList<>();
        List<CostWarningDataMsg> insertMsgList = new ArrayList<>();

        for (IndicatorInfo indicatorInfo : indicatorList) {
            String datasource = indicatorInfo.getDatasource();
            if (StringUtils.isEmpty(datasource)) {
                continue;
            }
            List<ProgramContingencyPlan> planList = indicatorInfo.getPlanList();
            if (StringUtils.isEmpty(planList)) {
                continue;
            }

            //真实值
//            JSONArray jsonArray = rtdbValueMap.get(datasource);
            List<TagData> jsonArray = rtdbValueMap.get(datasource.toUpperCase());
            if (StringUtils.isEmpty(jsonArray)) {
                continue;
            }
//            List<JSONObject> valueList = jsonArray.stream().map(item -> (JSONObject)item)
//                .filter(item -> {
//                if (item.getString("datetime").substring(0, 19).compareTo(dt) > 0) {
//                    //大于当前时间的过滤掉
//                    return false;
//                }
//                if (StringUtils.isNotEmpty(filterStartTime)) {
//                    //有上次执行时间
//                    //只从上次时间开始过滤
//                    return item.getString("datetime").substring(0, 19).compareTo(filterStartTime) >= 0;
//                }
//                return true;
//            })
//            .sorted(Comparator.comparing(v -> v.getString("datetime"))).collect(Collectors.toList());

            String itemLastTime = Optional.ofNullable(lastEndTimeMap).map(item -> item.get(indicatorInfo.getUnitid()+"_"+indicatorInfo.getSdid())).orElse(lasttime);
            for (ProgramContingencyPlan p : planList) {
                // 计划类型：up 超上限；low 超下限 (暂时不用该属性)
                //String planType = p.getPlanType();

                // 限值类型：key 关键限制；operate 操作限制
                String limitType = p.getLimitType();
                if (StringUtils.isAnyEmpty(limitType)) {
                    continue;
                }
                Double keyUpLimit = indicatorInfo.getKeyUpLimit();
                Double keyLowLimit = indicatorInfo.getKeyLowLimit();
                Double operateUpLimit = indicatorInfo.getOperateUpLimit();
                Double operateLowLimit = indicatorInfo.getOperateLowLimit();
                CostWarningDataInfo warnInfo = usingWarningMap.get(p.getId());
                //持续时间（分钟）
                Double duration = p.getDuration();

                List<JSONObject> overDataList = new ArrayList<>();
//                boolean flag = false;
                if (duration != null) {
                    String startTime = null;
                    String endTime = null;
                    for (int k=0; k<jsonArray.size(); k++) {
//                        JSONObject valueObj = jsonArray.getJSONObject(k);
                        TagData valueObj = jsonArray.get(k);
//                        String datetime = valueObj.getString("datetime").substring(0, 19);
                        String datetime = valueObj.getDatetime().substring(0, 19);
                        if (StringUtils.isNotEmpty(itemLastTime) && datetime.compareTo(itemLastTime) <= 0) {
                            //上次时间之前的跳过
                            continue;
                        }
//                        Double value = valueObj.getDouble("value");
                        Double value = valueObj.getValue() == null ? null : Double.parseDouble(valueObj.getValue().toString());

                        if ("key".equalsIgnoreCase(limitType) && keyUpLimit != null && value != null && value > keyUpLimit) {
                            //超key上限
                        } else if ("key".equalsIgnoreCase(limitType) && keyLowLimit != null && value != null && value < keyLowLimit) {
                            //超key下限
                        } else if ("operate".equalsIgnoreCase(limitType) && operateUpLimit != null && value != null && value > operateUpLimit) {
                            //超operate上限
                        } else if ("operate".equalsIgnoreCase(limitType) && operateLowLimit != null && value != null && value < operateLowLimit) {
                            //超operate下限
                        } else {
                            //正常不超限，重置开始时间
                            startTime = null;
                            if (warnInfo != null) {
                                //上次的预警信息used变为0
                                warnInfo.setTmused(0);
                            }
                            for (JSONObject json : overDataList) {
                                String end = json.getString("end");
                                if (datetime.compareTo(end) >= 0) {
                                    //此前时间所有时间段used全为0
                                    json.put("used", 0);
                                }
                            }
                            continue;
                        }

                        if (startTime == null && warnInfo != null && warnInfo.getTmused() == 1) {
                            //接上段预警信息，需从历史数据中读取开始超限时间
                            startTime = DateTimeUtils.formatDateTime(warnInfo.getStartWarningTime());
                            overDataList.add(new JSONObject());
                            overDataList.get(overDataList.size() - 1).put("start", startTime);
                            overDataList.get(overDataList.size() - 1).put("warnInfo", true);
                        } else if (startTime == null) {
                            //非历史预警信息，开始超限时间为第一个超限点的时间
                            startTime = datetime;
                            overDataList.add(new JSONObject());
                            overDataList.get(overDataList.size() - 1).put("start", startTime);
                        }

                        endTime = datetime;
                        overDataList.get(overDataList.size() - 1).put("end", endTime);
                        overDataList.get(overDataList.size() - 1).put("used", 1);
                        overDataList.get(overDataList.size() - 1).put("value", value);

                    }
                }

                if (warnInfo != null) {
                    updateWarningList.add(warnInfo);
                }

                if (StringUtils.isEmpty(overDataList)) {
                    continue;
                }

                for (JSONObject obj : overDataList) {
                    CostWarningDataInfo bean = null;
                    String start = obj.getString("start");
                    String end = obj.getString("end");
                    Double value = obj.getDouble("value");
                    if (obj.containsKey("warnInfo") && obj.getBoolean("warnInfo") && warnInfo != null) {
                        bean = warnInfo;
                    } else {
                        bean = new CostWarningDataInfo();
                        bean.setId(TMUID.getUID());
                        bean.setUnitId(shift.getUnitId());
                        bean.setProgramId(shift.getProgramid());
                        bean.setWarningPlanId(p.getId());
                        bean.setDataId(indicatorInfo.getSdid());
                        bean.setDataName(indicatorInfo.getName());
                        bean.setTagCode(indicatorInfo.getDatasource());
                        bean.setMsgSended(0);
                        bean.setTmused(obj.getInteger("used"));
                        bean.setStartWarningTime(DateTimeUtils.parseDate(start));
//                        bean.setForwardId("main");
                        insertWarningList.add(bean);
                    }
                    bean.setEndWarningTime(DateTimeUtils.parseDate(end));
                    int msgSended = bean.getMsgSended();
                    if (msgSended == 1) {
                        //发过消息不重复发送
                        continue;
                    }
                    if (duration == null) {
                        //持续时长为空不发消息
                        continue;
                    }

                    long diffSecond = DateTimeUtils.diffSecond(DateTimeUtils.parseDate(start), DateTimeUtils.parseDate(end));
                    if (diffSecond < duration * 60) {
                        //预警没超时，不发送消息
                        continue;
                    }

                    //转发
                    int forward = 0;
                    //接收人
                    String receivePosts = null;
                    String startPeriod = p.getStartPeriod();
                    String endPeriod = p.getEndPeriod();
                    Integer onduty = 0;
                    if (StringUtils.isNoneEmpty(startPeriod, endPeriod)) {
                        String nt = dt.substring(11, 16);
                        //判断是否在时段外
                        //开始时间小于结束时间时，当前时间小于开始时间或者当前时间大于结束时间
                        //开始时间大于结束时间时，当前时间小于开始时间并且当前时间大于结束时间
                        if ((startPeriod.compareTo(endPeriod) <= 0 && (nt.compareTo(startPeriod) < 0 || nt.compareTo(endPeriod) > 0))
                            || (startPeriod.compareTo(endPeriod) > 0 && (nt.compareTo(startPeriod) < 0 && nt.compareTo(endPeriod) > 0))) {
                            //时段外接收人
                            receivePosts = p.getOutDurationPos() == null ? "" : p.getOutDurationPos();
                            onduty = p.getOutOnduty();
                            forward = 1;
                        }
                    }

                    //非时段外均默认为时段内
                    if (receivePosts == null) {
                        receivePosts = p.getInDurationPos();
                        onduty = p.getInOnduty();
                    }

//                    HashMap<String, List<ProgramAlertReceiver>> receiverMap = indicatorInfo.getReceiverMap();
                    //其余皆正常发送消息
//                    List<ProgramAlertReceiver> receivers = receiverMap == null ? null : receiverMap.get(p.getId());

                    String[] receivers = StringUtils.isEmpty(receivePosts) ? null : receivePosts.split(",");
//                    List<String> msgUserIdList = new ArrayList<>();
                    Set<String> userIdList = new HashSet<>();
                    if (StringUtils.isNotEmpty(receivers)) {
//                        Set<String> userIdList = new HashSet<>();
                        for (String orgPost : receivers) {
                            String[] split = orgPost.split("_");
                            String orgId = split.length > 1 ? split[0] : null;
                            String postId = split.length > 1 ? split[1] : split[0];
                            //机构过滤
                            if (StringUtils.isNotEmpty(orgId) && onduty != null && onduty == 1 && !orgId.equals(shift.getOrgCode())) {
                                continue;
                            }
                            Set<String> users = StringUtils.isEmpty(orgId) ? empSrv.getEmployeeIdsByPostId(postId) : empSrv.getEmployeeIdsByOrgIdPostIdDisableTenant(orgId, postId);

                            if (StringUtils.isNotEmpty(users)) {
                                userIdList.addAll(users);
                            }
                        }

//                        if (onduty != null && onduty == 1) {
//                            //仅当班，需过滤
//                            List<SysEmployeeOrg> empOrgList = dao.queryDataDisableTenant(SysEmployeeOrg.class, Where.create().in(SysEmployeeOrg::getEmpid, userIdList.toArray()), null, null);
//                            msgUserIdList = empOrgList.stream().filter(item -> item.getOrgcode().equals(shift.getOrgCode())).map(SysEmployeeOrg::getEmpid).collect(Collectors.toList());
//                        } else {
//                            //不过滤，全部发消息
//                            msgUserIdList.addAll(userIdList);
//                        }
                    }
                    if (StringUtils.isNotEmpty(userIdList)) {
//                        bean.setMsgReceiveUser(StringUtils.join(userIdList, ","));
                        String message = null;
                        //超限持续时间达到设置值，且没发过消息，需发消息
                        String ovtext = "";
                        if ("key".equalsIgnoreCase(limitType)) {
                            ovtext = "（合格/关键限值"+(keyUpLimit==null?"":("上限：" +new BigDecimal(keyUpLimit).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()))+ (keyLowLimit==null?"":(" 下限："+new BigDecimal(keyLowLimit).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()))+"。）";
                        } else if ("operate".equalsIgnoreCase(limitType)) {
                            ovtext = "（调整/操作限值"+(operateUpLimit==null?"":("上限：" +new BigDecimal(operateUpLimit).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()))+(operateLowLimit==null?"":(" 下限："+new BigDecimal(operateLowLimit).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()))+"。）";
                        }
                        message = "工艺参数控制点【"+indicatorInfo.getName()+"】"+ovtext+"预警时("+end+")实时值为"+new BigDecimal(value).setScale(4, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()+", 已超出预警值"+(diffSecond/60)+"分钟。"+(StringUtils.isNotEmpty(p.getPlanInfo())?"("+p.getPlanInfo()+")":"");
                        bean.setWarningMessageTime(DateTimeUtils.parseDate(dt));
                        bean.setMsgSended(1);

                        MsgObject subdutyobj = new MsgObject();
                        subdutyobj.setModulecode("leanCosting");
                        subdutyobj.setFuncode("CostWarningInfo");
                        subdutyobj.setRecvEmpIds(StringUtils.join(userIdList, ","));
                        subdutyobj.setTitle("预警消息提醒");
                        subdutyobj.setContent(message);
//                        msgList.add(subdutyobj);
                        MsgObjectRet msgObjectRet = msgSrv.sendMsg(subdutyobj);
                        if (!msgObjectRet.isSuccess()) {
                            //发送失败，直接跳过不插入消息明细
                            bean.setMsgSended(0);
//                            continue;
                        }

                        List<String> failUserIdList = msgObjectRet.getFailMsgList().stream().filter(item -> StringUtils.isNotEmpty(item.getRecvEmpIds())).map(item -> item.getRecvEmpIds().split(",")).flatMap(Arrays::stream).collect(Collectors.toList());

                        for (String s : userIdList) {
                            CostWarningDataMsg msg = new CostWarningDataMsg();
                            msg.setInfoId(bean.getId());
                            msg.setWarningMessage(message);
                            msg.setMsgSended(1);
                            if (StringUtils.isNotEmpty(failUserIdList) && failUserIdList.contains(s)) {
                                msg.setMsgSended(0);
                            }
                            msg.setWarningMessageTime(DateTimeUtils.parseDate(dt));
                            msg.setId(TMUID.getUID());
                            msg.setMsgReceiveUser(s);
                            msg.setForwardState(forward); //TODO 0不转发 1待转发
                            insertMsgList.add(msg);
                        }

                    }

                }

            }
        }

        if (StringUtils.isNotEmpty(insertWarningList)) {
            dao.rawInsertBatchWithTenant(tenantId, insertWarningList);
        }
        if (StringUtils.isNotEmpty(updateWarningList)) {
            dao.updateByIdBatchWithTenant(tenantId, updateWarningList, 500);
        }
//        if (StringUtils.isNotEmpty(msgList)) {
//            //发送消息
//            MsgObjectRet msgObjectRet = msgSrv.sendMsg(msgList);
//            List<MsgObjectEx> failMsgList = msgObjectRet.getFailMsgList();
//            for (MsgObjectEx msgObjectEx : failMsgList) {
//            }

//        }

        //清除待办缓存
        clearWarningTotoCache();

        if (StringUtils.isNotEmpty(insertMsgList)) {
            dao.rawInsertBatchWithTenant(tenantId, insertMsgList);
        }
    }

    private JSONObject checkWarning () {

        return null;
    }

    private List<IndicatorInfo> getUnitProgramItems (String tenantId, ProductUnitProgShift shift, String dt, boolean useCache) {
        String unitId = shift.getUnitId();
        String programid = shift.getProgramid();
        String shiftClassCode = shift.getShiftClassCode();
        String xbsj = Optional.ofNullable(shift.getEndTime()).orElse(StringUtils.isNotEmpty(dt) ? dt : shift.getXbsj());
        dt = StringUtils.isEmpty(dt) ? xbsj : dt;   //没传时间按结束时间算
        Date xb = DateTimeUtils.parseDate(xbsj);
        Date d = DateTimeUtils.parseDate(dt);
        int bj = DateTimeUtils.bjDate(d, xb);
        if (!useCache || bj >= 0) {
            //不走缓存，或者过了下班时间，说明补录，不走redis
            return itemSrv.getIndicators(unitId, programid, dt.substring(0, 10), tenantId);
        } else {
            //先查redis
            String redisKey = "STEADYRATE:"+unitId+":"+programid+":"+shiftClassCode;
            List<IndicatorInfo> list = redis.getClassList(IndicatorInfo.class, redisKey);
//            List<IndicatorInfo> list = redis.getObjectList(IndicatorInfo.class, redisKey);
//            List<IndicatorInfo> list = redisTemplate.opsForList().range(redisKey, 0, -1);
            if (StringUtils.isEmpty(list)) {
                //过期时间设到下班为止
                long l = DateTimeUtils.diffSecond(xb, d);
                list = itemSrv.getIndicators(unitId, programid, dt.substring(0, 10), tenantId);
                if (StringUtils.isNotEmpty(list)) {
                    redis.setObject(redisKey, list, l);
                }
            } else {
//                for (IndicatorInfo indicatorInfo : list) {
//                    HashMap<String, List<ProgramAlertReceiver>> receiverMap = indicatorInfo.getReceiverMap();
//                    if (receiverMap != null) {
//                        HashMap<String, List<ProgramAlertReceiver>> result = new HashMap<>();
//                        for (Map.Entry<String, List<ProgramAlertReceiver>> entry : receiverMap.entrySet()) {
//                            String key = entry.getKey();
//                            List<ProgramAlertReceiver> value = entry.getValue();
//                            result.put(key, StringUtils.isEmpty(value) ? null : ObjUtils.convertToList(ProgramAlertReceiver.class, value));
//                        }
//                        indicatorInfo.setReceiverMap(result);
//                    }
//                }
            }
            return list;
        }

    }


    /**
     * 清除预警待办缓存
     */
    private void clearWarningTotoCache () {
        todoService.clearTodoCached("leanCosting","CostWarningInfo"); // 清除缓存
    }

}
