package com.yunhesoft.leanCosting.unitConf.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.leanCosting.baseConfig.entity.po.Costclass;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.MethodSaveDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.paramDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.CostMeteringUnit;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitsampledot;
import com.yunhesoft.leanCosting.unitConf.entity.vo.BeanVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostStipulateTimeVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostitemVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.SampledotVo;
import com.yunhesoft.leanCosting.unitConf.service.ICostitemService;
import com.yunhesoft.leanCosting.unitConf.service.IUnitMethodService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/leanCosting/unitConf/costItem")
@Api(tags = "成本项目分类")
public class CostitemController {

	@Autowired
	private ICostitemService costitemService;
	
	@Autowired
	private IUnitMethodService methodService;
	
	
	@RequestMapping(value = "/getData", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getData(@RequestBody paramDto dto) {
		Res<CostitemVo> res = new Res<CostitemVo>();
		CostitemVo obj = costitemService.getData(dto.getId(), dto.getType());
		res.setResult(obj);
		return res;
	}
	
	@RequestMapping(value = "/getDatas", method = RequestMethod.POST)
	@ApiOperation("获取数据")
	public Res<?> getDatas(@RequestBody paramDto dto) {
		Res<List<CostitemVo>> res = new Res<List<CostitemVo>>();
		List<CostitemVo> list = costitemService.getDatas(dto);
		res.setResult(list);
		return res;
	}

	@RequestMapping(value = "/saveData", method = RequestMethod.POST)
	@ApiOperation("保存数据")
	public Res<?> saveData(@RequestBody CostitemVo obj) {
		Res<CostitemVo> res = new Res<CostitemVo>();
		CostitemVo vo = costitemService.save(obj);
		res.setResult(vo);
		return res;
	}

//	@RequestMapping(value = "/deleteDatas", method = RequestMethod.POST)
//	@ApiOperation("删除数据")
//	public Res<?> delete(@RequestBody List<CostitemVo> bean) {
//		Res<Boolean> res = new Res<Boolean>();
//		res.setResult(false);
//		int i = costitemService.delete(bean);
//		if(i > 0) {
//			res.setResult(true);
//		}
//		return res;
//	}
	
	@RequestMapping(value = "/getDot", method = RequestMethod.POST)
	@ApiOperation("获取采集点数据")
	public Res<?> getDot(@RequestBody paramDto dto) {
		Res<SampledotVo> res = new Res<SampledotVo>();
		SampledotVo obj = costitemService.getDot(dto.getId(), dto.getType());
		res.setResult(obj);
		return res;
	}
	
	@RequestMapping(value = "/saveDot", method = RequestMethod.POST)
	@ApiOperation("保存采集点数据")
	public Res<?> saveDot(@RequestBody SampledotVo obj) {
		Res<SampledotVo> res = new Res<SampledotVo>();
		SampledotVo vo = costitemService.saveDot(obj);
		res.setResult(vo);
		return res;
	}
	
	//核算项目中用（采集类型为成本仪表的采集点数据）
	@RequestMapping(value = "/getDotList", method = RequestMethod.POST)
	@ApiOperation("获取采集点列表数据")
	public Res<?> getDotList(@RequestBody SampledotVo obj) {
		Res<List<Costunitsampledot>> res = new Res<List<Costunitsampledot>>();
		List<Costunitsampledot> list = costitemService.getDotList(obj);
		res.setResult(list);
		return res;
	}
	
	//核算项目中用（采集类型为成本仪表的采集点数据）
	@RequestMapping(value = "/saveDotData", method = RequestMethod.POST)
	@ApiOperation("批量保存采集点数据")
	public Res<?> saveDotData(@RequestBody MethodSaveDto saveDto) {
		Res<String> res = new Res<String>();
		String ret = methodService.saveCostunitsampledotData(saveDto);
		res.setResult(ret);
		return res;
	}
	
	
	//核算项目中用（成本仪表的规定时间数据）
	@RequestMapping(value = "/getStipulateTimeList", method = RequestMethod.POST)
	@ApiOperation("获取规定时间数据")
	public Res<?> getStipulateTimeList(@RequestBody MethodQueryDto dto) {
		Res<List<CostStipulateTimeVo>> res = new Res<List<CostStipulateTimeVo>>();
		List<CostStipulateTimeVo> list = costitemService.getStipulateTimeList(dto);
		res.setResult(list);
		return res;
	}
	
	//核算项目中用（成本仪表的规定时间数据）
	@RequestMapping(value = "/saveCostStipulateTimeData", method = RequestMethod.POST)
	@ApiOperation("保存规定时间数据")
	public Res<?> saveCostStipulateTimeData(@RequestBody MethodSaveDto saveDto) {
		Res<String> res = new Res<String>();
		String ret = methodService.saveCostStipulateTimeData(saveDto);
		res.setResult(ret);
		return res;
	}
	
	
	@RequestMapping(value = "/getCostItemClassTypeList", method = RequestMethod.POST)
	@ApiOperation("获取核算项目分类类型")
	public Res<?> getCostItemClassTypeList() {
		Res<List<BeanVo>> res = new Res<List<BeanVo>>();
		List<BeanVo> list = methodService.getCostClassTypeList();
		res.setResult(list);
		return res;
	}
	
	
	@RequestMapping(value = "/getCostMeteringUnitList", method = RequestMethod.POST)
	@ApiOperation("获取计量单位数据")
	public Res<?> getCostMeteringUnitList(@RequestBody MethodQueryDto queryDto) {
		Res<List<CostMeteringUnit>> res = new Res<List<CostMeteringUnit>>();
		List<CostMeteringUnit> list = methodService.getCostMeteringUnitList(queryDto);
		res.setResult(list);
		return res;
	}
	
	
	@RequestMapping(value = "/getCostclassById", method = RequestMethod.POST)
	@ApiOperation("获取核算分类")
	public Res<?> getCostclassById(@RequestBody MethodQueryDto queryDto) {
		Res<Costclass> res = new Res<Costclass>();
		Costclass obj = null;
		if(queryDto!=null) {
			String id = queryDto.getPid();
			if(StringUtils.isNotEmpty(id)) {
				obj = methodService.getCostclassById(id);
			}
		}
		res.setResult(obj);
		return res;
	}
	
	
}
