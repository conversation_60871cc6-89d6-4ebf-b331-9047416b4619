package com.yunhesoft.leanCosting.unitConf.entity.dto;


import java.util.List;

import com.yunhesoft.leanCosting.unitConf.entity.vo.UnitAlertRecieverConfigVo;

import lombok.Getter;
import lombok.Setter;


/**
 *	核算对象的默认预警设置保存类
 */
@Setter
@Getter
public class UnitAlertRecieverSaveDto {
	
	
	/** 核算对象id */
	private String unitid;

	/** 版本日期 */
	private String begintime;
	
	/** 核算对象的默认预警设置数据 */
	private List<UnitAlertRecieverConfigVo> unitAlertRecieverList;
    
}
