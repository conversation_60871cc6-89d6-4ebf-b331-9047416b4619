package com.yunhesoft.leanCosting.unitConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 规定时间设置表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "COSTSTIPULATETIME")
public class CostStipulateTime extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 核算对象ID */
    @Column(name="UNITID", length=100)
    private String unitid;
    
    /** 版本 */
    @Column(name="BEGINTIME", length=10)
    private String begintime;
    
    /** 成本项目仪表的唯一ID */
    @Column(name="PID", length=100)
    private String pid;
    
    /** 班次代码 */
    @Column(name="SHIFTCLASSCODE", length=100)
    private String shiftClassCode;
    
    /** 规定时间类型：1、上班时间之前；2、上班时间之后；3、下班时间之前；4、下班时间之后； */
    @Column(name="STIPULATETYPE")
    private Integer stipulateType;
    
    /** 规定时间（分钟） */
    @Column(name="STIPULATETIME")
    private Integer stipulateTime;
    
    /** 偏差时间（分钟） */
    @Column(name="DEVIATIONTIME")
    private Integer deviationTime;
    
}
