package com.yunhesoft.leanCosting.unitConf.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;



@Entity
@Setter
@Getter
@ApiModel("配置项目")
@Table(name = "T_REPORT_ENTRY")
public class TReportEntry  extends BaseEntity {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value="REPORTID", example="REPORTID1")
    @Column(name = "REPORTID")
    private String reportid;
    
    @ApiModelProperty(value="ZBID", example="ZBID1")
    @Column(length=100)
    private String zbid;
    
    @ApiModelProperty(value="ZBMC", example="ZBMC1")
    @Column(length=50)
    private String zbmc;
    
    @ApiModelProperty(value="ZBTYPE", example="123")
    @Column(name = "ZBTYPE")
    private int zbtype;
    
    @ApiModelProperty(value="ENTRYNAME", example="ENTRYNAME1")
    @Column(name = "ENTRYNAME")
    private String entryname;
    
	@ApiModelProperty(value="SORT", example="123")
	@Column(name = "SORT")
	private int sort;
	
	@ApiModelProperty(value="USED", example="123")
	@Column(name = "USED")
	private int used;
	

}
