package com.yunhesoft.leanCosting.unitConf.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 *	核算对象的默认预警设置表
 */
@Entity
@Setter
@Getter
@Table(name = "UNITALERTRECIEVERCONFIG")
public class UnitAlertRecieverConfig extends BaseEntity {
	
    private static final long serialVersionUID = 1L;
    
    /** 核算对象ID */
	@Column(name = "UNITID", length = 100)
	private String unitid;

	/** 版本日期 */
	@Column(name = "BEGINTIME", length = 20)
	private String begintime;
		
	/** 开始时段 */
    @Column(name="STARTPERIOD", length=10)
    private String startPeriod;
    
	/** 截止时段 */
    @Column(name="ENDPERIOD", length=10)
    private String endPeriod;
    
    /** 超限持续时长（分钟） */
    @Column(name="DURATION")
    private Double duration;
    
    /** 时段内接收岗位 */
    @Column(name="INDURATIONPOS", length=1000)
    private String inDurationPos;
    
    /** 时段内接收岗位名称 */
    @Column(name="INDURATIONPOSNAME", length=2000)
    private String inDurationPosName;
    
    /** 时段内仅当班 */
    @Column(name="INONDUTY")
    private Integer inOnduty;
    
    /** 时段外接收岗位 */
    @Column(name="OUTDURATIONPOS", length=1000)
    private String outDurationPos;
    
    /** 时段外接收岗位名称 */
    @Column(name="OUTDURATIONPOSNAME", length=2000)
    private String outDurationPosName;
    
    /** 时段外仅当班 */
    @Column(name="OUTONDUTY")
    private Integer outOnduty;
    
    /** 1、使用；0、不使用 */
    @Column(name="TMUSED")
    private Integer tmUsed;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmSort;
    
}
