package com.yunhesoft.leanCosting.unitConf.entity.vo;


import com.yunhesoft.leanCosting.unitConf.entity.po.CostKeyDeviceConf;

import lombok.Getter;
import lombok.Setter;

/**
 *	关键设备设置类
 * <AUTHOR>
 */
@Getter
@Setter
public class CostKeyDeviceConfVo extends CostKeyDeviceConf {
	
	
	private static final long serialVersionUID = 1L;

	private Integer rowFlag = 0; //记录标识：-1、删除；其他为新增或修改（根据数据id判断）
	
    private Integer runMinute_sub; //运行分钟浮动
    
    private Double runHour_sub; //运行小时浮动
    
    private Boolean hasStartStop = false; //是否有设备运行数据（有：不能删除）
}
