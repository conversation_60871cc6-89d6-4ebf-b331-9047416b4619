package com.yunhesoft.leanCosting.unitConf.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CopyDataDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostBindOrgDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.CostuintQueryDto;
import com.yunhesoft.leanCosting.unitConf.entity.dto.SynchronousCostUnitDto;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitcycle;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitmanager;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costunitoperator;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintExcelVo;
import com.yunhesoft.leanCosting.unitConf.entity.vo.CostuintVo;
import com.yunhesoft.system.org.entity.po.SysOrg;

public interface ICostService {

	/**
	 * TODO:接口：提供给移动端
	 * @param dto
	 * @return
	 */
	public List<CostuintVo> getCostuintTreeVo(CostDto dto);
	
	
	/**
	 * 查询排序使用
	 * 
	 * @param pid
	 * @param pSort
	 * @return
	 */
	public String getCostuitSort(String pid, String pSort);

	/**
	 * 查询数据
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costuint> getData(CostDto dto);

	/**
	 * 获取核算对象
	 * 
	 * @param id
	 * @return
	 */
	public Costuint getCostuintObjById(String id);

	/**
	 * @category 保存节点
	 * @param dto
	 * @return
	 */
	public String saveData(CostDto dto);

	/**
	 * 保存Vo
	 * 
	 * @param vo
	 * @return
	 */
	public String saveDataVo(CostuintVo vo);

	/**
	 * @category 添加版本数据
	 * @param dto
	 * @return
	 */
	public String addVersion(CostDto dto);

	/**
	 * @category 获取版本信息列表
	 * @param dto
	 * @return
	 */
	public List<String> getVersionList(CostDto dto);

	/**
	 * 获取核算对象的操作对象数据列表
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costunitoperator> getCostunitoperatorList(CostBindOrgDto dto);

	/**
	 * 获取核算对象的操作对象id列表--Map
	 * 
	 * @param tenantId 租户id
	 * @param unitid   核算对象id
	 * @return
	 */
	public LinkedHashMap<String, List<String>> getCostunitoperatorMap(String tenantId, String unitid);

	/**
	 * 获取核算对象的管理对象数据列表
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costunitmanager> getCostunitmanagerList(CostBindOrgDto dto);

	/**
	 * 获取核算对象的绑定机构
	 * 
	 * @param dto
	 * @return
	 */
	public CostBindOrgDto getCostUnitBindOrg(CostBindOrgDto dto);

	/**
	 * 保存核算对象的绑定机构
	 * 
	 * @param saveDto
	 * @return
	 */
	public String saveCostUnitBindOrgData(CostBindOrgDto dto);

	/**
	 * 获取核算对象列表（根据绑定机构ID）
	 * 
	 * @param orgId   绑定机构ID 查询类型==2时，默认带当前登入人的{机构}和{岗位}
	 * @param selType 查询类型：1、操作机构；2、全部（操作机构,管理机构）
	 * @return
	 */
	List<Costuint> getCostuintListByOrgId(String orgId, int selType);

	/**
	 * 获取核算对象列表（根据绑定机构ID）
	 * 
	 * @param orgId     绑定机构ID
	 * @param userId    操作人员ID
	 * @param orgPostId 操作机构岗位ID（orgId_postId）
	 * @param selType   查询类型：1、操作机构；2、全部（操作机构,管理机构）
	 * @return
	 */
	List<Costuint> getCostuintListByOrgId(String orgId, String userId, String orgPostId, int selType);

	/**
	 * 获取核算对象列表（根据绑定机构ID），目标传导的公式用这个
	 * 
	 * @param selType
	 * @return
	 */
	List<Costuint> getCostuintListByUser(String orgId, int selType);

	/**
	 * 2024-01-03号添加 1.运行部来源于costuint表的维护机构（orgid），去重后按照组织机构中机构排序对运行部进行排序。提供新接口返回机构
	 * 
	 * @param orgId
	 * @param userId
	 * @param orgPostId
	 * @param selType
	 * @return 返回机构ID
	 */
	List<SysOrg> getCostuintSysOrgList(String orgId, int selType);

	/**
	 * 
	 * @param listKeyStream
	 * @return
	 */
	List<Costuint> getCostuintListOrgId(String orgIds);

	/**
	 * 2024-01-03号添加
	 * 下拉框可多选，默认选择排第一位的运行部，选择运行部后，表格刷新展示运行部下的核算对象。提供新接口，根据[维护机构]返回用户可查看的核算对象。
	 * 
	 * @param costUnitID
	 * @param orgId      维护机构ID
	 * @return 返回核算对象
	 */
	List<Costuint> getCostuintListOrgId(CostuintQueryDto dto, String orgIds);

	/**
	 * 获取核算对象的周期列表
	 * 
	 * @param dto
	 * @return
	 */
	public List<Costunitcycle> getCostunitcycleList(CostBindOrgDto dto);

	/**
	 * 保存核算对象的周期数据
	 * 
	 * @param dto
	 * @return
	 */
	public String saveCostunitcycleData(CostBindOrgDto dto);

	/**
	 * 核算对象数据另存为
	 * 
	 * @param saveDto
	 * @return
	 */
	public String copyCostUnit(CopyDataDto saveDto);

	/**
	 * 生产反馈核算对象下拉框数据
	 * 
	 * <AUTHOR>
	 * @return
	 * @params
	 */
	public JSONArray getCostObjComboData();

//	public String userOrgIsManageOrg(String unitId, String userOrg);
	/**
	 * 
	 * @category	 	用户所属的机构是不是给定核算对象的维护机构
	 * @param unitId  	核算对象ID
	 * @param userOrg	用户的机构代码
	 * @param empId		人员EMPID
	 * @param postId	岗位ID
	 * @return manage是维护机构；operate是操作机构；other其余认为仅查询
	 */
	public String userOrgIsManageOrg(String unitId, String userOrg, String empId, String postId);

	/**
	 * 重新排序
	 * 
	 * @param updateList
	 * @param cot        1正序加1 -1倒序-1
	 */
	void getCostLists(List<Costuint> updateList, String oldTmsort, int cot);

	/**
	 * 通过PId查询
	 * 
	 * @param pid
	 * @return
	 */
	List<Costuint> getData(String pid);

	/**
	 * 1.交接班使用，同步到对应核算对象
	 * 
	 * @param list
	 * @return
	 */
	public boolean synchronousCostUnit(List<SynchronousCostUnitDto> list);

	public List<CostuintExcelVo> toExcel();

	/**
	 * 导入
	 * 
	 * @param teList
	 * @return
	 */
	public String importExcel(List<CostuintExcelVo> teList);

	/**
	 * 获取新序号
	 * 
	 * @param tmSort
	 * @param addNum
	 * @return
	 */
	public String getNewSort(String tmSort, int addNum);

	/**
	 * 增加过滤台账查询
	 * 
	 * @param orgId       机构ID
	 * @param selType     类型
	 * @param ledgerEntry 是否用于台账 true过滤否用于台账；false，不过滤
	 * @return
	 */
	List<Costuint> getCostuintListByOrgId(String orgId, int selType, boolean ledgerEntry);
	

	/**
	 * 通过人员ID查询操作（机构，人员，岗位）信息
	 * @param costUintId	核算对象ID
	 * @param user		人员信息user
	 * @return				true 包括，false 不包括
	 */
	boolean getCostuintListByOrgId(String costUintId, SysUser user);


	/**
	 * 核算对象
	 * @param vo		cost核算对象
	 * @param rowFlat	操作状态	-1删除，0修改（默认），1添加
	 * 			判断逻辑只判断重复
	 * @return
	 */
	String saveDataVo(Costuint vo, Integer rowFlat);

	/**
	 * 根据传入的核算对象ID，得到对象的维护机构，操作机构和管理机构
	 * 
	 * @param costuintId 核算对象Id
	 * @return
	 */
	Map<String, List<String>> getCostuintById(String costuintId);


	/**
	 * 获取核算对象列表（根据绑定机构ID）
	 * 
	 * @param orgId     	绑定机构ID
	 * @param userId    	操作人员ID
	 * @param orgPostId 	操作机构岗位ID（orgId_postId）
	 * @param selType   	查询类型：1、操作机构；2、全部（操作机构,管理机构）
	 * @param productive	生产活动：0、核算对象；1、作业活动；2、台账模型
	 * @return
	 */
	List<Costuint> getCostuintListByOrgId(String orgId, String userId, String orgPostId, int selType,
			Integer productive);


	/**
	 * 查询核算对象列表
	 * @param orgid		机构id
	 * @param selType	类型
	 * @param productive	生产活动：0、核算对象；1、作业活动；2、台账模型
	 * @return
	 */
	List<Costuint> getCostuintListByUser(String orgid, int selType, Integer productive);


	List<Costunitoperator> getCostunitoperatorOrgList(CostBindOrgDto dto);

}
