package com.yunhesoft.leanCosting.workDispatch.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.leanCosting.costReport.entity.po.CostBatchOnDuty;
import com.yunhesoft.leanCosting.unitConf.entity.po.Costuint;
import com.yunhesoft.leanCosting.unitConf.service.ICostService;
import com.yunhesoft.leanCosting.unitConf.service.UnitItemInfoService;
import com.yunhesoft.leanCosting.workDispatch.entity.dto.CostBatchDto;
import com.yunhesoft.leanCosting.workDispatch.entity.dto.WorkDispatchDto;
import com.yunhesoft.leanCosting.workDispatch.service.WorkDispatchService;
import com.yunhesoft.shift.shift.entity.dto.ShiftForeignDto;
import com.yunhesoft.shift.shift.entity.vo.ShiftForeignVo;
import com.yunhesoft.system.kernel.config.SysUserHolder;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.shift.shift.service.IShiftService;

@Service
public class WorkDispatchServiceImpl implements WorkDispatchService{
	@Autowired
	private EntityService dbo;
	@Autowired
	private ICostService costService;
	@Autowired
	private UnitItemInfoService unititeminfoservice;
	@Autowired
	private IShiftService IShiftService;
	
	/**
	 * 判断登录人班组是否当班
	 * @param day
	 * @return
	 */
	public boolean getShiftDataByrqtime(String day) {
		boolean bool=false;
		ShiftForeignDto dto=new ShiftForeignDto();
		SysUser user = SysUserHolder.getCurrentUser();
		String orgCode=user.getOrgId();//登录人所在班组
		dto.setOrgCode(orgCode);
		if(day==null||day.equals("")) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			day=sdf.format(new Date());
		}
		dto.setNowDateTime(day);
		List<ShiftForeignVo> list=IShiftService.getShiftDataByrqtime(dto);
		if(list!=null&&list.size()>0) {
			bool=true;
		}
		return bool;
	}
	
	
	/**
	 * 给2个日期参数，返回2个日期之间的所有日期
	 * 
	 * @return
	 */
	public List<String> getdates(String d1, String d2) throws ParseException {
		List<String> list = new ArrayList<String>();
		if (d1 == null || d2 == null || d1.equals("") || d2.equals("")) {
			return list;
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		long m1 = sdf.parse(d1).getTime();
		long m2 = sdf.parse(d2).getTime();
		Long oneDay1 = (long) (1000 * 60 * 60 * 24);
		while (m1 <= m2) {
			Date d = new Date(m1);
			list.add(sdf.format(d));
			m1 += oneDay1;
		}
		return list;
	}
	/**
	 * 获取调度台数据
	 * @param bean
	 * @return
	 */
	public JSONObject getData(WorkDispatchDto bean) {
		JSONObject jobj=new JSONObject();//数据
		
		
		//返回两个日期之间所以日期
		List<String> listdt=new ArrayList<String>();
		JSONArray array_table=new JSONArray();
		try {
			listdt= getdates(bean.getStartDt(), bean.getEndDt());
//			jtable.put("unitName", "核算对象");
			//编制表头
			if(listdt!=null&&listdt.size()>0) {
				for(int i=0;i<listdt.size();i++) {
					JSONObject jtable=new JSONObject();//表头
					String dt=listdt.get(i);
					jtable.put("id","A"+i+1);
					jtable.put("name",dt);
					
					array_table.add(jtable);
				}
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		jobj.put("table", array_table);//填充表头
		
		
		List<Costuint> list=new ArrayList<Costuint>();//核算对象列表
		List<String> listId=new ArrayList<String>();//查询子表数据用-核算ID
		List<String> listrq=new ArrayList<String>();//查询子表数据用-日期区间
		
		SysUser user = SysUserHolder.getCurrentUser();
		List<Costuint> listCostuint=costService.getCostuintListByOrgId(user.getOrgId(), 2);
		//查指定的核算对象数据
		if(bean.getUnitId()!=null&&!bean.getUnitId().equals("all")) {
			String unitId=bean.getUnitId();
			String unitMc=bean.getUnitName();
			Costuint e=new Costuint();
			e.setId(unitId);
			e.setName(unitMc);
			if(listCostuint!=null&&listCostuint.size()>0) {
				for(int i=0;i<listCostuint.size();i++){
					Costuint ec=listCostuint.get(i);
					if(ec.getId().equals(unitId)) {
						e.setFormId(ec.getFormId());	
					}
				}
			}
			
			list.add(e);
			listId.add(unitId);
		}else {
		//查询全部核算对象数据
			if(listCostuint!=null&&listCostuint.size()>0) {
				list.addAll(listCostuint);
				for(int i=0;i<listCostuint.size();i++){
					Costuint e=listCostuint.get(i);
					listId.add(e.getId());
				}
			}
		}
		
		listrq.add(bean.getStartDt());
		listrq.add(bean.getEndDt());
		
		Map<String,String> map=new HashMap<String,String>();//封装核算id+日期，用于判断当天是否已经录入过数据
		
		Where where = Where.create();
		where.in(CostBatchOnDuty::getUnitId, listId.toArray());//核算对象id
		where.between(CostBatchOnDuty::getWriteDay, listrq.toArray());//填报日期，就是当天日期
		Order order = Order.create();
		order.orderByAsc(CostBatchOnDuty::getBeginTime);//开始时间排序
		List<CostBatchOnDuty> listCost = dbo.queryList(CostBatchOnDuty.class, where, order);
		if(listCost!=null&&listCost.size()>0) {
			for(int i=0;i<listCost.size();i++) {
				CostBatchOnDuty e=listCost.get(i);
				String key=e.getUnitId()+""+e.getWriteDay();
				map.put(key, "1");//核算ID+日期=有值
			}
		}
		JSONArray array=new JSONArray();
		//核算对象
		if(list!=null&&list.size()>0){
			for(int i=0;i<list.size();i++) {
				JSONObject jobjdata=new JSONObject();//表头
				Costuint e=list.get(i);
				String unitId=e.getId();
				String unitName=e.getName();
				String formId=e.getFormId();
				jobjdata.put("unitId", unitId);
				jobjdata.put("unitName", unitName);
				jobjdata.put("formId", formId);
				//遍历日期，给每天赋值
				if(listdt!=null&&listdt.size()>0) {
					for(int j=0;j<listdt.size();j++) {
						String dt=listdt.get(j);//日期
						String key=unitId+""+dt;//核算ID+日期
						String value="0";
						if(map!=null&&map.containsKey(key)) {
							value=map.get(key);
						}
						jobjdata.put("A"+j+1, value);
					}
				}
				array.add(jobjdata);
			}
			
		}
		jobj.put("data", array);//填充数据
		
		return jobj;
	}
	
	/**
	 * 查询交接班明细记录
	 * @param bean
	 * @return
	 */
	@Override
	public List<CostBatchOnDuty> getListCostBatchOnDuty(CostBatchDto bean){
		
		Where where = Where.create();
		where.eq(CostBatchOnDuty::getUnitId, bean.getUnitId());//核算对象id
		where.eq(CostBatchOnDuty::getWriteDay, bean.getWriteDay());//填报日期，就是当天日期
		Order order = Order.create();
		order.orderByAsc(CostBatchOnDuty::getBeginTime);//开始时间排序
		List<CostBatchOnDuty> list = dbo.queryList(CostBatchOnDuty.class, where, order);
		return list;
	}
	
	/**
	 * 新增/保存
	 * @param list
	 * @return
	 */
	@Override
	public int saveData(List<CostBatchOnDuty> list) {
		int n=1;
		//判断传值是否为空
		List<CostBatchOnDuty> listIn =new ArrayList<CostBatchOnDuty>();
		List<CostBatchOnDuty> listUp =new ArrayList<CostBatchOnDuty>();
		if(list!=null&&list.size()>0) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String day=sdf.format(new Date());
			SysUser user = SysUserHolder.getCurrentUser();
			for(int i=0;i<list.size();i++) {
				CostBatchOnDuty bean=list.get(i);
				bean.setFeedbackUserId(user.getId());
				bean.setFeedbackUserName(user.getRealName());
				bean.setFeedbackTime(day);
				//判断ID是否有值
				if(bean.getId()!=null&&!bean.getId().equals("")) {
					String unitCode=bean.getUnitId();//核算对象id
					String queryDate=bean.getWriteDay();//日期
					String shiftId=bean.getShiftId();//班次
					//获取倒班信息
					List<ShiftForeignVo> listvo=unititeminfoservice.getShift( unitCode,  queryDate,  shiftId);//这个方法得改
					if(listvo!=null&&listvo.size()>0) {
						ShiftForeignVo e=listvo.get(0);
						bean.setSummaryDay(e.getTjsj());// 统计时间
						bean.setShiftBegintime(e.getSbsj());//班次开始时间
						bean.setShiftEndtime(e.getXbsj());//班次结束时间
						bean.setTeamId(e.getOrgCode());//班组ID
						bean.setTeamName(e.getOrgName());//班组名称
					}
					//更新
					listUp.add(bean);
				}else {
					//添加
					bean.setId(TMUID.getUID());
					bean.setFormId(bean.getId());//表单id
					String unitCode=bean.getUnitId();//核算对象id
					String queryDate=bean.getWriteDay();//日期
					String shiftId=bean.getShiftId();//班次
					//获取倒班信息
					List<ShiftForeignVo> listvo=unititeminfoservice.getShift( unitCode,  queryDate,  shiftId);//这个方法得改
					if(listvo!=null&&listvo.size()>0) {
						ShiftForeignVo e=listvo.get(0);
						bean.setSummaryDay(e.getTjsj());// 统计时间
						bean.setShiftBegintime(e.getSbsj());//班次开始时间
						bean.setShiftEndtime(e.getXbsj());//班次结束时间
						bean.setTeamId(e.getOrgCode());//班组ID
						bean.setTeamName(e.getOrgName());//班组名称
					}
//					private String batchNo;//批号--先手动录入
					listIn.add(bean);
				}
			}
		}
		int m1=1;
		int m2=1;
		if(listIn!=null&&listIn.size()>0) {
			m1=dbo.insertBatch(listIn);
		}
		if(listUp!=null&&listUp.size()>0) {
			m2=dbo.updateByIdBatch(listUp);
		}
		if(m1<1||m2<1) {
			n=0;
		}
		return n;
	}
	
	/**
	 * 删除数据
	 * @param list
	 * @return
	 */
	@Override
	public int deleteData(List<CostBatchOnDuty> list) {
		int m=dbo.deleteByIdBatch(list);
		return m;
	}
	/**
	 * 添加记录
	 * @param list
	 * @return
	 */
	@Override
	public boolean insertData(List<CostBatchOnDuty> list) {
		int m = dbo.insertBatch(list);
		boolean result = false;
		if(m>0) {
			result = true;
		}
		return result;
	}
	
	/**
	 * 修改记录
	 * @param list
	 * @return
	 */
	@Override
	public boolean updateData(List<CostBatchOnDuty> list) {
		int m = dbo.updateBatch(list);
		boolean result = false;
		if(m>0) {
			result = true;
		}
		return result;
	}
	
}
