package com.yunhesoft.mobile.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;

@Entity
@Data
@ApiModel("核算对象录入表（主表）")
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "ACCTOBJ_INPUT")
public class AcctobjInput extends BaseEntity {
    @ApiModelProperty(value = "业务活动ID", example = "BA：Business Activity")
    @Column(name = "BA_ID", length = 200)
    private String baId;

    @ApiModelProperty(value = "业务活动名称", example = "BA：Business Activity")
    @Column(name = "BA_NAME", length = 200)
    private String baName;

    @ApiModelProperty(value = "核算对象ID", example = "核算对象ID1")
    @Column(name = "ACCTOBJ_ID", length = 200)
    private String acctobjId;

    @ApiModelProperty(value = "核算对象名称", example = "核算对象名称1")
    @Column(name = "ACCTOBJ_NAME", length = 200)
    private String acctobjName;

    @ApiModelProperty(value = "录入时间", example = "2024-01-14 11:34:24")
    @Column(name = "INPUT_TIME")
    private Date inputTime;

    @ApiModelProperty(value = "录入时所在位置经度", example = "123")
    @Column(name = "INPUT_LONGITUDE")
    private Double inputLongitude;

    @ApiModelProperty(value = "录入时所在位置纬度", example = "123")
    @Column(name = "INPUT_LATITUDE")
    private Double inputLatitude;

    @ApiModelProperty(value = "作业半径", example = "123")
    @Column(name = "OPERATING_RADIUS")
    private Double operatingRadius;

    @ApiModelProperty(value = "班次代码", example = "班次代码1")
    @Column(length = 50)
    private String bcdm;

    @ApiModelProperty(value = "班次名称", example = "班次名称1")
    @Column(length = 200)
    private String bcmc;

    @ApiModelProperty(value = "上班时间", example = "2024-01-15 11:34:24")
    private Date sbsj;

    @ApiModelProperty(value = "下班时间", example = "2024-01-16 11:34:24")
    private Date xbsj;

    @ApiModelProperty(value = "班组ID", example = "班组ID1")
    @Column(name = "TEAM_ID", length = 50)
    private String teamId;

    @ApiModelProperty(value = "班组名称", example = "班组名称1")
    @Column(name = "TEAM_NAME", length = 200)
    private String teamName;

    @ApiModelProperty(value = "可用标识", example = "1=可用，0=无效")
    private Integer tmused;

    @Transient
    @ApiModelProperty("智能化岗位责任清单子活动实例 ID")
    private String taskId;

    @ApiModelProperty(value = "活动录入时间", example = "2024-01-14 11:34:24")
    @Column(name = "JOB_INPUT_TIME")
    private Date jobInputTime;
}
