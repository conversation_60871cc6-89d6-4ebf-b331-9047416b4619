package com.yunhesoft.rep.service.impl;

import com.yunhesoft.rep.entity.dto.RepInfoDto;
import com.yunhesoft.rep.entity.po.RepInfo;
import com.yunhesoft.rep.service.IRepInfoService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Service
public class RepInfoServiceImpl implements IRepInfoService {

    @Autowired
    private EntityService entityService;

    @Override
    public List<RepInfo> queryList(RepInfoDto dto) {
        Where where = new Where();
        if (dto != null) {
            //填充条件
        }
        return entityService.queryList(RepInfo.class,where);
    }

    /**
     * 保存数据
     * @param data
     * @return
     */
    @Override
    public Integer saveData(List<RepInfo> data) {
        data.forEach(item->{
            item.setId(UUID.randomUUID().toString());
            item.setTmused(1);
        });
        return entityService.insertBatch(data,500);
    }

    /**
     * 删除数据
     * @param data
     * @return
     */
    @Override
    public Integer deleteData(List<RepInfo> data) {
        return entityService.deleteByIdBatch(data);
    }
}
