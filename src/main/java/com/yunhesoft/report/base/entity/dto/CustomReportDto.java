package com.yunhesoft.report.base.entity.dto;

import com.yunhesoft.report.base.entity.po.ReportManage;
import com.yunhesoft.report.base.entity.po.ReportQuery;
import com.yunhesoft.report.base.entity.po.ReportRegion;
import lombok.Data;

import java.util.List;

@Data
public class CustomReportDto {
    ReportManage reportManage; //报表管理配置
    List<ReportQuery> reportQueryList; //报表查询条件
    List<ReportRegion> reportRegionList; //报表区域设置
    List<ReportRegion> rowL; //行区记录列表
    List<ReportRegion> colL; //列区记录列表
    List<ReportRegion> measureL; //度量记录列表
}
