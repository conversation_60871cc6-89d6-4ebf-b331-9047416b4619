package com.yunhesoft.report.base.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Entity
@Data
@ApiModel("报表管理查询条件")
@Table(name = "REPORT_QUERY")
public class ReportQuery extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "报表ID", example = "报表ID1")
    @Column(name = "REPORT_ID", length = 50)
    private String reportId;

    @ApiModelProperty(value = "条件名称", example = "条件名称1")
    @Column(name = "NAME", length = 200)
    private String name;

    @ApiModelProperty(value = "条件别名", example = "条件别名1")
    @Column(name = "ALIAS", length = 200)
    private String alias;

    @ApiModelProperty(value = "关系运算符", notes = "等于 不等于 包含 不包含 大于 大于等于 小于 小于等于 为空 不为空")
    @Column(name = "OPERATOR", length = 10)
    private String operator;

    @ApiModelProperty(value = "默认值", notes = "数据源公式")
    @Column(name = "DEF_VAL", length = 200)
    private String defVal;

    @ApiModelProperty(value = "控件类型", notes = "textfield=文本框 datefield=日期选择框 monthfield=月份选择框 datetimefield=日期时间选择器 yearfield=年份选择框 combo=下拉框")
    @Column(name = "COMPONENT_TYPE", length = 50)
    private String componentType;

    @ApiModelProperty(value="控件宽度", example="100")
    @Column(name="COMPONENT_WIDTH")
    private Integer componentWidth;

    @ApiModelProperty(value = "备选值", notes = "Json对象数组")
    @Column(name = "OPTION_VAL", length = 2000)
    private String optionVal;

    @ApiModelProperty(value = "绑定类型", notes = "1=数据源入参（透传） 2=数据源出参（过滤）")
    @Column(name = "BIND_TYPE")
    private Integer bindType;

    @ApiModelProperty(value = "绑定参数", example = "绑定参数1")
    @Column(name = "BIND_PARAM", length = 200)
    private String bindParam;

    @ApiModelProperty(value = "绑定参数别名", example = "绑定参数别名1")
    @Column(name = "BIND_PARAM_ALIAS", length = 200)
    private String bindParamAlias;

    @ApiModelProperty(value = "是否显示参数", notes = "1=显示 0=不显示")
    @Column(name = "SHOWED")
    private Integer showed;

    @ApiModelProperty(value = "排序号", notes = "排序号")
    @Column(name = "TMSORT")
    private Long tmsort;

    @ApiModelProperty(value = "可用标识", notes = "1=有效 0=无效")
    @Column(name = "TMUSED")
    private Integer tmused;

    @ApiModelProperty(value = "数据类型", notes = "tdsString=字符型 tdsDouble=数值型")
    @Column(length = 50)
    private String datatype;
    
    @ApiModelProperty(value = "读取数据源设置", notes = "1=读取 0=不读取")
    @Column(name = "READ_TDS_CONFIG")
    private Integer readTdsConfig;
}
