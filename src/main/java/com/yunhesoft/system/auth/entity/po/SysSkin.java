package com.yunhesoft.system.auth.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * 系统皮肤
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_SKIN")
public class SysSkin extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 皮肤名称 */
    @Column(name="SKINNAME", length=500)
    private String skinname;
    
    /** 排序 */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** 使用标识 */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

