package com.yunhesoft.system.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;

/**
 * xxl-job config
 *
 * <AUTHOR> 2022-03-10
 */
@Configuration
public class XxlJobConfig {

	private Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

	// 是否启用
	@Value("${xxl.job.enable:false}")
	private String enable;

	@Value("${xxl.job.admin.addresses:unknown}")
	private String adminAddresses;

	@Value("${xxl.job.accessToken:unknown}")
	private String accessToken;

	@Value("${xxl.job.executor.appname:tm4job}")
	private String appname;

	@Value("${xxl.job.executor.address:unknown}")
	private String address;

	@Value("${xxl.job.executor.ip:127.0.0.1}")
	private String ip;

	@Value("${xxl.job.executor.port:9998}")
	private int port;

	@Value("${xxl.job.executor.logpath:unknown}")
	private String logPath;

	@Value("${xxl.job.executor.logretentiondays:30}")
	private int logRetentionDays;

	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		if ("true".equals(enable)) {// 启用
			logger.info(">>>>>>>>>>> xxl-job config init.");
			XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
			xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
			xxlJobSpringExecutor.setAppname(appname);
			xxlJobSpringExecutor.setAddress(address);
			xxlJobSpringExecutor.setIp(ip);
			xxlJobSpringExecutor.setPort(port);
			xxlJobSpringExecutor.setAccessToken(accessToken);
			xxlJobSpringExecutor.setLogPath(logPath);
			xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
			return xxlJobSpringExecutor;
		} else {
			return null;
		}
	}
	/**
	 * 针对多网卡、容器内部署等情况，可借助 "spring-cloud-commons" 提供的 "InetUtils" 组件灵活定制注册IP；
	 *
	 * 1、引入依赖： <dependency> <groupId>org.springframework.cloud</groupId>
	 * <artifactId>spring-cloud-commons</artifactId> <version>${version}</version>
	 * </dependency>
	 *
	 * 2、配置文件，或者容器启动变量 spring.cloud.inetutils.preferred-networks: 'xxx.xxx.xxx.'
	 *
	 * 3、获取IP String ip_ = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
	 */

}