package com.yunhesoft.system.kernel.service.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 系统BLOB字段存储测试表(测试使用)
 * 
 */
//@Entity
@Setter
@Getter
//@Table(name = "SYS_BLOB_STORE_TEST")

public class SysBlobStoreTest extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 表名 */

	// @Column(name = "TABLE_NAME", length = 255)
	private String tableName;

	// @Lob
	// @Column(name = "BLOB_DATA")
	private String blobData;

	/** 排序 */
	// @Column(name = "TMSORT")
	private Integer tmSort;

}
