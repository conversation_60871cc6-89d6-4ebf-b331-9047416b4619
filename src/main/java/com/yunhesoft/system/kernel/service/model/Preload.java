package com.yunhesoft.system.kernel.service.model;

import com.yunhesoft.core.common.utils.EntityUtils;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @category 实体类的List类型的字段的预加载
 * <AUTHOR>
 *
 * @since 2021-12-2 15:18:35
 *
 *        开发过程中...暂未启用
 */
@Deprecated()
@Data
@EqualsAndHashCode(callSuper = false)
public class Preload extends ASQLObject {
	// 父实体类型
	private Class<?> pclazz;
	// 父实体类字段
	private String field;
	// 子实体类
	private Class<?> cclazz;
	// 子实体类表名
	private String ctable;
	// where表达式
	private Where on;
	// 是否是树形
	private boolean isTree;

	/**
	 * @param pclazz 父实体类型
	 * @param field  父实体字段名称
	 * @param cclazz 子实体类型
	 */
	public Preload(Class<?> pclazz, Func<?, ?> field, Class<?> cclazz) {
		this.field = resolveColumnName(field);
		this.cclazz = cclazz;
		on = Where.create(EntityUtils.tableName(pclazz));
		ctable = EntityUtils.tableName(cclazz);
	}

	/**
	 * @param pclazz 父实体类型
	 * @param field  父实体字段名称
	 * @param isTree 子实体类型
	 */
	public Preload(Class<?> pclazz, Func<?, ?> field, boolean isTree) {
		this.pclazz = pclazz;
		this.cclazz = pclazz;
		this.field = resolveColumnName(field);
		on = Where.create(EntityUtils.tableName(pclazz));
		ctable = EntityUtils.tableName(cclazz);
	}

	/**
	 * @category 父实体字段与子实体字段的等于关系
	 * @param pfunc 父字段
	 * @param cfunc 子字段
	 * @return
	 */
	public Preload on(Func<?, ?> pfunc, Func<?, ?> cfunc) {
		on.eq(pfunc, new Column(ctable, resolveColumnName(cfunc)));
		return this;
	}
}