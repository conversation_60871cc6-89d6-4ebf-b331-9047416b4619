package com.yunhesoft.system.kernel.service.model;

import lombok.Data;

@Data
public class TableColumnInfo {
	private String tableName;
	private Integer ordinalPosition;
	private String columnName;// 字段名
	private String columnType;
	private String columnComment;
	private String dataType;// 数据类型
	private Long charMaxlength; // 字符长度
	private Integer numPrecision; // 数字长度
	private Integer numScale;// 小数位数
	private boolean primaryKey;// 是否为主键
	private boolean isnullAble;// 是否允许为空
}
