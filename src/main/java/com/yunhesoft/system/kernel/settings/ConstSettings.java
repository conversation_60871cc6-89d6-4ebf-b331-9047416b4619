package com.yunhesoft.system.kernel.settings;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/**
 * @category 初始化常量接口
 * <AUTHOR>
 *
 * @since 2021-8-13 19:07:32
 */
@Documented
@Target(TYPE)
@Retention(RUNTIME)
public @interface ConstSettings {
	public String name() default "";
}
