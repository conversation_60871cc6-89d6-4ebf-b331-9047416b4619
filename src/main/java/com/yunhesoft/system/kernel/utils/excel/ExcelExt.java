package com.yunhesoft.system.kernel.utils.excel;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * EasyPoi Excel导出扩展注释
 * 
 * <AUTHOR> 2021.7.12
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface ExcelExt {

	/**
	 * 导出数据对齐方式（center,left,right）
	 */
	public String align() default "center";

	/**
	 * 单元格颜色 {a_red,b_green,v-else_black}<br>
	 * 单元格数据为a时显示红色，b是显示绿色,其他黑色
	 * 
	 * @return
	 */
	public String[] color() default "{}";
}
