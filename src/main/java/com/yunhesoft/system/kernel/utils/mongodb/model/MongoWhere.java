package com.yunhesoft.system.kernel.utils.mongodb.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.data.mongodb.core.query.Criteria;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

@Data
public class MongoWhere {
	// 逻辑运算符
	private String logic;
	// 字段
	private String field;
	// 操作符
	private String optionoperator = "=";
	// 值
	private List<Object> values = new ArrayList<Object>();
	// 子条件
	private MongoWheres wheres = new MongoWheres();

	public MongoWhere() {
	}

	public MongoWhere(MongoWheres parent, String logic, String field, String optionoperator, Object... values) {
		this.logic = logic;
		this.field = field;
		this.optionoperator = optionoperator;
		if (values != null) {
			for (Object v : values) {
				this.values.add(v);
			}
		}
	}

	@JSONField(serialize = false)
	@JsonIgnore
	public Object getValue() {
		if (this.values.size() > 0) {
			return this.values.get(0);
		} else {
			return null;
		}
	}

	public void setValue(Object value) {
		if (this.values.size() > 0) {
			this.values.set(0, value);
		} else {
			this.values.add(value);
		}
	}

	@JSONField(serialize = false)
	@JsonIgnore
	public Object getBetweenValue1() {
		if (this.values.size() > 0) {
			return this.values.get(0);
		} else {
			return null;
		}
	}

	@JSONField(serialize = false)
	@JsonIgnore
	public Object getBetweenValue2() {
		if (this.values.size() > 1) {
			return this.values.get(1);
		} else {
			return null;
		}
	}

	public String getLogic() {
		if (logic == null) {
			return "&&";
		} else {
			return logic;
		}
	}

//	public boolean hasChildren() {
//		return wheres.hasChildren();
//	}

	public StringBuffer sql(int i) {
		// TODO 未完 待继续完成
		StringBuffer sql = new StringBuffer();
		if (wheres.hasChildren()) {
			sql.append(logic).append(" ").append(wheres.sql());
		} else {
			if (i > 0) {
				sql.append(logic);
			}
			sql.append(" ").append(field).append(" ").append(optionoperator).append(" ");
			if (optionoperator.equals("between")) {
				sql.append(" ").append(this.values.get(0)).append(" and ").append(this.values.get(1));
			} else {
				sql.append(this.values.get(0));
			}

		}
		sql.append(" ");
		return sql;
	}

	public Criteria criteria() {
		Criteria criteria = null;
		if (this.wheres.hasChildren()) {
			criteria = this.wheres.criteria();
		} else {
			if ("=".equals(optionoperator)) {
				criteria = new Criteria().and(field).is(this.values.get(0));
			} else if ("like".equalsIgnoreCase(optionoperator)) {
				criteria = new Criteria().and(field).regex(".*?\\" + this.values.get(0) + ".*");
			} else if ("between".equalsIgnoreCase(optionoperator)) {
				criteria = new Criteria().and(field).gte(this.values.get(0))
						.lte(this.values.get(0) == null ? "" : this.values.get(1));// 小于等于 大于等于
			} else if (">".equals(optionoperator)) {
				criteria = new Criteria().and(field).gt(this.values.get(0));
			} else if (">=".equals(optionoperator)) {
				criteria = new Criteria().and(field).gte(this.values.get(0));
			} else if ("<=".equals(optionoperator)) {
				criteria = new Criteria().and(field).lte(this.values.get(0));
			} else if ("<".equals(optionoperator)) {
				criteria = new Criteria().and(field).lt(this.values.get(0));
			} else if ("!=".equals(optionoperator)) {
				criteria = new Criteria().and(field).ne(this.values.get(0));
			} else if ("in".contentEquals(optionoperator)) {
				criteria = new Criteria().and(field).in(this.values);
			} else if ("nin".contentEquals(optionoperator)) {
				criteria = new Criteria().and(field).nin(this.values);
			} else if ("regex".contentEquals(optionoperator)) {
				criteria = new Criteria().and(field).regex(String.valueOf(this.values.get(0)));
			}
		}

		return criteria;
	}
}