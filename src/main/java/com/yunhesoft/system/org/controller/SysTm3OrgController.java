package com.yunhesoft.system.org.controller;

import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.system.org.entity.dto.SysTm3OrgTreeList;
import com.yunhesoft.system.org.entity.vo.SysTm3OrgTree;
import com.yunhesoft.system.org.service.ISysTm3OrgDetailService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

@Api(tags = "获取TM3机构管理接口")
@RestController
@RequestMapping("/system/org/SysTm3OrgDetail")
public class SysTm3OrgController {

	@Autowired
	private ISysTm3OrgDetailService sysTm3OrgDetailService;

	@ApiOperation(value = "获取TM3机构对照信息", notes = "获取TM3机构对照信息")
	@RequestMapping(value = "/getTreeOrgList", method = { RequestMethod.GET })
	public SysTm3OrgTreeList getTreeOrgList(
			@ApiParam(value = "TM3机构代码,根节点是0") String id) {
		//查询根节点数据
		LinkedHashMap<String, List<SysTm3OrgTree>> orgTrees = sysTm3OrgDetailService.listTm3OrgData(id);
		//最终输出
		SysTm3OrgTreeList tm3OrgTreeList = sysTm3OrgDetailService.getSysTm3OrgTreeList(orgTrees);
		return tm3OrgTreeList;
	}

}
