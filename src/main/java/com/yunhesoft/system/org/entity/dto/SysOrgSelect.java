package com.yunhesoft.system.org.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SysOrgSelect {
	/**
	 * 过滤用户的父id（包含指定的filterOrgCode节点）
	 */
	@ApiModelProperty(value = "filterOrgCode")
	private String filterOrgCode;
	/**
	 * 父id （这个不包含porgcode节点，是特殊应用时的参数）
	 */
	@ApiModelProperty(value = "porgcode")
	private String porgcode;
	/**
	 * 包含父节点
	 */
	@ApiModelProperty(value = "includeNode")
	private String includeNode;
	/**
	 * 类型【all：全部】，【subordinate：紧取下级】
	 */
	@ApiModelProperty(value = "datatype")
	private String datatype;

	/**
	 * Excel导入类型 1：模板（用于添加数据） 2：数据（用于修改数据）
	 */
	@ApiModelProperty(value = "exportType")
	private String exportType;

	/**
	 * 不包含部门节点
	 */
	@ApiModelProperty(value = "notContainOrgcode")
	private String notContainOrgcode;

	/**
	 * 是否显示根节点 true：显示；false：不显示
	 */
	@ApiModelProperty(value = "showRootNode")
	private String showRootNode;

	/**
	 * 是否执行数据权限功能  true或者null 数据权限有效  false数据权限无效
	 */
	@ApiModelProperty(value = "dataPermissVaild")
	private Boolean dataPermissVaild;

	/**
	 * 机构范围  注意此功能启用时 数据权限失效
	 */
	@ApiModelProperty(value = "orgScope")
	private String orgScope;
	
	/**
	 *	只获取父机构树形
	 */
	@ApiModelProperty(value = "isParentTree")
	private Boolean isParentTree = false;
	private Boolean isSet;
	
	@ApiModelProperty(value = "可显示的机构类型：公司：company 分厂：factory 车间：workshop 部门：department 装置：equipment 班组：shiftteam 多选用逗号分隔")
	private String showOrgType;

	@ApiModelProperty(value = "默认机构")
	private String defaultValue;
	
}
