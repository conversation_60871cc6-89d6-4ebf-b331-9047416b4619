package com.yunhesoft.system.org.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;
/**
 * SysOrgRelation
 * @category SysOrgRelation接口
 * <AUTHOR>
 * @date 2020/03/10
 */
@Entity
@Setter
@Getter
@Table(name="SYS_ORG_RELATION")
public class SysOrgRelation extends BaseEntity{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Column(name = "orgcode" , length=50 )
	private String orgcode;
	
	@Column(name = "porgcode" , length=50 )
	private String porgcode;
	
	@Column(name = "version_date" , length=20 )
	private String versionDate;
	
	@Column(name = "used" , columnDefinition = "int default 0 " )
	private int used;
	
	/** 租户id 一般开发人员不需要考虑 **/
//	@Column(name = "TENANT_ID", columnDefinition = "varchar(50) default '0'", length = 50)
//	private String tenant_id = "0";
}
