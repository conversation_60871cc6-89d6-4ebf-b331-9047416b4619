package com.yunhesoft.system.org.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunhesoft.system.org.entity.po.SysOrgRelation;

/**
 * SysOrgRelationMapper
 * 
 * @category SysOrgRelation接口
 * <AUTHOR>
 * @date 2020/03/10
 */
public interface ISysOrgRelationService {
	/**
	 * 查所有
	 * 
	 * @return
	 */
	List<SysOrgRelation> listData();

	SysOrgRelation map2bean(LinkedHashMap val);

	List<SysOrgRelation> getDataFromDb();

	/**
	 * 根据id查询父id
	 * 
	 * @param id
	 * @return
	 */
	List<SysOrgRelation> listData(String id);

	/**
	 * 根据父id查询id
	 * 
	 * @param pid
	 * @return
	 */
	List<SysOrgRelation> listDataPids(String pid);
	
	/**
	 * 根据机构列表，查询机构的绑定关系
	 * 
	 * @param pid
	 * @return
	 */
	List<SysOrgRelation> listDataByOrgCodes(List<String> orgList);

	/**
	 * 保存
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean saveData(SysOrgRelation data);

	/**
	 * 更新
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean updateData(SysOrgRelation data);

	/**
	 * 插入
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean updateDataBatch(List<SysOrgRelation> list);

	/**
	 * 删除
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean deleteData(String id);

	/**
	 * 删除
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean deleteList(List<String> list);

	boolean insertData(List<SysOrgRelation> list);
	boolean insertDataWithTenant(List<SysOrgRelation> list,String tenantId);
	

	/**
	 * 添加或者更新
	 * 
	 * @param list
	 * @return
	 */
	boolean saveByOrgcode(List<SysOrgRelation> list);

	/**
	 * 获得机构关系map
	 * 
	 * @return
	 */
	Map<String, SysOrgRelation> getOrgRelation();

	/**
	 * 根据机构代码获取平级的所有机构
	 * @param orgCode 机构代码
	 * @return
	 */
	List<SysOrgRelation> getLevelOrg(String orgCode);

	/**
	 * 查所有使用中的
	 * 
	 * @return
	 */
	List<SysOrgRelation> getUsedDataList();

	Map<String, LinkedHashMap> getOrgRelationMapFromReids();

	void setOrgRelationToReids(List<SysOrgRelation> list);

	void initOrgRelationToReids();

	void clearRedis();
}
