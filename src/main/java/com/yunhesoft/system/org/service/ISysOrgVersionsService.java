package com.yunhesoft.system.org.service;

import java.util.List;

import com.yunhesoft.system.org.entity.po.SysOrgVersions;

/**
 * SysOrgVersionsMapper
 * 
 * @category SysOrgVersions接口
 * <AUTHOR>
 * @date 2020/03/10
 */
public interface ISysOrgVersionsService {
	/**
	 * 查询
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	List<SysOrgVersions> listData(String id);

	/**
	 * 保存
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean saveData(List<SysOrgVersions> list);

	/**
	 * 更新
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean updateData(List<SysOrgVersions> list);

	/**
	 * 插入
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean insertData(List<SysOrgVersions> list);

	/**
	 * 删除
	 * 
	 * @param list
	 * @param name
	 * @return
	 */
	boolean deleteData(String id);
}
