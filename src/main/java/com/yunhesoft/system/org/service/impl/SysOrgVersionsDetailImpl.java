package com.yunhesoft.system.org.service.impl;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.org.entity.po.SysOrgVersionsDetail;
//import com.yunhesoft.system.org.mapper.SysOrgVersionsDetailMapper;
import com.yunhesoft.system.org.service.ISysOrgVersionsDetailService;

/**
 * SysOrgVersionsDetailMapper
 * @category SysOrgVersionsDetail接口
 * <AUTHOR>
 * @date 2020/03/10
 */
@Service
public class SysOrgVersionsDetailImpl
		/*extends ServiceImpl<SysOrgVersionsDetailMapper, SysOrgVersionsDetail>*/ implements
		ISysOrgVersionsDetailService {
	@Autowired
	private EntityService entityService;

	@Override
	public List<SysOrgVersionsDetail> listData(String id) {
		List<SysOrgVersionsDetail> list = null;

		// LambdaQueryWrapper<SysOrgVersionsDetail> query = new LambdaQueryWrapper<SysOrgVersionsDetail>();
		// query.eq(SysOrgVersionsDetail::getId, id);
		// List<SysOrgVersionsDetail> list =this.list(query);

		Where where = Where.create();
		// where.and("id = ?", id);
		where.eq(SysOrgVersionsDetail::getId, id);
		list = entityService.queryList(SysOrgVersionsDetail.class, where);

		return list;
	}

	@Override
	@Transactional
	public boolean saveData(List<SysOrgVersionsDetail> list) {
		int rs = entityService.insertBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
		// return this.saveData(list);
	}

	@Override
	@Transactional
	public boolean updateData(List<SysOrgVersionsDetail> list) {
		int rs = entityService.updateByIdBatch(list);
		if (rs <= 0) {
			return false;
		}
		return true;
		// return this.updateData(list);
	}

	@Override
	@Transactional
	public boolean insertData(List<SysOrgVersionsDetail> list) {
		return this.saveData(list);
	}

	@Override
	@Transactional
	public boolean deleteData(String id) {
		Where where = Where.create();
		where.eq(SysOrgVersionsDetail::getId, id);
		int rs = entityService.delete(SysOrgVersionsDetail.class, where);
		if (rs <= 0) {
			return false;
		} else {
			return true;
		}
		// return this.removeById(id);
	}

}
