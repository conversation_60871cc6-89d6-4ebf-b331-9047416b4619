package com.yunhesoft.system.post.controller;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.post.entity.po.SysPost;
import com.yunhesoft.system.post.service.ISysDiyPost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.kernel.utils.excel.ExcelExport;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.post.entity.dto.PostCompParamDto;
import com.yunhesoft.system.post.entity.dto.PostDto;
import com.yunhesoft.system.post.entity.dto.PostParamDto;
import com.yunhesoft.system.post.entity.dto.SysPostHandleDrop;
import com.yunhesoft.system.post.entity.vo.PostCompVo;
import com.yunhesoft.system.post.entity.vo.PostVo;
import com.yunhesoft.system.post.service.IPostBasicOperationService;
import com.yunhesoft.system.post.service.ISysPostService;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * <AUTHOR>
 * @date 2020/03/12
 */
@Log4j2
@Api(tags = "岗位管理接口")
@RestController
@RequestMapping("/system/post")
public class PostController extends BaseRestController {
    /**
     * 基础操作服务接口（增、删、改、查）
     */
    @Autowired
    private IPostBasicOperationService bscOperSrv;

    @Autowired
    private ISysPostService iSysPostService;

    @Autowired
    private EntityService entityService;

    @Autowired
    private ISysDiyPost diyPost;

    /**
     * 添加岗位信息
     *
     * @param listDto
     * @return
     * @category 添加岗位信息
     */
    @ResponseBody
    @RequestMapping(value = "/add", method = {RequestMethod.POST})
    @ApiOperation(value = "添加岗位信息")
    @ApiImplicitParam(name = "listDto", value = "岗位信息列表", required = true)
    public Res<List<PostVo>> add(@RequestBody List<PostDto> listDto) {
        Res<List<PostVo>> resp = new Res<List<PostVo>>();
        resp.setSuccess(true);
        resp.setMessage("添加岗位信息成功");
        // 验重
        List<String> names = listDto.stream().map(i -> i.getName()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(names)) {
            Where where = Where.create();
            where.in(SysPost::getName, names.toArray());
            if (StringUtils.isNotEmpty(listDto.get(0).getOrgCode())) {
                where.eq(SysPost::getOrgCode, listDto.get(0).getOrgCode());
                where.eq(SysPost::getUsed, 1);
            }
            int sum = entityService.queryCount(SysPost.class, where).intValue();
            if (sum > 0) {
                resp.setSuccess(false);
                resp.setMessage("禁止添加重复的岗位");
                return resp;
            }
        }

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("添加岗位信息失败（数据为空）");
                return resp;
            }
            List<PostVo> newPostVoList = bscOperSrv.addPost(listDto);
            if (newPostVoList != null && newPostVoList.size() > 0) {
                resp.setResult(newPostVoList);
            } else {
                resp.setSuccess(false);
                resp.setMessage("添加失败，请查看日志");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 删除岗位信息
     *
     * @param listDto
     * @return
     * @category 删除岗位信息
     */
    @ResponseBody
    @RequestMapping(value = "/delete", method = {RequestMethod.POST})
    @ApiOperation(value = "删除岗位信息")
    @ApiImplicitParam(name = "listDto", value = "岗位信息列表", required = true)
    public Res<String> delete(@RequestBody List<PostDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("删除岗位信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("删除岗位信息失败（数据为空）");
                return resp;
            }
            String err = bscOperSrv.delPost(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("删除岗位信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 修改岗位信息
     *
     * @param listDto
     * @return
     * @category 修改岗位信息
     */
    @ResponseBody
    @RequestMapping(value = "/update", method = {RequestMethod.POST})
    @ApiOperation(value = "修改岗位信息")
    @ApiImplicitParam(name = "listDto", value = "岗位信息列表", required = true)
    public Res<String> update(@RequestBody List<PostDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("修改岗位信息成功");

        // 验重
        List<String> names = listDto.stream().map(i -> i.getName()).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(names)) {
            Where where = Where.create();
            where.in(SysPost::getName, names.toArray());
            if (StringUtils.isNotEmpty(listDto.get(0).getOrgCode())) {
                where.eq(SysPost::getOrgCode, listDto.get(0).getOrgCode());
                where.eq(SysPost::getUsed, 1);
            }
            List<SysPost> lp = entityService.rawQueryListByWhere(SysPost.class, where);
            if (StringUtils.isNotEmpty(lp)) {
                List<String> ids = lp.stream().map(i -> i.getId()).collect(Collectors.toList());
                for (PostDto postDto : listDto) {
                    if (!ids.contains(postDto.getId())) {
                        resp.setSuccess(false);
                        resp.setMessage("禁止重命名重复的岗位");
                        return resp;
                    }
                }
            }
        }
        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("修改岗位信息失败（数据为空）");
                return resp;
            }
            String err = bscOperSrv.updPost(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("修改岗位信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    /**
     * 移动岗位信息节点，跨父节点移动
     *
     * @param listDto
     * @return
     * @category 移动岗位信息节点
     */
    @ResponseBody
    @RequestMapping(value = "/movePost", method = {RequestMethod.POST})
    @ApiOperation(value = "移动岗位信息")
    @ApiImplicitParam(name = "listDto", value = "岗位信息列表", required = true)
    public Res<String> movePost(@RequestBody List<PostDto> listDto) {
        Res<String> resp = new Res<String>();
        resp.setSuccess(true);
        resp.setMessage("移动岗位信息成功");

        try {
            if (listDto == null || listDto.size() <= 0) {
                resp.setSuccess(false);
                resp.setMessage("移动岗位信息失败（数据为空）");
                return resp;
            }
            String err = bscOperSrv.movePost(listDto);
            if (err != null && !"".equals(err)) {
                resp.setSuccess(false);
                resp.setMessage("移动岗位信息失败（" + err + "）");
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }

        return resp;
    }

    @ResponseBody
    @RequestMapping(value = "/getPostChildren", method = {RequestMethod.POST})
    @ApiOperation(value = "获取岗位信息下一层子节点")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true)
    public Res<List<PostVo>> getPostChildren(@RequestBody PostParamDto paramDto) {
        List<PostVo> list = new ArrayList<PostVo>();
        Res<List<PostVo>> resp = new Res<List<PostVo>>();
        resp.setSuccess(true);

        try {
            list = bscOperSrv.getPostChildren(paramDto);
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        resp.setResult(list);

        return resp;
    }

    @ResponseBody
    @RequestMapping(value = "/getPostCompenent", method = {RequestMethod.POST})
    @ApiOperation(value = "获取岗位信息下一层子节点（组件使用）")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true)
    public Res<List<PostCompVo>> getPostCompenent(@RequestBody PostCompParamDto paramDto) {
        List<PostCompVo> list = new ArrayList<PostCompVo>();
        Res<List<PostCompVo>> resp = new Res<List<PostCompVo>>();
        resp.setSuccess(true);

        try {
            if (paramDto == null) {
                return resp;
            }

            PostParamDto pmDto = new PostParamDto();
            pmDto.setId(paramDto.getPid());
            pmDto.setUsed(1);
            List<PostVo> postListVo = bscOperSrv.getPostChildren(pmDto);
            if (postListVo != null && postListVo.size() > 0) {
                for (PostVo postVo : postListVo) {
                    PostCompVo postCompVo = new PostCompVo();
                    postCompVo.setId(postVo.getId());
                    postCompVo.setLabel(postVo.getName());
                    postCompVo.setPid(postVo.getPcode());
                    // 查询子节点
                    pmDto.setId(postVo.getId());
                    List<PostVo> childrenListVo = bscOperSrv.getPostChildren(pmDto);
                    if (childrenListVo != null && childrenListVo.size() > 0) {
                        postCompVo.setHasChildren(true);

                        List<PostCompVo> childrenCompVoList = new ArrayList<PostCompVo>();
                        for (PostVo child : childrenListVo) {
                            PostCompVo childCompVo = new PostCompVo();
                            childCompVo.setId(child.getId());
                            childCompVo.setLabel(child.getName());
                            childCompVo.setPid(child.getPcode());
                            childrenCompVoList.add(childCompVo);
                        }
                        postCompVo.setChildren(childrenCompVoList);
                    } else {
                        postCompVo.setHasChildren(false);
                    }
                    list.add(postCompVo);
                }
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        resp.setResult(list);

        return resp;
    }

    @ResponseBody
    @RequestMapping(value = "/getPostChildrenAll", method = {RequestMethod.POST})
    @ApiOperation(value = "获取岗位信息所有层级子节点")
    @ApiImplicitParam(name = "paramDto", value = "请求参数", required = true)
    public Res<List<PostVo>> getPostChildrenAll(@RequestBody PostParamDto paramDto) {
        List<PostVo> list = new ArrayList<PostVo>();
        Res<List<PostVo>> resp = new Res<List<PostVo>>();
        resp.setSuccess(true);
        Pagination<?> page = null;
        if (paramDto.getPageSize() != null && paramDto.getPageNum() != null) {
            page = Pagination.create(paramDto.getPageNum(), paramDto.getPageSize());
        }
        try {
            if (StringUtils.isNotEmpty(diyPost.isUseOrgDiyPost()) && StringUtils.isNotEmpty(paramDto.getOrgCode())) {
                List<PostVo> diyPostByOrgCode = diyPost.getDiyPostByOrgCode(paramDto.getOrgCode(), page, null);
                if (StringUtils.isNotEmpty(diyPostByOrgCode)) {
                    PostVo postVo = diyPostByOrgCode.get(0);
                    list.addAll(postVo.getChildren());
                }
            } else {
                list = bscOperSrv.getPostChildrenAll(paramDto, page);
            }
            if (page != null) {
                resp.setTotal(page.getTotal());// 总数量赋值
            }
        } catch (Exception e) {
            log.error("", e);
            resp.setSuccess(false);
            resp.setMessage("后台处理有误，请查看日志");
        }
        resp.setResult(list);

        return resp;
    }

    @RequestMapping(value = "/handleDropSysPost", method = RequestMethod.POST)
    @ApiOperation(value = "岗位拖拽调节位置")
    public Res<?> handleDropSysPost(@RequestBody SysPostHandleDrop sysPostHandleDrop) {
        Res<String> res = new Res<String>();
        boolean bool = iSysPostService.handleDropSysPost(sysPostHandleDrop);
        if (bool) {
            res.setSuccess(true);
            res.setMessage("移动成功");
        } else {
//            res.setSuccess(false);
//            res.setError("移动失败");
            res.fail("移动失败");
        }
        return res;
    }

    @ApiOperation(value = "获取岗位列表", notes = "获取岗位列表")
    @RequestMapping(value = "getPostList", method = {RequestMethod.GET})
    public Res<?> getPostList() {
        return Res.OK(iSysPostService.getPostList(false));
    }

    @ResponseBody
    @RequestMapping(value = "/isPostUsing", method = {RequestMethod.POST})
    @ApiOperation(value = "判断岗位是否被其他模块使用")
    @ApiImplicitParam(name = "paramDto", value = "请求参数-岗位ID", required = true)
    public Res<?> isPostUsing(@RequestBody PostParamDto paramDto) {
        String str = "";
        if (iSysPostService.isPostUsing(paramDto.getId())) {
            str = "该岗位已被人员信息使用，无法删除！";
        }
        return Res.OK(str);
    }

    /**
     * 导出Excel
     *
     * @param querySysRoleDto
     */
    @RequestMapping(value = "/toExcel", method = RequestMethod.POST)
    @ApiOperation(value = "岗位信息导出Excel")
    public void toExcel(@RequestBody PostParamDto paramDto) {
        try {
            List<PostVo> listData = new ArrayList<PostVo>();
            if ("2".equals(paramDto.getExportType())) {// 导出数据
                List<PostVo> list = new ArrayList<>();
                if (StringUtils.isNotEmpty(diyPost.isUseOrgDiyPost()) && StringUtils.isNotEmpty(paramDto.getOrgCode())) {
                    list = diyPost.getDiyPostByOrgCode(paramDto.getOrgCode(), null, null);
                } else {
                    list = bscOperSrv.getPostChildrenAll(paramDto, null);
                }
                if (StringUtils.isNotEmpty(list)) {
                    listData.addAll(list.get(0).getChildren());
                }
            } else {// 导出空模板

            }
            Map<String, List<String>> selectMap = new HashMap<String, List<String>>();//
            List<String> list = new ArrayList<>();
            list.add("是");
            list.add("否");
            selectMap.put("isHead", list);// 角色下拉框
            String sub = "1、ID列为标识列，空代表添加记录，否则为修改记录；2、标题名称不可以修改。";
            ExcelExport.exportExcel("岗位信息", sub, false, PostVo.class, listData, selectMap, response);
        } catch (Exception e) {
            log.error("岗位导出", e);
        }
    }

    @SuppressWarnings("unchecked")
    @ApiOperation(value = "岗位信息数据导入")
    @RequestMapping(value = "/import", method = {RequestMethod.POST})
    public Res<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("params") String params) throws Exception {
        ExcelImportResult<?> result = ExcelImport.importExcel(file.getInputStream(), PostVo.class, 2, 1, false);
        if (result != null) {
            if (result.isVerifyFail()) {// 校验失败
                return Res.OK("数据校验失败！");// Res.OK(result.getFailList());// 校验失败的数据
            }
            List<?> list = result.getList();// 导入的结果数据
            if (StringUtils.isNotEmpty(list)) {
                List<PostVo> dataList = (List<PostVo>) list;
                //验空
                List<PostVo> emptyList = dataList.stream().filter(i -> StringUtils.isEmpty(i.getName())).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(emptyList)) {
                    Res resp = new Res();
                    resp.setSuccess(false);
                    resp.setMessage("导入数据名称不能为空");
                    return resp;
                }
                // 验重
                List<String> names = dataList.stream().map(i -> i.getName()).collect(Collectors.toList());
                if (StringUtils.isNotEmpty(names)) {
                    //判断添加记录列表本身具有重复项
                    Set<String> set = new HashSet<>();
                    set.addAll(names);
                    if (set.size() < names.size()) {
                        Res resp = new Res();
                        resp.setSuccess(false);
                        resp.setMessage("禁止重命名重复的岗位");
                        return resp;
                    }
                    //查验数据库中的重复项
                    Where where = Where.create();
                    where.in(SysPost::getName, names.toArray());
                    where.eq(SysPost::getUsed, 1);
                    if (StringUtils.isNotEmpty(diyPost.isUseOrgDiyPost())) {
                        JSONObject paramObj = JSONObject.parseObject(params);
                        String orgCode = paramObj.getString("orgCode");
                        where.eq(SysPost::getOrgCode, orgCode);
                    }
                    List<SysPost> lp = entityService.rawQueryListByWhere(SysPost.class, where);
                    if (StringUtils.isNotEmpty(lp)) {
                        Map<String, Set<String>> ids = lp.stream().collect(Collectors.groupingBy(SysPost::getName, Collectors.mapping(SysPost::getId, Collectors.toSet())));
                        for (PostVo postDto : dataList) {
                            Set<String> strings = ids.get(postDto.getName());
                            if (StringUtils.isNotEmpty(strings) && !strings.contains(postDto.getId())) {
                                Res resp = new Res();
                                resp.setSuccess(false);
                                resp.setMessage("禁止重命名重复的岗位");
                                return resp;
                            }
                        }
                    }
                }
                return Res.OK(bscOperSrv.importPostData(dataList, params));
            } else {
                return Res.OK("未解析出导入数据！");
            }
        }
        return Res.FAIL("");
    }


    /**
     * 通过岗位系数更新排序
     *
     * @param querySysRoleDto
     */
    @RequestMapping(value = "/updateSortByCoefficient", method = RequestMethod.POST)
    @ApiOperation(value = "通过岗位系数更新排序")
    public Res<?> updateSortByCoefficient(@RequestBody PostParamDto paramDto) {
        boolean bln = false;
        try {
            paramDto.setPageNum(1);
            paramDto.setPageSize(0);
            paramDto.setPostName("");
            List<PostVo> listData = new ArrayList<PostVo>();
            List<PostVo> list = new ArrayList<>();
            if (StringUtils.isNotEmpty(diyPost.isUseOrgDiyPost()) && StringUtils.isNotEmpty(paramDto.getOrgCode())) {
                list = diyPost.getDiyPostByOrgCode(paramDto.getOrgCode(), null, null);
            } else {
                list = bscOperSrv.getPostChildrenAll(paramDto, null);
            }
            if (StringUtils.isNotEmpty(list)) {
                listData.addAll(list.get(0).getChildren());
                bln = bscOperSrv.updateSortByCoefficient(listData);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return Res.OK(bln);
    }

}
