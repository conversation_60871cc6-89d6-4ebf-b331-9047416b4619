package com.yunhesoft.system.queue.redis.impl;

import java.util.HashMap;
import java.util.Map;

import com.yunhesoft.system.queue.redis.RedisQueueConsumer;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class RedisQueueConsumerContainer {

	// 存放消费者的map key=消费者 queueName value=消费者对象
	private static Map<String, RedisQueueConsumer> consumerMap = new HashMap<>();

	// 容器是否初始化完毕的标示
	public static Boolean isRun = false;
	RedisQueueThreadPool redisQueueThreadPool;

	public static void addConsumer(RedisQueueConsumer consumer) {
		if (consumer.getQueueName() == null) {
			log.error("【添加redis队列失败】:{}", "队列名称为null");
		} else if (null == consumerMap.get(consumer.getQueueName())) {
			consumerMap.put(consumer.getQueueName(), consumer);
			log.error("【添加redis队列成功】:{}", consumer.getQueueName());
			newRedisQueueThreadPool(consumer);

		}
	}

	public void destroy() {
		log.info("redis消费者容器销毁");
		redisQueueThreadPool.destroy();
	}

	public void init() {
		log.info("redis消费者容器初始化开始");
		isRun = true;
		if (consumerMap.size() > 0) {
			redisQueueThreadPool = RedisQueueThreadPool.getInstance(consumerMap.size());
			consumerMap.forEach((queueName, consumer) -> {
				redisQueueThreadPool.executor(new RedisQueueListener(consumer));
			});
		}
	}

	public static void newRedisQueueThreadPool(RedisQueueConsumer consumer) {
		System.out.println("增加消费者");
		RedisQueueThreadPool redisQueueThreadPool = RedisQueueThreadPool.getInstance(consumerMap.size());
		redisQueueThreadPool.executor(new RedisQueueListener(consumer));
	}
}