package com.yunhesoft.system.queue.redis.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.yunhesoft.core.utils.RedisUtil;

/**
 * 生产者工具
 * 
 * <AUTHOR>
 *
 */
@Component
public class RedisQueueProducerService {
	@Autowired
	private RedisUtil redis;

	/**
	 * 向redis写入消息
	 * 
	 * @param queueName
	 * @param message
	 */
	public void sendMessage(String queueName, Object message) {
		queueName = "queue:" + queueName;
		redis.lLeftPush(queueName, message);
	}

}