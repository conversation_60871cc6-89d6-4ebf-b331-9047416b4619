package com.yunhesoft.system.role.entity.dto;

import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "添加角色信息", description = "添加角色信息与校验")
public class AddRoleDto {

	@ApiModelProperty(value = "id")
    private String id;
	
    @ApiModelProperty(value = "pid")
    @NotBlank(message = "pid不能为空")
    private String pid;

    @ApiModelProperty(value = "name")
    @NotBlank(message = "name不能为空")
    private String name;

    @ApiModelProperty(value = "description")
    private String description;

    private Integer roleLevel;

    @ApiModelProperty(value = "permids")
    private List<String> permids;
}
