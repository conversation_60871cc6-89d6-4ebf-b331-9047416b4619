package com.yunhesoft.system.role.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色拥有的人员列表
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "角色拥有的人员列表", description = "角色拥有的人员列表")
public class EmpRoleDto {

	/** 角色id */
	@ApiModelProperty(value = "roleid")
	private String roleid;

	/** 人员姓名 */
	@ApiModelProperty(value = "empname")
	private String empname;

	/** 分页数量 */
	@ApiModelProperty(value = "pageSize")
	private Integer pageSize;
	
	/** 当前页 */
	@ApiModelProperty(value = "pageNum")
	private Integer pageNum;
	
	
	private Integer recordCount;

}
