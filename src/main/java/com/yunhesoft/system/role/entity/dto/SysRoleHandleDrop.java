package com.yunhesoft.system.role.entity.dto;

import lombok.Data;

@Data
public class SysRoleHandleDrop {
    private String sourceRoleCode;

    private String sourcePRoleCode;

    private String targetRoleCode;

    private String targetPRoleCode;

    /**
     * 1代表同一角色组内上下移动，2代表跨角色组移动且有排序位置，3代表放置进其他角色组。
     */
    private Integer moveType;

    /**
     * 1代表之前，2代表之后。
     */
    private Integer location;
}
