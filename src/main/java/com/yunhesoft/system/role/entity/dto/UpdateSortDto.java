package com.yunhesoft.system.role.entity.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "上移下移操作信息", description = "上移下移操作信息与校验")
public class UpdateSortDto {

    @ApiModelProperty(value = "sourceId")
    @NotBlank(message = "源头id不能为空")
    private String sourceId;

    @ApiModelProperty(value = "sourceId")
    @NotBlank(message = "目标id不能为空")
    private String targetId;

}
