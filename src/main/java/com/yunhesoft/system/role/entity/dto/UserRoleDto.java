package com.yunhesoft.system.role.entity.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 人员角色关系信息查询参数
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "人员角色关系信息查询参数", description = "人员角色关系信息查询参数")
public class UserRoleDto {

	/** 人员信息id */
	@ApiModelProperty(value = "userid")
	@NotBlank(message = "员工ID不能为空")
	private String userid;

	/** 角色id */
	@ApiModelProperty(value = "roleid")
	private String roleid;
}
