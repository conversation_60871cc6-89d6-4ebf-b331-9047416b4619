package com.yunhesoft.system.role.service;

import java.util.List;

import com.yunhesoft.system.role.entity.dto.UserRoleDto;
import com.yunhesoft.system.role.entity.po.SysUserRole;

/**
 * 人员角色表操作接口
 * 
 * @category 人员角色表操作接口
 * <AUTHOR>
 * @date 2021/04/23
 */
public interface ISysUserRoleService {
	/**
	 * 批量添加人员角色关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	boolean addBatch(List<SysUserRole> listPo);

	boolean addBatchWithTenant(List<SysUserRole> listPo, String tenantId);

	/**
	 * 批量删除人员角色关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	boolean deleteBatchById(List<SysUserRole> listPo);

	/**
	 * 批量更新人员角色关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	boolean updateBatchByIds(List<SysUserRole> listPo);

	/**
	 * 获取人员角色关系信息
	 * 
	 * @param param
	 * @return
	 */
	List<SysUserRole> getUserRole(UserRoleDto param);

	/**
	 * 
	 * @param type 1:超级管理员; -1:未注册用户
	 * @return
	 */
	List<SysUserRole> getDefaultUserRole(String userId, int type);

	/**
	 * 通过人员id和角色id删除角色关系
	 * 
	 * @param param
	 * @return
	 */
	boolean deleteUserRoleByUserId(UserRoleDto param);

	/**
	 * 通过人员id删除角色关系
	 * 
	 * @param userIds 逗号分隔
	 * @return
	 */
	boolean deleteUserRoleByUserIds(List<String> userIds);

	/**
	 * 获取人员角色关系信息
	 * 
	 * @param userIds
	 * @return
	 */
	List<SysUserRole> getUserRole(List<String> userIds);

	/**
	 * 查询角色是否有人使用
	 * 
	 * @param roleId
	 * @return
	 */
	int getUserRoleCount(String roleId);

	/**
	 * 根据角色列表获取人员id
	 * 
	 * @param roleidList 角色列表
	 * @return
	 */
	List<String> getUserIds(List<String> roleidList);

	/**
	 * 初始化用户角色信息到redis
	 */
	void initUserRoleData();

}
