package com.yunhesoft.system.role.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.menu.service.SysMenuService;
import com.yunhesoft.system.role.entity.dto.SysRolePermDto;
import com.yunhesoft.system.role.entity.po.SysRolePerm;
import com.yunhesoft.system.role.service.ISysRolePermService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SysRolePermServiceImpl implements ISysRolePermService {

    @Autowired
    private EntityService dao;

    @Autowired
    private AuthService authServ;

    @Autowired
    private RedisUtil redis;

    @Autowired
    private SysMenuService sysMenuService;

    private final static String RED_KEY = "SYSTEM:ROLEPERM_INFO";

    /**
     * 批量添加角色权限关系信息
     *
     * @param list
     * @param listPo
     * @return
     */
    @Override
    public boolean addBatch(List<SysRolePerm> listPo) {
        boolean flag = false;
        if (listPo != null && listPo.size() > 0) {
            for (SysRolePerm sysRolePerm : listPo) {
                if (StringUtils.isEmpty(sysRolePerm.getId())) {
                    sysRolePerm.setId(TMUID.getUID());
                }
            }
            int rtn = dao.insertBatch(listPo);
            flag = (rtn == 0 ? false : true);
            if (flag) {
                authServ.removeAllUserFromRedis();
                this.initRolePermData();
            }
        }
        return flag;
    }

    /**
     * 批量添加
     */
    @Override
    public boolean addBatch(String roleid, List<String> permids) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(permids)) {
            List<SysRolePerm> list = new ArrayList<SysRolePerm>();
            List<String> idList = new ArrayList<>();
            for (String permid : permids) {
                if (permid.startsWith("B_") || permid.startsWith("M_")) {
                    String perm = permid.substring(2);
                    if (!idList.contains(perm)) {
                        idList.add(perm);
                        SysRolePerm bean = new SysRolePerm();
                        bean.setId(TMUID.getUID());
                        bean.setCreateTime(new Date());
                        bean.setRoleid(roleid);
                        bean.setPermid(perm);
                        list.add(bean);
                    }
                }
            }
            if (list.size() > 0) {
                bln = this.addBatch(list);
                if (bln) {
                    authServ.removeAllUserFromRedis();
                    this.initRolePermData();
                }
            }
        }
        return bln;
    }

    /**
     * 批量添加（不删除，只添加）
     */
    @Override
    public boolean saveBatch(List<String> roleids, List<String> permids) {
        boolean bln = false;
        if (StringUtils.isNotEmpty(roleids) && StringUtils.isNotEmpty(permids)) {
            List<SysRolePerm> list = new ArrayList<SysRolePerm>();
            for (String roleid : roleids) {
                List<String> listPerm = this.getRolePerm(roleid, false); // 权限列表
                for (String permid : permids) {
                    if (permid.startsWith("B_") || permid.startsWith("M_")) {
                        String perm = permid.substring(2);
                        if (!listPerm.contains(perm)) {
                            SysRolePerm bean = new SysRolePerm();
                            bean.setId(TMUID.getUID());
                            bean.setCreateTime(new Date());
                            bean.setRoleid(roleid);
                            bean.setPermid(perm);
                            list.add(bean);
                        }
                    }
                }
            }
            if (list.size() > 0) {
                if (dao.insertBatch(list) > 0) {
                    authServ.removeAllUserFromRedis();
                    this.initRolePermData();
                    bln = true;
                }
            }
        }
        return bln;
    }

    /**
     * 批量删除关系信息
     *
     * @param list
     * @param listPo
     * @return
     */
    @Override
    public boolean deleteBatchById(List<SysRolePerm> listPo) {
        boolean flag = false;
        if (listPo != null && listPo.size() > 0) {
            int rtn = dao.deleteByIdBatch(listPo);
            flag = (rtn == 0 ? false : true);
            if (flag) {
                authServ.removeAllUserFromRedis();
                this.initRolePermData();
            }
        }
        return flag;
    }

    /**
     * 批量更新关系信息
     *
     * @param list
     * @param listPo
     * @return
     */
    @Override
    public boolean updateBatchByIds(List<SysRolePerm> listPo) {
        boolean flag = false;
        try {
            if (listPo != null && listPo.size() > 0) {
                int rtn = dao.updateByIdBatch(listPo);
                flag = (rtn == 0 ? false : true);
                if (flag) {
                    authServ.removeAllUserFromRedis();
                    this.initRolePermData();
                }
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return flag;
    }

    /**
     * 获取角色权限关系信息
     *
     * @param param
     * @return
     */
    @Override
    public List<SysRolePerm> getRolePerm(SysRolePermDto paramDto) {
        if (StringUtils.isEmpty(paramDto.getRoleid()) && StringUtils.isEmpty(paramDto.getPermid())) {
            return null;
        }
        Where where = Where.create();
        if (StringUtils.isNotEmpty(paramDto.getRoleid())) {
            where.eq(SysRolePerm::getRoleid, paramDto.getRoleid());
        }
        if (StringUtils.isNotEmpty(paramDto.getPermid())) {
            where.eq(SysRolePerm::getPermid, paramDto.getPermid());
        }
        return dao.rawQueryListByWhere(SysRolePerm.class, where);
    }
    /**
     * 获取角色权限关系信息
     *
     * @param param
     * @return
     */
    private List<SysRolePerm> getRolePerm(List<String> permIds) {
    	List<SysRolePerm> result = new ArrayList<SysRolePerm>();
   	 	if (StringUtils.isNotEmpty(permIds)) {
   		Where where = Where.create();
       	if(permIds.size()==1) {
       		where.eq(SysRolePerm::getPermid, permIds.get(0));
       	}else {
       		where.in(SysRolePerm::getPermid, permIds.toArray());
       	}
       	result =dao.rawQueryListByWhere(SysRolePerm.class, where);
       }
       return result;
    }
    /**
     * 根据权限id获取对应的角色id
     *
     * @param permId 权限id
     * @return
     */
    @Override
    public List<String> getRoleIds(String permId) {
        List<String> rtnList = new ArrayList<String>();
        SysRolePermDto paramDto = new SysRolePermDto();
        paramDto.setPermid(permId);
        List<SysRolePerm> list = this.getRolePerm(paramDto);
        if (StringUtils.isNotEmpty(list)) {
            rtnList = list.stream().map(SysRolePerm::getRoleid).collect(Collectors.toList());
        }
        return rtnList;
    }

	/**
	 * 根据权限id获取对应的角色id
	 * 
	 * @param permIds 权限id
	 * @return
	 */
    @Override
    public List<String> getRoleIds(List<String> permIds){
	  List<String> rtnList = new ArrayList<String>();
      List<SysRolePerm> list = this.getRolePerm(permIds);
      if (StringUtils.isNotEmpty(list)) {
          rtnList = list.stream().map(SysRolePerm::getRoleid).collect(Collectors.toList());
      }
      return rtnList;
    }
    /**
     * 获取角色拥有的权限
     *
     * @param param
     * @return
     */
    @Override
    public List<String> getRolePerm(String roleid, boolean isforTree) {
        List<String> rtnList = new ArrayList<String>();
        SysRolePermDto paramDto = new SysRolePermDto();
        paramDto.setRoleid(roleid);
        List<SysRolePerm> list = this.getRolePerm(paramDto);
        if (StringUtils.isNotEmpty(list)) {
            Map<String, List<String>> menuMap = null;
            if (isforTree) {
                menuMap = sysMenuService.getButtonMap();
            }
            for (SysRolePerm sysRolePerm : list) {
                if (isforTree) {// 资源树使用
                    rtnList.add("B_" + sysRolePerm.getPermid());
                    //判断是否为页面权限，如果页面下没有按钮权限的话改为菜单选择中状态
                    if (!menuMap.containsKey(sysRolePerm.getPermid())) {
                        rtnList.add("M_" + sysRolePerm.getPermid());
                    }
                } else {
                    rtnList.add(sysRolePerm.getPermid());
                }
            }
        }
        return rtnList;
    }

    /**
     * 通过角色id获取权限信息
     *
     * @param roleIds 角色列表
     * @param menuId  菜单ID
     * @return
     */
    @Override
    public List<SysRolePerm> getRolePermByIds(List<String> roleIds, String permId) {
        if (StringUtils.isEmpty(roleIds)) {
            return null;
        }
        Where where = Where.create();
        Order order = Order.create();
        if (roleIds.size() == 1) {
            where.eq(SysRolePerm::getRoleid, roleIds.get(0));
        } else {
            where.in(SysRolePerm::getRoleid, roleIds.toArray());
            order.orderByAsc(SysRolePerm::getRoleid);// order.order(SysRolePerm::getRoleid, "ASC");
        }
        if (StringUtils.isNotEmpty(permId)) {
            where.eq(SysRolePerm::getPermid, permId);
        }
        return dao.rawQueryListByWhere(SysRolePerm.class, where, order);
    }

    private void setRolePermToRedis(Map<String, String> dataMap) {
        redis.setMap(RED_KEY, dataMap);
    }

    private void deleteRolePermFromRedis(String roleId) {
        redis.hDelete(RED_KEY, roleId);
    }

    /**
     * 初始化数据到redis
     */
    @Override
    public void initRolePermData() {
        List<SysRolePerm> list = dao.rawQueryListByWhereDisableTenant(SysRolePerm.class, null);
        if (StringUtils.isNotEmpty(list)) {
            redis.delete(RED_KEY);
            Map<String, List<SysRolePerm>> map = new HashMap<String, List<SysRolePerm>>();
            for (SysRolePerm e : list) {
                if (map.containsKey(e.getRoleid())) {
                    map.get(e.getRoleid()).add(e);
                } else {
                    List<SysRolePerm> l = new ArrayList<SysRolePerm>();
                    l.add(e);
                    map.put(e.getRoleid(), l);
                }
            }
            Map<String, String> dataMap = new HashMap<String, String>();
            for (String key : map.keySet()) {
                dataMap.put(key, JSON.toJSONString(map.get(key)));
            }
            this.setRolePermToRedis(dataMap);
        }
    }

    /**
     * 通过角色信息获取权限列表
     */
    @Override
    public List<SysRolePerm> getRolePermByIds(List<String> roleIds) {
        List<SysRolePerm> list = this.getRolePermByIdsFromRedis(roleIds);// 内存中读取
        if (list != null) {
            return list;
        } else {// 从数据库中读取
            return this.getRolePermByIds(roleIds, null);
        }
    }

    /**
     * 从reids中获取权限列表
     *
     * @param roleIds
     * @return
     */
    private List<SysRolePerm> getRolePermByIdsFromRedis(List<String> roleIds) {
        Collection<Object> hKeys = new ArrayList<Object>();
        hKeys.addAll(roleIds);
        List<String> list = redis.getMultiMapValue(RED_KEY, hKeys);
        if (StringUtils.isNotEmpty(list)) {
            List<SysRolePerm> rtnList = new ArrayList<SysRolePerm>();
            for (String json : list) {
                try {
                    if (json != null && !json.equalsIgnoreCase("null") && json.length() > 0) {
                        List<SysRolePerm> tempList = JSONArray.parseArray(json, SysRolePerm.class);
                        if (tempList != null) {
                            rtnList.addAll(tempList);
                        }
                    }
                } catch (Exception e) {
                    log.error("", e);
                }
            }
            if (StringUtils.isEmpty(rtnList)) {
                return null;
            } else {
                return rtnList;
            }
        }
        return null;
    }

    /**
     * 删除角色关系
     */
    @Override
    public boolean deleteByRoleId(SysRolePermDto paramDto) {
        return deleteByRoleId(null, paramDto);
    }

    /**
     * 删除角色关系
     */
    @Override
    public boolean deleteByRoleId(String tenant_id, SysRolePermDto paramDto) {
        String roleid = paramDto.getRoleid();// 角色ID
        Where where = Where.create();
        if (StringUtils.isNotEmpty(paramDto.getRoleid())) {
            where.eq(SysRolePerm::getRoleid, roleid);
        }
        if (StringUtils.isNotEmpty(paramDto.getPermid())) {
            where.eq(SysRolePerm::getPermid, paramDto.getPermid());
        }
        int rtn = 0;
        if (StringUtils.isNotEmpty(tenant_id)) {
            rtn = dao.deleteWithTenant(tenant_id, SysRolePerm.class, where);
        } else {
            rtn = dao.rawDeleteByWhere(SysRolePerm.class, where);
        }
        if (rtn > 0) {
            if (StringUtils.isNotEmpty(roleid)) {
                this.deleteRolePermFromRedis(roleid);
            } else {
                this.initRolePermData();
            }
            authServ.removeAllUserFromRedis();
        }
        return rtn == 0 ? false : true;
    }

    /**
     * 删除角色权限信息
     */
    @Override
    public boolean deleteByPermId(String permid) {
        return deleteByPermId(null, permid);
    }

    /**
     * 删除角色权限信息
     */
    @Override
    public boolean deleteByPermId(String tenant_id, String permid) {
        if (StringUtils.isEmpty(permid)) {
            return false;
        }
        try {
            SysRolePermDto dto = new SysRolePermDto();
            dto.setPermid(permid);
            return deleteByRoleId(dto);
        } catch (Exception e) {
            log.error("", e);
            return false;
        }
    }

}
