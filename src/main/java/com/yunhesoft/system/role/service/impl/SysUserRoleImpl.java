package com.yunhesoft.system.role.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.RedisUtil;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.auth.service.AuthService;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.role.entity.dto.UserRoleDto;
import com.yunhesoft.system.role.entity.po.SysUserRole;
import com.yunhesoft.system.role.entity.vo.RoleTree;
import com.yunhesoft.system.role.service.ISysRoleService;
import com.yunhesoft.system.role.service.ISysUserRoleService;

import lombok.extern.log4j.Log4j2;

/**
 * 人员角色表操作接口
 * 
 * @category 人员角色表操作接口
 * <AUTHOR>
 * @date 2021/04/23
 */
@Log4j2
@Service
public class SysUserRoleImpl implements ISysUserRoleService {

	@Autowired
	private EntityService dao;

	@Autowired
	private ISysRoleService roleServ;

	@Autowired
	private AuthService authServ;

	@Autowired
	private RedisUtil redis;

	private final static String RED_KEY = "SYSTEM:USERROLE_INFO";

	/**
	 * 批量添加人员岗位关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	@Override
	public boolean addBatch(List<SysUserRole> listPo) {
		boolean flag = false;
		if (listPo != null && listPo.size() > 0) {
			for (SysUserRole bean : listPo) {
				if (StringUtils.isEmpty(bean.getId())) {
					bean.setId(TMUID.getUID());
				}
				authServ.removeUserFromRedis(bean.getUserid());
				this.deleteUserRoleFromRedis(bean.getUserid());
			}
			int rtn = dao.insertBatch(listPo, 500);
			flag = rtn == 0 ? false : true;
		}
		return flag;
	}

	/**
	 * 批量添加人员岗位关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	@Override
	public boolean addBatchWithTenant(List<SysUserRole> listPo, String tenantId) {
		if (listPo != null && listPo.size() > 0) {
			for (SysUserRole bean : listPo) {
				if (StringUtils.isEmpty(bean.getId())) {
					bean.setId(TMUID.getUID());
				}
				authServ.removeUserFromRedis(bean.getUserid());
				this.deleteUserRoleFromRedis(bean.getUserid());
			}
			try {
				dao.rawInsertBatchWithTenant(tenantId, listPo);
			} catch (Exception ex) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 批量删除人员岗位关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	@Override
	public boolean deleteBatchById(List<SysUserRole> listPo) {
		boolean flag = false;
		if (listPo != null && listPo.size() > 0) {
			int rtn = dao.deleteByIdBatch(listPo); // flag = this.removeByIds(idList);
			flag = rtn == 0 ? false : true;
		}
		if (flag) {
			for (SysUserRole bean : listPo) {
				this.deleteUserRoleFromRedis(bean.getUserid());
				authServ.removeUserFromRedis(bean.getUserid());
			}
		}
		return flag;
	}

	/**
	 * 批量更新人员岗位关系信息
	 * 
	 * @param list
	 * @param listPo
	 * @return
	 */
	@Override
	public boolean updateBatchByIds(List<SysUserRole> listPo) {
		boolean flag = false;
		try {
			if (listPo != null && listPo.size() > 0) {
				int rtn = dao.updateByIdBatch(listPo);
				flag = rtn == 0 ? false : true;
				if (flag) {
					for (SysUserRole bean : listPo) {
						this.deleteUserRoleFromRedis(bean.getUserid());
						authServ.removeUserFromRedis(bean.getUserid());
					}
				}
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return flag;
	}

	/**
	 * 获取人员角色关系信息
	 * 
	 * @param param
	 * @return
	 */
	@Override
	public List<SysUserRole> getUserRole(UserRoleDto paramDto) {
		String userid = paramDto.getUserid(); // 用工ID
		String roleid = paramDto.getRoleid();// 角色ID
		List<SysUserRole> list = null;
		if (StringUtils.isNotEmpty(userid)) {
			list = getUserRoleFromRedis(userid);// 内存数据库获取
			if (list == null) {
				list = getUserRoleFromDB(userid, null);// 数据库中获取
				if (list != null) {
					setUserRoleToRedis(userid, list);
				}
			}
			if (StringUtils.isNotEmpty(list) && StringUtils.isNotEmpty(roleid)) {// 查找某一个角色id
				for (SysUserRole sysUserRole : list) {
					if (roleid.equals(sysUserRole.getRoleid())) {
						List<SysUserRole> rtnlist = new ArrayList<SysUserRole>();
						rtnlist.add(sysUserRole);
						return rtnlist;
					}
				}
				return null;
			}
			return list;
		}
		return this.getUserRoleFromDB(userid, roleid);
	}

	private void setUserRoleToRedis(String userid, List<SysUserRole> list) {
		if (list != null) {
			String json = JSON.toJSONString(list);
			redis.setMapValue(RED_KEY, userid, json);
		}
	}

	private void deleteUserRoleFromRedis(String userid) {
		redis.hDelete(RED_KEY, userid);
	}

	/**
	 * 从内存中获取用户角色信息
	 * 
	 * @param userid
	 * @return
	 */
	private List<SysUserRole> getUserRoleFromRedis(String userid) {
		List<SysUserRole> list = null;
		try {
			String json = redis.getMapValue(RED_KEY, userid);
			if (StringUtils.isNotEmpty(json)) {
				list = JSONArray.parseArray(json, SysUserRole.class);
			}
		} catch (Exception e) {
			log.error("", e);
		}
		return list;
	}

	private List<SysUserRole> getUserRoleFromDB(String userid, String roleid) {
		// List<SysUserRole> list = null;
		// String userid = paramDto.getUserid(); // 用工ID
		// String roleid = paramDto.getRoleid();// 角色ID
		Where where = Where.create();
		if (StringUtils.isNotEmpty(userid)) {
			where.eq(SysUserRole::getUserid, userid);
		}
		if (StringUtils.isNotEmpty(roleid)) {
			where.eq(SysUserRole::getRoleid, roleid);
		}
		return dao.rawQueryListByWhere(SysUserRole.class, where);
	}

	@Override
	public List<SysUserRole> getUserRole(List<String> userIds) {
		Where where = Where.create();
		where.in(SysUserRole::getUserid, userIds.toArray());
		return dao.rawQueryListByWhere(SysUserRole.class, where);
	}

	/**
	 * 删除人员角色关系表
	 */
	@Override
	public boolean deleteUserRoleByUserIds(List<String> userIds) {
		boolean bln = false;
		if (StringUtils.isNotEmpty(userIds)) {
			Where where = Where.create();
			where.in(SysUserRole::getUserid, userIds.toArray());
			int rtn = dao.rawDeleteByWhere(SysUserRole.class, where);
			bln = (rtn == 0 ? false : true);
			if (bln) {// 删除登陆缓存
				for (String empid : userIds) {
					authServ.removeUserFromRedis(empid);
					this.deleteUserRoleFromRedis(empid);
				}
			}
			return bln;
		} else {
			return false;
		}
	}

	/**
	 * 删除人员角色关系表
	 */
	@Override
	public boolean deleteUserRoleByUserId(UserRoleDto paramDto) {
		boolean bln = false;
		String empid = paramDto.getUserid(); // 用工ID
		String roleid = paramDto.getRoleid();// 角色ID
		Where where = Where.create();
		where.eq(SysUserRole::getUserid, empid);
		if (StringUtils.isNotEmpty(roleid)) {
			where.eq(SysUserRole::getRoleid, roleid);
		}
		int rtn = dao.rawDeleteByWhere(SysUserRole.class, where);
		bln = (rtn == 0 ? false : true);
		if (bln) {// 删除登陆缓存
			authServ.removeUserFromRedis(empid);
			this.deleteUserRoleFromRedis(empid);
		}
		return bln;
	}

	/**
	 * 
	 * @param type 1:超级管理员; -1:未注册用户
	 * @return
	 */
	@Override
	public List<SysUserRole> getDefaultUserRole(String userId, int type) {
		List<SysUserRole> list = new ArrayList<SysUserRole>();
		RoleTree roleTree = roleServ.getDefaultSysRole(-1); // 获取未设置角色用户的默认角色
		if (roleTree != null) {
			SysUserRole userRole = new SysUserRole();
			userRole.setId(TMUID.getUID());
			userRole.setRoleid(roleTree.getId());
			userRole.setUserid(userId);
			list.add(userRole);
		}
		return list;
	}

	/**
	 * 查询角色是否有人使用
	 * 
	 * @param roleId
	 * @return
	 */
	@Override
	public int getUserRoleCount(String roleId) {
		int count = 0;
		Where query = Where.create();
		query.eq(SysUserRole::getRoleid, roleId);
		count = dao.queryCount(SysUserRole.class, query).intValue();
		return count;
	}

	/**
	 * 根据角色列表获取人员id
	 * 
	 * @param roleidList 角色列表
	 * @return
	 */
	@Override
	public List<String> getUserIds(List<String> roleidList) {
		List<String> rtnList = new ArrayList<String>();
		if (StringUtils.isNotEmpty(roleidList)) {
			Where where = Where.create();
			if (roleidList.size() == 1) {
				where.eq(SysUserRole::getRoleid, roleidList.get(0));
			} else {
				where.in(SysUserRole::getRoleid, roleidList.toArray());
			}
			List<SysUserRole> list = dao.rawQueryListByWhere(SysUserRole.class, where);
			if (StringUtils.isNotEmpty(list)) {
				rtnList = list.stream().map(SysUserRole::getUserid).collect(Collectors.toList());
			}
		}
		return rtnList;
	}

	/**
	 * 初始化用户角色信息到redis
	 */
	@Override
	public void initUserRoleData() {
		List<SysUserRole> list = dao.rawQueryListByWhereDisableTenant(SysUserRole.class, null);
		if (list != null && list.size() > 0) {
			Map<String, List<SysUserRole>> map = new HashMap<String, List<SysUserRole>>();
			for (SysUserRole e : list) {
				String userid = e.getUserid();
				if (map.containsKey(userid)) {
					map.get(userid).add(e);
				} else {
					List<SysUserRole> templist = new ArrayList<SysUserRole>();
					templist.add(e);
					map.put(userid, templist);
				}
			}
			redis.delete(RED_KEY);
			for (String key : map.keySet()) {
				this.setUserRoleToRedis(key, map.get(key));
			}
		}

	}

}
