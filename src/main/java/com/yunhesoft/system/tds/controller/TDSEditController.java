package com.yunhesoft.system.tds.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.utils.excel.ExcelImport;
import com.yunhesoft.system.tds.entity.dto.TdsEditDto;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.model.TDataSourceManager;
import com.yunhesoft.system.tds.service.IDataSourceEditService;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.log4j.Log4j2;

/**
 * 数据源修改相关
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "数据源")
@RestController
@RequestMapping("/tdsEdit")
@Log4j2
public class TDSEditController extends BaseRestController {

	@Autowired
	private IDataSourceEditService tdsEditServ; // 数据源修改服务

	@ApiOperation(value = "保存数据源数据")
	@RequestMapping(value = "saveTdsData", method = { RequestMethod.POST })
	public Res<?> saveTdsData(@RequestBody TdsEditDto param) {
		return Res.OK(tdsEditServ.saveData(param));
	}

	/**
	 * Excel数据导入示例
	 * 
	 * @param file
	 * @return
	 * @throws Exception
	 */
	@ApiOperation(value = "数据导入")
	@RequestMapping(value = "importTdsData", method = { RequestMethod.POST })
	public Res<?> importExcel(@RequestParam("file") MultipartFile file, @RequestParam("params") String params) {
		String info = "";
		String improtCount = "0";
		Res<?> res = new Res<>();
		try {
			int titleRows = 0; // 标题行数
			int headerRows = 1;// tdsEditServ.getTdsHeaderRow(params); // 标题行数

			// 获取数据源中设置的表头行
			JSONObject tds = JSON.parseObject(params);
			String tdsAlias = tds.getString("name");// 数据源别名

			TDataSourceManager tsm = new TDataSourceManager();
			TdataSource tdataSource = StringUtils.isEmpty(tdsAlias) ? null : tsm.getTdataSource(tdsAlias);// tdsEditServ.getTds(tdsAlias);
// =========合并表头导入有bug， 先屏蔽此功能 by x.zhong 2023.7.11==================================
//			if (tdataSource != null && tdataSource.getExcelHeaderRows() != null
//					&& tdataSource.getExcelHeaderRows() > 0) {
//				headerRows = tdataSource.getExcelHeaderRows();
//			}
//============================================================================================
			// 指定导入sheet页名称，如果找不到该页则默认导入第一个sheet页
			String sheetName = tdataSource == null ? null : tdataSource.getExcelImportSheetName();
//			if (StringUtils.isEmpty(sheetName)) {
//				sheetName = "Sheet1";
//			}
			ExcelImportResult<?> result = ExcelImport.importExcel(file, titleRows, headerRows, false, sheetName);
			if (StringUtils.isNotEmpty(result.getFailList())) {
				info = "导入失败数量：" + result.getFailList().size();
			} else {
				if (StringUtils.isNotEmpty(result.getList())) {
					// improtCount = result.getList().size();
					Map<String, String> returnMap = tdsEditServ.importData(params, result.getList());
					info = returnMap.get("info");
					if (returnMap.get("count") != null) {
						improtCount = returnMap.get("count");
					}
				} else {
					info = "无导入数据";
				}
			}
		} catch (Exception e) {
			log.error("数据源导入", e);
			// return Res.FAIL(e.getMessage());
			res.setSuccess(false);
			res.setMessage(e.getMessage());
			return res;
		}
		if (StringUtils.isNotEmpty(info)) {
			// return Res.FAIL(info);
			res.setSuccess(false);
			res.setMessage(info);
			return res;
		} else {
			return Res.OK("成功导入" + improtCount + "条数据！");
		}
	}

	@ApiOperation(value = "查询数据源数据")
	@RequestMapping(value = "reExtractData", method = { RequestMethod.POST })
	public Res<?> reExtractData(@RequestBody TdsQueryDto param) {
		return Res.OK(tdsEditServ.reExtractData(param));
	}

	@ApiOperation(value = "生成一个新的TMUID")
	@RequestMapping(value = "generateTMUID", method = { RequestMethod.GET })
	public Res<?> generateTMUID() {
		return Res.OK(TMUID.getUID());
	}

}
