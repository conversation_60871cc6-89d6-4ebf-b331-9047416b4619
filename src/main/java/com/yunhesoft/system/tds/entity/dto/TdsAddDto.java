package com.yunhesoft.system.tds.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据源添加
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@ApiModel(description = "添加数据源")
public class TdsAddDto {

	@ApiModelProperty(value = "模块编码")
	private String moduleCode;

	@ApiModelProperty(value = "数据表")
	private String tableName;

	@ApiModelProperty(value = "数据源别名")
	private String tdsAlias;

	@ApiModelProperty(value = "输入参数")
	private String inPara;// 多个用逗号分隔

}
