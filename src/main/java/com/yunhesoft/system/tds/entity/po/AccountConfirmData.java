package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 台账确认信息
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "ACCOUNT_CONFIRM_DATA")
public class AccountConfirmData extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 应用时间 */
    @Column(name="APPLY_TIME")
    private Date applyTime;
    
    /** 应用时间字符串 */
    @Column(name="APPLY_TIME_STR", length=50)
    private String applyTimeStr;
    
    /** 应用日期 */
    @Column(name="APPLY_DAY", length=50)
    private String applyDay;
    
    /** 所属核算单元 */
    @Column(name="UNIT_CODE", length=50)
    private String unitCode;
    
    /** 表单标识 */
    @Column(name="FORM_ID", length=50)
    private String formId;
    
    /** 自定义表单标识 */
    @Column(name="CUSTOM_FORM_ID", length=50)
    private String customFormId;
    
    /** 确认时间 */
    @Column(name="CONFIRM_TIME")
    private Date confirmTime;
    
    /** 确认人 */
    @Column(name="CONFIRM_USER_NAME", length=50)
    private String confirmUserName;
    
    /** 确认范围 */
    @Column(name="CONFIRM_BOUND")
    private Integer confirmBound;
    
    /** 确认超期标识1超0未超 */
    @Column(name="CONFIRM_OVER")
    private Integer confirmOver;
    
    /** TMSORT */
    @Column(name="TMSORT")
    private Integer tmsort;
    
    /** TMUSED */
    @Column(name="TMUSED")
    private Integer tmused;
    

}

