package com.yunhesoft.system.tds.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据源信息表
 *
 * <AUTHOR>
 * @category 数据源
 */
@Getter
@Setter
@Entity
@Table(name = "TDS_DATASOURCE")
public class TdataSource extends BaseEntity {

    private static final long serialVersionUID = 1L;
    // 数据源别名
    @Column(name = "TDSALIAS", length = 255)
    private String tdsalias;
    // 模块编码
    @Column(name = "MODULECODE", length = 50)
    private String moduleCode;
    // 数据源名称
    @Column(name = "TDSNAME", length = 255)
    private String tdsname;
    // SYS:系统数据源；其他：自定义数据源
    @Column(name = "TDSTYPE", length = 50)
    private String tdstype;
    // 执行类名
    @Column(name = "TDSCLASSNAME", length = 100)
    private String tdsclassName;
    // 备注
    @Column(name = "MEMO", length = 255)
    private String memo;
    // 是否可编辑
    @Column(name = "ALLOWTOSAVE")
    private Integer allowToSave;
    // 查询语句
    @Column(name = "TDSQUERYSQL", length = 4000)
    private String tdsQuerySql;
    // 更新语句（未使用）
    @Column(name = "TDSUPDATESQL", length = 100)
    private String tdsUpdateSql;
    // 注册时间
    @Column(name = "REGTIME")
    private Date regtime;
    // 是否使用 1：使用；0：不使用
    @Column(name = "USED")
    private Integer used;
    // 自动加载
    @Column(name = "AUTOLOAD")
    private Integer autoLoad;
    // TM4未使用
    @Column(name = "IMPORTDATABASE")
    protected Integer importDataBase;
    // 脚本（TM4未使用）
    @Column(name = "SCRIPT", length = 255)
    private String script;
    // 系统库 or 分厂库 （tm4已废弃）
    @Column(name = "ISMAINDB")
    private Integer isMainDb;
    // 创建人id
    @Column(name = "CREATEUID", length = 50)
    private String createUid;
    // 创建人
    @Column(name = "CREATEUNAME", length = 50)
    private String createUname;
    // 更新人id
    @Column(name = "UPDATEUID", length = 50)
    private String updateUid;
    // 更新人
    @Column(name = "UPDATEUNAME", length = 50)
    private String updateUname;
    // 更新时间
    @Column(name = "UPDATEDATE")
    private Date updateDate;
    // 是否允许为空（TM4未使用）
    @Column(name = "ALLOWNULL")
    private Integer allowNull;
    // 分类ID
    @Column(name = "CATEGORYID", length = 50)
    private String categoryId;
    // 排序
    @Column(name = "TMSORT")
    private Integer tmSort;
    // 数据库连接id（TM3外部数据源使用）
    @Column(name = "DBCONNINFOID", length = 50)
    private String dbConnInfoId;
    // TM3Excel数据源使用
    @Column(name = "EXCELAUTOCREATE")
    private Integer excelAutoCreate;

    // 数据源修改更新表名
    @Column(name = "DBTABLENAME", length = 50)
    private String dbTableName;
    // TM3Excel数据源使用
    @Column(name = "EXCELMULTISHEET")
    private Integer excelMultiSheet;
    // 是否是系统内置数据源 1:内置（如：SysInfo）
    @Column(name = "ISSYS")
    private Integer isSys;
    // TM3使用
    @Column(name = "FINDBYKEY")
    private Integer findByKey;
    // 高级检索功能 ，tm4未启用
    @Column(name = "ADVANCEDSEARCH")
    private Integer advancedSearch;
    // 数据源修改默认初始化插入数据语句
    @Column(name = "TDSINITSQL", length = 2000)
    private String tdsInitSql;
    // 数据源页面计算脚本
    @Column(name = "TDSCALSCRIPT", length = 2000)
    private String tdsCalScript;
    // 数据源页面字段是否可以编辑脚本
    @Column(name = "TDSCANEDITSCRIPT", length = 500)
    private String tdsCanEditScript;

    // 子数据源名称
    @Column(name = "C_TDSNAME", length = 255)
    private String cTdsName;
    // 子数据源别名
    @Column(name = "C_TDSALIAS", length = 255)
    private String cTdsAlias;

    @Column(name = "EXCEL_HEADER_ROWS")
    private Integer excelHeaderRows; // 导入excel数据源表头行数

    @Column(name = "EXCEL_IMPORT_SHEETNAME", length = 255)
    private String excelImportSheetName; // 导入excel的sheet页名称

    // 数据初始化类型
    @Column(name = "TDSINITTYPE")
    private Integer tdsInitType;// 数据源编辑功能初始化类型（1：数据源，2或null：自定义初始化语句）

    // 数据源编辑功能初始化数据绑定的数据源别名
    @Column(name = "BINDTDSALIAS")
    private String bindTdsAlias;

    // 表格尺寸 medium / small / mini / super-mini
    @Column(name = "TABLESIZE")
    private String tableSize;

    // 数据源页面编辑后脚本
    @Column(name = "TDSAFTEREDITSCRIPT", length = 2000)
    private String tdsAfterEditScript;
    // 是否向外部开放数据接口 -1代表不启用 0代表启用
    @Column(name = "ISOUTTDS")
    private Integer isOutTds;

    @Column(name = "TDS_GROUP")
    private String tdsGroup;

    // 是否每次加载数据时都重现初始化数据
    @Column(name = "ISLOADINIT")
    private Integer isLoadInit;

    @Column(name = "AFTERSAVE", length = 1000)
    private String afterSave;


    // 使用逻辑删除
    @Column(name = "USE_LOGIC_DELETE")
    private Integer useLogicDelete;
    // 逻辑删除字段
    @Column(name = "LOGIC_DELETE_COLUMN", length = 50)
    private String logicDeleteColumn;

    //数据源导出Excel多sheet页时，按照哪个输入参数进行检索数据
    @Column(name = "MULTISHEET_INPARA", length = 255)
    private String multiSheetInPara;

}