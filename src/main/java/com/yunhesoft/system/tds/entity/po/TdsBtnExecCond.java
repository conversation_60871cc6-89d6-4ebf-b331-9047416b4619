package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据源操作  按钮执行条件
 * 
 * @category 数据源
 * <AUTHOR>
 *
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_BTN_EXECCOND")
public class TdsBtnExecCond extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 数据源别名 */
    @Column(name="TDSALIAS", length=100)
    private String tdsalias;
    
    /** 数据源列名 */
    @Column(name="PARAALIAS", length=100)
    private String paraalias;
    
    /** 按钮ID */
    @Column(name="BTNID", length=100)
    private String btnid;
    
    /** 参数名称 */
    @Column(name="NAME", length=100)
    private String name;
    
    /** 参数别名 */
    @Column(name="ALIAS", length=100)
    private String alias;
    
    /** 判断条件 */
    @Column(name="COND", length=50)
    private String cond;
    
    /** 条件值 */
    @Column(name="VALUE", length=100)
    private String value;
    
    /** 提示语 */
    @Column(name="MSG", length=1000)
    private String msg;
    
    /** 是否使用 */
    @Column(name="USED")
    private Integer used;
    
    /** 排序 */
    @Column(name="SORT")
    private Integer sort;

}