package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据源操作  按钮表操作执行条件语句
 * 
 * @category 数据源
 * <AUTHOR>
 *
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_BTN_TABEXEC_EXECCONDSQL")
public class TdsBtnTabExecExecCondSql extends BaseEntity {
	
    private static final long serialVersionUID = 1L;

    /** 数据源别名 */
    @Column(name="TDSALIAS", length=100)
    private String tdsalias;
    
    /** 数据源列名 */
    @Column(name="PARAALIAS", length=100)
    private String paraalias;
    
    /** 按钮ID */
    @Column(name="BTNID", length=100)
    private String btnid;
    
    /** 执行操作ID */
    @Column(name="EXECID", length=100)
    private String execid;
    
    /** 语句执行条件 */
    @Column(name="SQLWHERE", length=4000)
    private String sqlwhere;
    
}