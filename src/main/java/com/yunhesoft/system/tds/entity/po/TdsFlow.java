package com.yunhesoft.system.tds.entity.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;


/**
 * TDS_FLOW
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "TDS_FLOW")
public class TdsFlow extends BaseEntity {
	
    private static final long serialVersionUID = 1L;


    /** 数据源别名 */
    @Column(name="TDSALIAS", length=50)
    private String tdsalias;
    
    /** 是否启动工作流 */
    @Column(name="ISSTARTFLOW")
    private Integer isstartflow;
    
    /** 工作流程编码 */
    @Column(name="BUSITEMPLATEID", length=255)
    private String busiTemplateId;
    
    /** 是否显示批量发起按钮 */
    @Column(name="ISSHOWBATCHSTARTBTN")
    private Integer isshowbatchstartbtn;
    
    /** 是否显示输出列 */
    @Column(name="ISSHOWOUTCOLUMN")
    private Integer isshowoutcolumn;
    
    /** 允许发起流程提交脚本 */
    @Column(name="ALLOWCOMMITSCRIPT", length=4000)
    private String allowcommitscript;
    
    /** 业务主键 */
    @Column(name="BUSINESSKEY", length=50)
    private String businessKey;
    
    /** 自定义按钮别名 */
    @Column(name="STARTALIAS", length=30)
    private String startAlias;
    
	/** 流程启动后，自动跳转页面地址 */
    @Column(name="TOPATH", length=500)
    private String toPath;
    
    /** 根业务主键 */
    @Column(name="ROOTBUSINESSKEY", length=50)
    private String rootBusinessKey;

}
