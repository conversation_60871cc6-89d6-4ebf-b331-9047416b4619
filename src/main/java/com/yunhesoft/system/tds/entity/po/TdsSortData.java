package com.yunhesoft.system.tds.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据源排序
 */
@Getter
@Setter
@Entity
@Table(name = "TDS_SORTDATA")
public class TdsSortData extends BaseEntity {

	private static final long serialVersionUID = 1L;

	// 数据源别名
	@Column(name = "TDSALIAS", length = 100)
	private String tdsAlias;
	// 输出参数别名
	@Column(name = "PARAMALIAS", length = 50)
	private String paramAlias;

	// 排序方式 1：升序；0：降序
	@Column(name = "SORTTYPE")
	private Integer sortType;

	// 排序顺序
	@Column(name = "TMSORT")
	private Integer tmSort;

}