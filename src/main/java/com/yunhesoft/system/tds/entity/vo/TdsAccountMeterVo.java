package com.yunhesoft.system.tds.entity.vo;


import javax.persistence.Transient;

import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;

import lombok.Data;

/**
 * 核算对象
 * 
 */
@Data
public class TdsAccountMeterVo extends TdsAccountMeter {

	/** 核算单元名称 */
    private String unitName;
	
    private String unitcode;
    private String version;
    private String name;
    private String unitid;
    private String tagname;
    
	
    private Boolean showMark;//是否显示
    
    private String zone;//单元、区域
    private String dev;//设备
    
    
//    private Double uplimit;
//    private Double lowlimit;
}
