package com.yunhesoft.system.tds.entity.vo;


import javax.persistence.Transient;

import com.yunhesoft.system.tds.entity.po.TdsAccountMeter;
import com.yunhesoft.system.tds.entity.po.TdsAccountTag;

import lombok.Data;

/**
 * 核算对象
 * 
 */
@Data
public class TdsAccountTagVo extends TdsAccountTag {

	/** 核算单元名称 */
    private String unitName;
    private String tagType;
	
    private Boolean showMark;//是否显示
    
    private String zone;//单元、区域
    private String dev;//设备
    
    
    private Integer ISSHOWLEDGER;
    
    private String sj;//时间
    private String val;//仪表值
}
