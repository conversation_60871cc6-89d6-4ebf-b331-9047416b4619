package com.yunhesoft.system.tds.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@Getter
public enum EditComponentEnum {

    textinput("单行文本", "input", "textfield"),
    textarea("多行文本", "textarea", "textarea"),
    numberfield("数字输入框", "number", "numberfield"),
    datefield("日期选择框", "date", "datefield"),
    monthfield("月份选择框", "date", "monthfield"),
    datetimefield("日期时间选择器", "time", "datetimefield"),
    yearfield("年份选择框", "date", "yearfield"),
    combo("下拉框", "select", "combo"),
    editcombo("可编辑下拉框", "select", "editcombo");

    //组件名称
    private final String componentName;
    //简流组件别名
    private final String tmsf_value;
    //数据源组件别名
    private final String tds_value;

    public static final Map<String, String> TMSF_TDS_MAP = new HashMap<>();
    public static final Map<String, String> TDS_TMSF_MAP = new HashMap<>();
    static {
        EditComponentEnum[] values = EditComponentEnum.values();
        for (EditComponentEnum e : values) {
            TMSF_TDS_MAP.put(e.getTmsf_value(), e.getTds_value());
            TDS_TMSF_MAP.put(e.getTds_value(), e.getTmsf_value());
        }
    }
}
