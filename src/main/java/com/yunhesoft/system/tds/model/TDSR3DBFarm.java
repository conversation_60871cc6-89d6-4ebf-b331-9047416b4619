package com.yunhesoft.system.tds.model;

import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.core.utils.spring.SpringUtils;
import com.yunhesoft.rtdb.core.model.Tag;
import com.yunhesoft.rtdb.core.model.TagData;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.tds.service.IRtdbService;
import lombok.extern.log4j.Log4j2;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * 实时数据读取 for 智慧养鸡平台
 *
 * <AUTHOR>
 * @since 2025-04-01
 */
@Log4j2
public class TDSR3DBFarm extends ADataSource {

    private static final long serialVersionUID = 1L;

    //r3db实时数据库服务
    private IRtdbService rtdbSrv = SpringUtils.getBean(IRtdbService.class);

    private EntityService dao = SpringUtils.getBean(EntityService.class);

    private List<String> tags = new ArrayList<String>();//仪表位号
    private String startDt = "";//开始日期
    private String endDt = "";//截止日期
    private int interval = 60;//间隔时间，秒

    private String companyCode = "FYYS";//公司编码，输入参数传入
    private List<String> hourseist = new ArrayList<String>();//鸡舍编码列表，输入参数传入

    private String showtype = "chart";//数据显示格式 table|chart

    private String batchYf = "";//批次月份

    /**
     * 初始化数据源必需信息<br>
     *
     * @param initInfoObj
     * @category 初始化数据源必需信息
     */
    public void init(Object initInfoObj) {
        setDataSourceId(TMUID.getUID());
        setAutoLoad(false);

    }

    /**
     * 获得批次开始日期、截止日期
     *
     * @param farmno  鸡舍id
     * @param batchYf 批次月份
     * @return
     */
    public Map<String, String> getBatchDay(String farmno, String batchYf) {
        Map<String, String> map = new HashMap<String, String>();
        String sql = "SELECT ksrq,jzrq from farm_batch where yf = ? and FARMNO=? ";
        List<Object> params = new ArrayList<>();
        params.add(batchYf);
        params.add(farmno);
        SqlRowSet rs = dao.rawQuery(sql, params.toArray());
        if (rs != null && rs.next()) {
            Date ksrq = null;
            try {
                ksrq = rs.getDate("ksrq");
            } catch (Exception e) {
                Object d1 = rs.getObject("ksrq");
                if (d1 instanceof java.time.LocalDateTime) {
                    ZonedDateTime zonedDateTime = ((java.time.LocalDateTime) d1).atZone(ZoneId.systemDefault());
                    java.time.Instant instant = zonedDateTime.toInstant();
                    ksrq = Date.from(instant);
                }
            }
            Date jzrq = null;
            try {
                jzrq = rs.getDate("jzrq");
            } catch (Exception e) {
                Object d2 = rs.getObject("jzrq");
                if (d2 instanceof java.time.LocalDateTime) {
                    ZonedDateTime zonedDateTime = ((java.time.LocalDateTime) d2).atZone(ZoneId.systemDefault());
                    java.time.Instant instant = zonedDateTime.toInstant();
                    jzrq = Date.from(instant);
                }
            }
            if (jzrq == null) {
                jzrq = new Date();
            }
            map.put("ksrq", DateTimeUtils.formatDate(ksrq, "yyyy-MM-dd") + " 00:00:00");
            map.put("jzrq", DateTimeUtils.formatDate(jzrq, "yyyy-MM-dd") + " 23:59:59");
        }
        return map;
    }

    /**
     * 获取输入参数
     */
    private void initInParas() {
        //公司别名
        String v = this.getInParaValue("gsbm");
        if (v != null) {
            this.companyCode = v;
        }
        //开始日期
        this.startDt = this.getInParaValue("ksrq");
        //截止日期
        this.endDt = this.getInParaValue("jzrq");
        //鸡舍列表
        String house = this.getInParaValue("js");
        if (house != null) {
            String[] houses = house.split(",");
            for (String h : houses) {
                this.hourseist.add(h);
            }
        }
        //仪表位号
        String ybwh = this.getInParaValue("ybwh");
        if (ybwh != null) {
            String[] ybwhs = ybwh.split(",");
            for (String h : ybwhs) {
                this.tags.add(h);
            }
        }
        //数据显示格式
        String _showtype = this.getInParaValue("showtype");
        if (StringUtils.isNotEmpty(_showtype)) {
            this.showtype = _showtype;
        }
        //时间间隔
        String _interval = this.getInParaValue("interval");
        if (StringUtils.isNotEmpty(_interval)) {
            this.interval = Integer.parseInt(_interval);
        }
        //批次也发
        String _batchYf = this.getInParaValue("batchYf");
        if (StringUtils.isNotEmpty(_batchYf)) {
            this.batchYf = _batchYf;
        }
        if (StringUtils.isNotEmpty(this.batchYf) && this.hourseist.size() > 0) {
            Map<String, String> map = this.getBatchDay(this.hourseist.get(0), this.batchYf);
            if (map.size() > 0) {
                this.startDt = map.get("ksrq");
                this.endDt = map.get("jzrq");
            }
        }
    }

    private String getInParaValue(String alias) {
        TInPara inPara1 = this.getInParaByAlias(alias);
        if (inPara1 != null) {
            Object val = inPara1.getValue();
            if (val != null) {
                return val.toString();
            }
        }
        return null;
    }

    public void load() {
        this.load(1, 0);
    }

    public void load(int page, int pageSize) {
//        hourseist.add("1");
//        tags.add("p_a_h");
//        tags.add("p_a_l");
//        startDt = "2025-03-31 23:00:00";
//        endDt = "2025-03-31 23:59:59";
        this.initInParas();
        this.initOutParams();
        List<String> tagList = this.getR3DbTags();
        if (StringUtils.isNotEmpty(tagList)) {
            if (StringUtils.isEmpty(startDt)) {
                startDt = DateTimeUtils.getNowDateTimeStr();
            }
            if (StringUtils.isEmpty(endDt)) {
                endDt = DateTimeUtils.getNowDateTimeStr();
            }
            try {
                List<Tag> rdbtaglist = rtdbSrv.queryRtdbTagData(tagList, startDt, endDt, interval);
                List<String> listDt = new ArrayList<>();
                if (this.interval == 0 && "chart".equals(this.showtype)) {
                    if (StringUtils.isNotEmpty(rdbtaglist)) {
                        for (Tag tag : rdbtaglist) {
                            List<TagData> datas = tag.getDatas();
                            for (TagData data : datas) {
                                String dt = data.getDatetime().substring(0, 19);//时间
                                if (!listDt.contains(dt)) {
                                    listDt.add(dt);
                                }
                            }
                        }
                    }

                }
                if (StringUtils.isNotEmpty(rdbtaglist)) {
                    int rowid = -1;
                    Map<String, Integer> rowMap = new HashMap<String, Integer>();
                    if (StringUtils.isNotEmpty(listDt)) {
                        Collections.sort(listDt);
                        for (String dt : listDt) {
                            rowid = tds.addRow();
                            rowMap.put(dt, rowid);
                        }
                    } else {
                        if ("chart2".equals(this.showtype)) {
                            if (this.startDt.equals(this.endDt)) {
                                for (String _hId : this.hourseist) {
                                    rowid = tds.addRow();
                                    String ky = _hId;
                                    rowMap.put(ky, rowid);
                                }
                            }
                        }
                    }
                    //Map<String, String> tagColNameMap = new HashMap<String, String>();
                    for (Tag tag : rdbtaglist) {
                        //String tagName = tag.getTagName();
                        //String tagCode = tag.getTagCode();
                        List<TagData> datas = tag.getDatas();
                        if (StringUtils.isNotEmpty(datas)) {
                            Map<String, String> valMap = this.getOtherVal(tag);
                            if ("table".equals(this.showtype)) {
                                for (TagData data : datas) {
                                    rowid = tds.addRow();
                                    for (int n = 0; n < tOutParas.size(); n++) {
                                        String col = tOutParas.get(n).getAlias();
                                        Object value = null;
                                        if ("dt".equals(col)) {//时间
                                            value = data.getDatetime().substring(0, 19);
                                        } else if ("value".equals(col)) {//值
                                            value = data.getValue();
                                        } else {//其他属性
                                            value = valMap.get(col);
                                        }
                                        tds.set(rowid, n, value);
                                    }
                                }
                            } else if ("chart".equals(this.showtype) || "chart2".equals(this.showtype)) {
                                String hourseId = valMap.get("hourseId");//鸡舍id
                                String hourseName = valMap.get("hourseName");//鸡舍名称
                                String tagName = valMap.get("tagName");//仪表名称
                                String tagCode = valMap.get("tagCode");//仪表编码
                                String alias = this.getOutAlias(hourseId, tagCode);
                                if ("chart2".equals(this.showtype)) {
                                    alias = this.getOutAlias("", tagCode);
                                }

                                //tagColNameMap.put(tagCode,tagName);
                                for (int n = 0; n < tOutParas.size(); n++) {
                                    String col = tOutParas.get(n).getAlias();
                                    if (alias.equals(col)) {
                                        String s = "";
                                        if ("chart".equals(this.showtype)) {
                                            if (this.hourseist.size() > 1) {
                                                s = hourseName + "." + tagName;
                                            } else {
                                                s = tagName;
                                            }
                                        } else if ("chart2".equals(this.showtype)) {
                                            s = tagName;
                                        }
                                        tOutParas.get(n).setName(s);
                                        break;
                                    }
                                }


                                for (TagData data : datas) {
                                    String dt = data.getDatetime().substring(0, 19);//时间
                                    String ky = dt;//hourseId + ":" + dt;
                                    if ("chart2".equals(this.showtype)) {
                                        if (this.startDt.equals(this.endDt)) {
                                            ky = hourseId;
                                        } else {
                                            ky = hourseId + ":" + dt;
                                        }
                                    }
                                    boolean isinsert = false;
                                    if (rowMap.containsKey(ky)) {
                                        rowid = rowMap.get(ky).intValue();
                                    } else {
                                        rowid = tds.addRow();
                                        rowMap.put(ky, rowid);
                                        isinsert = true;
                                    }
                                    for (int n = 0; n < tOutParas.size(); n++) {
                                        String col = tOutParas.get(n).getAlias();
                                        Object value = null;
                                        //if (isinsert) {
                                        if ("dt".equals(col)) {//时间
                                            tds.set(rowid, n, dt);
                                        } else if ("hourseName".equals(col)) {
                                            tds.set(rowid, n, hourseName);
//                                            } else if ("hourseId".equals(col)) {
//                                                tds.set(rowid, n, hourseId);
                                        } else if (alias.equals(col)) {//仪表值
                                            tds.set(rowid, n, data.getValue());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (listDt.size() > 0 && tds.getRowCount() > 0) {
                        for (int i = 0; i < tds.getRowCount(); i++) {
                            TRow tr = tds.getRow(i);
                            if (i > 0) {
                                for (int n = 0; n < tOutParas.size(); n++) {
                                    String col = tOutParas.get(n).getAlias();
                                    if ("dt".equals(col)) {//时间
                                    } else {
                                        Object value = tr.get(n);
                                        if (value == null || "".equals(value.toString())) {
                                            Object val = tds.getRow(i - 1).get(n);
                                            tds.set(i, n, val);  //补上一个点
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // rdbtaglist = null;
                log.error("获取实时仪表数据失败", e);
            }
        }
    }

    /**
     * 更新数据<br>
     * 该类不需要更新数据
     *
     * @category 更新数据
     */
    public Boolean update() {
        return false;
    }

    /**
     * 根据公司code和鸡舍编码、仪表位号生成实时仪表位号
     *
     * @return
     */
    private List<String> getR3DbTags() {
        List<String> tagList = new ArrayList<>();
        if (StringUtils.isNotEmpty(hourseist) && StringUtils.isNotEmpty(tags) && StringUtils.isNotEmpty(companyCode)) {
            for (String hcode : hourseist) {
                for (String tagcode : tags) {
                    String tag = companyCode + ".N" + hcode + "." + tagcode;
                    tagList.add(tag);
                }
            }
        }
        return tagList;
    }

    /**
     * 获取其他附件信息
     *
     * @param tag
     * @return
     */
    private Map<String, String> getOtherVal(Tag tag) {
        Map<String, String> map = new HashMap<String, String>();
        try {
            String tagName = tag.getTagName();
            String tagCode = tag.getTagCode();
            String[] ary = tagName.split("\\.");//1号鸡舍.低压报警压力
            if (ary.length > 1) {
                map.put("hourseName", ary[0]);//鸡舍名称
                map.put("tagName", ary[1]);//仪表名称
            } else {
                map.put("hourseName", "");//鸡舍名称
                map.put("tagName", tagName);//仪表名称
            }
            String[] ary1 = tagCode.split("\\.");//FYYS.N1.p_a_l
            if (ary1.length > 2) {
                map.put("hourseId", ary1[1].substring(1));//鸡舍ID
                map.put("tagCode", ary1[2].toLowerCase());//仪表位号
            } else {
                map.put("hourseId", "");//鸡舍ID
                map.put("tagCode", tagCode);//仪表位号
            }
            map.put("r3dbTagCode", tagCode);//实时仪表位号
            map.put("r3dbTagName", tagName);//实时仪表名称
        } catch (Exception e) {
            log.error("", e);
        }
        return map;
    }

    /**
     * 初始化输出参数
     */
    private void initOutParams() {
        if (StringUtils.isEmpty(tOutParas)) {
            tOutParas = new ArrayList<TOutPara>();
            int rowId = 0;
            this.addOutPara(this.getOut("dt", "时间", rowId, 180, "center", IDataSource.DataType.tdsString));
            if ("table".equals(this.showtype)) {
                rowId++;
                this.addOutPara(this.getOut("hourseName", "鸡舍名称", rowId, 100, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("hourseId", "鸡舍Id", rowId, 100, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("tagName", "仪表名称", rowId, 100, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("tagCode", "仪表位号", rowId, 100, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("r3dbTagCode", "实时仪表位号", rowId, 160, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("r3dbTagName", "实时仪表名称", rowId, 160, "left", IDataSource.DataType.tdsString));
                rowId++;
                this.addOutPara(this.getOut("value", "值", rowId, 160, "right", DataType.tdsDouble));
            } else if ("chart".equals(this.showtype)) {
                for (String hourseid : hourseist) {
                    for (String tagCode : tags) {
                        rowId++;
                        String alias = this.getOutAlias(hourseid, tagCode);
                        this.addOutPara(this.getOut(alias, alias, rowId, 160, "right", DataType.tdsDouble));
                    }
                }
            } else if ("chart2".equals(this.showtype)) {
                rowId++;
                this.addOutPara(this.getOut("hourseName", "鸡舍名称", rowId, 100, "left", IDataSource.DataType.tdsString));
                for (String tagCode : tags) {
                    rowId++;
                    String alias = this.getOutAlias("", tagCode);
                    this.addOutPara(this.getOut(alias, alias, rowId, 160, "right", DataType.tdsDouble));
                }
            }
        }
    }

    private String getOutAlias(String hourseid, String tagCode) {
        String alias = "N" + hourseid + "_T_" + tagCode;
        return alias;
    }

    private TOutPara getOut(String alias, String name, int id, int width, String align, IDataSource.DataType dataType) {
        TOutPara top1 = new TOutPara(this);
        top1.setAlias(alias);
        top1.setName(name);
        top1.setID(id);
        top1.setWidth(width);
        top1.setVisible(true);
        top1.setAlign(align);
        top1.setDataType(dataType);
        return top1;
    }

}
