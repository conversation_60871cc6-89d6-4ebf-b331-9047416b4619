package com.yunhesoft.system.tds.model;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import lombok.extern.log4j.Log4j2;

/**
 * 数据源输入参数类 <br>
 * 数据源必需输入的参数的属性<br>
 * 其中的值属性为外部 输入的参数值<br>
 * 供加载数据之用
 * 
 */
@Log4j2
public class TInPara {

	/** 序列号 */
	private int paraID;

	public IDataSource getDs() {
		return ds;
	}

	public void setDs(IDataSource ds) {
		this.ds = ds;
	}

	/** 参数名称 */
	private String paraName;
	/** 参数别名 */
	private String paraAlias;
	/** 数据类型 */
	private IDataSource.DataType dataType;
	/** 该输入框在界面上是否显示 */
	private Boolean displayed;
	/** 下拉框是否支持模糊检索 */
	private Boolean canquery;

	/** 参数值 */
	private Object value;
	/** 下拉框的显示值 */
	private Object rawValue;
	/**
	 * 下拉框组件填充用数据 <br>
	 * key内部编码 value为显示内容<br>
	 * 类型:LinkedMap
	 */
	private Map<Object, Object> mapDefaultValue;
	/** 下拉组件的内部编码,只适用于自动加载类型的数据源,例:ds.getColValues("组员ID") */
	private String defaultKeyScript;
	/** 下拉组件的显示内容,只适用于自动加载类型的数据源,例:ds.getColValues("组员姓名") */
	private String defaultValueScript;
	/**
	 * 显示组件类型<br>
	 * InputBox输入框、DropDownList下拉框<br>
	 * DateChooser日期选择框、DateTimeChooser日期时间选择框
	 */
	private String componentType;

	// 输入是否代入录入项 for 数据源编辑
	private Boolean insertEdit;

	/**
	 * 组件属性
	 */
	private String comParams;

	public Boolean getInsertEdit() {
		return insertEdit;
	}

	public void setInsertEdit(Boolean insertEdit) {
		this.insertEdit = insertEdit;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}

	public String getInitValueScript() {
		return initValueScript;
	}

	public void setInitValueScript(String initValueScript) {
		this.initValueScript = initValueScript;
	}

	private Integer width;

	// 初始化默认值
	private String initValueScript;

	/** 父对象内部调用 */
	private IDataSource ds;
	// 数据库更新操作标识
	private int rowflag;

	/***************************************************************************
	 * 属性操作
	 **************************************************************************/
	public TInPara(IDataSource ds) {
		this.ds = ds;
		mapDefaultValue = new LinkedHashMap<Object, Object>();
	}

	public String name() {
		return paraName;
	}

	public String alias() {
		return paraAlias;
	}

	public int getParaID() {
		return paraID;
	}

	public void setParaID(int paraID) {
		this.paraID = paraID;
	}

	public String getParaName() {
		return paraName;
	}

	public void setParaName(String paraName) {
		this.paraName = paraName;
	}

	public String getParaAlias() {
		return paraAlias;
	}

	public void setParaAlias(String paraAlias) {
		this.paraAlias = paraAlias;
	}

	public IDataSource.DataType getDataType() {
		return dataType;
	}

	public void setDataType(IDataSource.DataType dataType) {
		this.dataType = dataType;
	}

	public Map<Object, Object> getDefaultValueMap() {
		return mapDefaultValue;
	}

	public void setDefaultValueMap(Map<Object, Object> defaultValueMap) {
		this.mapDefaultValue = defaultValueMap;
	}

	public String getComponentType() {
		return componentType;
	}

	public void setComponentType(String componentType) {
		this.componentType = componentType;
	}

	public Boolean getDisplayed() {
		return displayed;
	}

	public void setDisplayed(Boolean displayed) {
		this.displayed = displayed;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		// // 分析输入默认脚本
		// if (value != null && value.toString().indexOf("@") >= 0
		// && value.toString().indexOf("=") >= 0) {
		// defaultKeyScript = parseDefaultValue(defaultKeyScript,
		// value.toString());
		// defaultValueScript = parseDefaultValue(defaultValueScript,
		// value.toString());
		// }
		this.value = value;
	}

	public String getDefaultValueScript() {
		return defaultValueScript;
	}

	public void setDefaultValueScript(String defaultValueScript) {
		this.defaultValueScript = defaultValueScript;
	}

	public void setDefaultValue(Map<Object, Object> mapDefaultValue) {
		this.mapDefaultValue = mapDefaultValue;
	}

	public String getDefaultKeyScript() {
		return defaultKeyScript;
	}

	public void setDefaultKeyScript(String defaultKeyScript) {
		this.defaultKeyScript = defaultKeyScript;
	}

	/***************************************************************************
	 * 函数操作
	 **************************************************************************/
	/**
	 * 返回调用函数字符串
	 * 
	 * @category 返回调用函数字符串
	 * @return 返回调用函数字符串
	 */
	public String getIF() {
		String str = "$" + ds.getDSAlias() + ".";
		if (dataType == IDataSource.DataType.tdsDouble || dataType == IDataSource.DataType.tdsLong) {
			str += "getinf(\"" + paraName + "\")";
		} else {
			str += "getins(\"" + paraName + "\")";
		}
		return str;
	}

	public Date toDate() {
		if (value instanceof Date) {
			return (Date) value;
		} else if (value instanceof String) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
			try {
				return sdf.parse(value.toString());
			} catch (ParseException e) {
				log.error("", e);
				return null;
			}
		} else if (value instanceof Long) {
			return new Date(Long.valueOf(value.toString()));
		} else {
			return null;
		}
	}

	public int getRowflag() {
		return rowflag;
	}

	public void setRowflag(int rowflag) {
		this.rowflag = rowflag;
	}

	/**
	 * 对数据源中的输入参数进行分析
	 * 
	 * @category 对数据源中的输入参数进行分析
	 * 
	 *           <pre>
	 * 分析规则如下
	 * 针对其中包含变量，@var代表一个变量
	 * 如果不包含变量，则直接赋值
	 * value=if(13=@var){@var;}else{@var1;};
	 * newValue=@var=12,@var1=23
	 * 上例中，则以12代替@var,23代替@var1后
	 * 然后交由数据源进行解析计算，并得出结果
	 *           </pre>
	 * 
	 * @param value
	 * @param newValue
	 * @return
	 */
	/*
	 * private String parseDefaultValue(String value, String newValue) { if
	 * (newValue.indexOf("@") >= 0 && newValue.indexOf("=") >= 0) { String[] vars =
	 * newValue.split(","); for (int i = 0; i < vars.length; i++) { int p =
	 * vars[i].indexOf("="); value = value.toString().replace(vars[i].substring(0,
	 * p), vars[i].substring(p + 1, vars[i].length())); } } else { value = newValue;
	 * } return value; }
	 */

	public Object getRawValue() {
		return rawValue;
	}

	public void setRawValue(Object rawValue) {
		this.rawValue = rawValue;
	}

	public Boolean getCanquery() {
		return canquery;
	}

	public void setCanquery(Boolean canquery) {
		this.canquery = canquery;
	}

	public String getComParams() {
		return comParams;
	}

	public void setComParams(String comParams) {
		this.comParams = comParams;
	}

}
