package com.yunhesoft.system.tds.model;

import java.util.Iterator;
import java.util.List;

/**
 * 数据源模块对象<br>
 * 数据源中提供的模块对象<br>
 * 其包含数据源列表
 */
public class TModule {
	private String ModuleName;
	private String ModuleCode;
	private String ModuleType;
	private List<TModule> tModuleList;
	private List<IDataSource> tDataSourceList;

	/**
	 * 增加数据源
	 * 
	 * @category 增加数据源
	 * @param ds 数据源对象
	 */
	public void addDataSource(IDataSource ds) {
		tDataSourceList.add(ds);
	}

	/**
	 * 查找指定别名的数据源对象
	 * 
	 * @category 查找给定别名的数据源
	 * @param dsAlias
	 * @return 返回数据源对象
	 */
	public IDataSource getDataSource(String dsAlias) {
		Iterator<IDataSource> it = tDataSourceList.iterator();
		IDataSource ds = null;
		// 在本级模块下查找数据源
		while (it.hasNext()) {
			IDataSource tds = it.next();
			if (tds.getDataSourceAlias().equals(dsAlias)) {
				ds = tds;
				break;
			}
		}
		if (ds == null) {
			// 在下级模块下查找数据源
			Iterator<TModule> itm = tModuleList.iterator();
			while (itm.hasNext()) {
				ds = itm.next().getDataSource(dsAlias);
			}
		}
		return ds;
	}

	/**
	 * 返回数据源对象列表
	 * 
	 * @category 返回本级模块下数据源
	 * @return 数据源对象列表
	 */
	public List<IDataSource> getDataSourceList() {
		return tDataSourceList;
	}

	/**
	 * 读取下级模块列表
	 * 
	 * @category 读取下级模块
	 * @return 下级模块列表
	 */
	public List<TModule> getModuleList() {
		return tModuleList;
	}

	/**
	 * 在本级模块增加下一级模块
	 * 
	 * @category 在本级模块增加下一级模块
	 * @param tmodel 模块名称
	 */
	public void addModule(TModule tmodule) {
		tModuleList.add(tmodule);
	}

	/**
	 * 读取模块名称
	 * 
	 * @category 读取模块名称
	 * @return 模块名称
	 */
	public String getModuleName() {
		return ModuleName;
	}

	/**
	 * 设置模块名称
	 * 
	 * @category 设置模块名称
	 * @param modelName 模块名称
	 */
	public void setModuleName(String moduleName) {
		ModuleName = moduleName;
	}

	/**
	 * 读取模块代码
	 * 
	 * @category 读取模块代码
	 * @return 模块代码
	 */
	public String getModuleCode() {
		return ModuleCode;
	}

	/**
	 * 设置模块代码
	 * 
	 * @category 设置模块代码
	 * @param moduleCode 模块代码
	 */
	public void setModuleCode(String moduleCode) {
		ModuleCode = moduleCode;
	}

	/**
	 * 读取模块类型
	 * 
	 * @category 读取模块类型
	 * @return 模块类型
	 */
	public String getModuleType() {
		return ModuleType;
	}

	/**
	 * 设置模块类型
	 * 
	 * @category 设置模块类型
	 * @param moduleType 模块类型
	 */
	public void setModuleType(String moduleType) {
		ModuleType = moduleType;
	}

	/**
	 * 判断数据源是否存在
	 * 
	 * @category 判断数据源是否存在
	 * @param alias 数据源别名
	 * @return 存在与不存在
	 */
	public Boolean existsDataSource(String alias) {
		// TODO
		return false;
	}
}
