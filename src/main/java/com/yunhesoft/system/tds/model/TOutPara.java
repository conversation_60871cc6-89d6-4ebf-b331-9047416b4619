package com.yunhesoft.system.tds.model;

import java.util.Map;

/**
 * 数据源输出参数 数据源中可供输出的参数的属性
 *
 * @category 数据源输出参数
 *
 */
public class TOutPara {

	private int colID;
	private String colName;
	private String colAlias;
	private IDataSource.DataType dataType;
	private String colMemo;
	private Boolean isKey = false;
	private Boolean isSum = false;
	private Boolean isGroup = false;
	private Boolean isSpan = false;

	private Object value;
	private String callFun;
	private IDataSource ds;
	private Integer width;
	private String align;
	private boolean visible;
	private String rendererFun;// 显示函数
	private String defaultKeyScript;// 显示内容
	private String defaultValueScript;// 显示值
	private String comType;// 控件类型
	private int rowflag;// 数据库更新操作标识
	private String reportFormula;// 报表统计公式
	private String lx;
	/** 当内容过长被隐藏时显示 tooltip */
	private boolean overtip;
	/** 列是否固定在左侧或者右侧，true 表示固定在左侧 string, boolean true, left, right */
	private String fixed;
	/** 列宽是否自动扩展 */
	private boolean autowidth;
	private boolean autoSwapRow;

	/** 录入时限制最大长度 */
	private Long maxlength;

	/** 合并模式 */
	private Integer spanType; // 0:相同合并；1：按照条件合并

	/** 合并脚本 */
	private String spanScript;

	private boolean required; // 是否为必填项

	private boolean showPlaceholder = false; // 是否显示站位提示及字符限制

	/**
	 * 组件属性
	 */
	private String comParams;

	/**
	 * 下拉框组件填充用数据 <br>
	 * key内部编码 value为显示内容<br>
	 * 类型:LinkedMap
	 */
	private Map<Object, Object> mapDefaultValue;

	private Integer dataStatusFlag; // 0或null:不是数据状态 列；1：数据状态列

	// 导出excel 是否解析渲染函数
	private Integer isExportRender;

	// 显示时是否按照数值显示（去掉无用的小数，0 不显示）
	private Integer isShowAsNum;

	// 添加数据时的默认值，数据源修改功能使用
	private String insertDefaultValue;

	private Boolean disabled;

	private String copyAddDefaultMode; // 复制新增默认值模式

	private String displayMode;

	public TOutPara(IDataSource ds) {
		this.ds = ds;
	}

	/***************************************************************************
	 * 属性操作
	 **************************************************************************/
	/**
	 * 输出参数名称
	 *
	 * @category 输出参数名称
	 * @return 名称
	 */

	public String getDisplayMode() {
		return displayMode;
	}

	public void setDisplayMode(String displayMode) {
		this.displayMode = displayMode;
	}

	public Boolean getDisabled() {
		return disabled;
	}

	public void setDisabled(Boolean disabled) {
		this.disabled = disabled;
	}

	public String getCopyAddDefaultMode() {
		return copyAddDefaultMode;
	}

	public void setCopyAddDefaultMode(String copyAddDefaultMode) {
		this.copyAddDefaultMode = copyAddDefaultMode;
	}

	public Boolean getIsSum() {
		return isSum;
	}

	public void setIsSum(Boolean isSum) {
		this.isSum = isSum;
	}

	public Boolean getIsSpan() {
		return isSpan;
	}

	public void setIsSpan(Boolean isSpan) {
		this.isSpan = isSpan;
	}

	public Boolean getIsGroup() {
		return isGroup;
	}

	public void setIsGroup(Boolean isGroup) {
		this.isGroup = isGroup;
	}

	public int getRowflag() {
		return rowflag;
	}

	public void setRowflag(int rowflag) {
		this.rowflag = rowflag;
	}

	public String name() {
		return colName;
	}

	public IDataSource getDs() {
		return ds;
	}

	public void setDs(IDataSource ds) {
		this.ds = ds;
	}

	/**
	 * 返回输出参数别名
	 *
	 * @category 返回输出参数别名
	 * @return 输出参数别名
	 */
	public String alias() {
		return colAlias;
	}

	/**
	 * 返回输出参数id
	 *
	 * @category 返回输出参数id
	 * @return 输出参数id
	 */
	public int colid() {
		return colID;
	}

	public int getID() {
		return colID;
	}

	public void setID(int colID) {
		this.colID = colID;
	}

	public String getName() {
		return colName;
	}

	public void setName(String colName) {
		this.colName = colName;
	}

	public String getAlias() {
		return colAlias;
	}

	public void setAlias(String colAlias) {
		this.colAlias = colAlias;
	}

	public IDataSource.DataType getDataType() {
		return dataType;
	}

	public void setDataType(IDataSource.DataType dataType) {
		this.dataType = dataType;
	}

	public String getMemo() {
		return colMemo;
	}

	public void setMemo(String colMemo) {
		this.colMemo = colMemo;
	}

	public Boolean getIsKey() {
		return isKey;
	}

	public void setIsKey(Boolean isKey) {
		this.isKey = isKey;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public String getCallFun() {
		return callFun;
	}

	public void setCallFun(String callFun) {
		this.callFun = callFun;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}

	public String getAlign() {
		return align;
	}

	public void setAlign(String align) {
		this.align = align;
	}

	public void setVisible(boolean visible) {
		this.visible = visible;
	}

	public boolean getVisible() {
		return visible;
	}

	/***************************************************************************
	 * 函数操作
	 **************************************************************************/

	/**
	 * 返回调用的函数串
	 *
	 * @category 返回调用的函数串
	 * @return 调用函数字符串
	 */
	public String getIF() {
		String str = "$" + ds.getDSAlias() + ".get()";
		// str += getIFN();
		return str;
	}

	public String getIFN() {
		String str = "";
		if (dataType == IDataSource.DataType.tdsDouble || dataType == IDataSource.DataType.tdsLong
				|| dataType == IDataSource.DataType.tdsInteger) {
			str = "getf(\"" + colName + "\")";
		} else {
			str = "gets(\"" + colName + "\")";
		}
		return str;
	}

	public String getRendererFun() {
		return rendererFun;
	}

	public void setRendererFun(String rendererFun) {
		this.rendererFun = rendererFun;
	}

	public String getDefaultKeyScript() {
		return defaultKeyScript;
	}

	public void setDefaultKeyScript(String defaultKeyScript) {
		this.defaultKeyScript = defaultKeyScript;
	}

	public String getDefaultValueScript() {
		return defaultValueScript;
	}

	public void setDefaultValueScript(String defaultValueScript) {
		this.defaultValueScript = defaultValueScript;
	}

	public String getComType() {
		return comType;
	}

	public void setComType(String comType) {
		this.comType = comType;
	}

	public String getReportFormula() {
		return reportFormula;
	}

	public void setReportFormula(String reportFormula) {
		this.reportFormula = reportFormula;
	}

	public String getLx() {
		return lx;
	}

	public void setLx(String lx) {
		this.lx = lx;
	}

	public Map<Object, Object> getDefaultValueMap() {
		return mapDefaultValue;
	}

	public void setDefaultValueMap(Map<Object, Object> mapDefaultValue) {
		this.mapDefaultValue = mapDefaultValue;
	}

	public boolean isOvertip() {
		return overtip;
	}

	public void setOvertip(boolean overtip) {
		this.overtip = overtip;
	}

	public String getFixed() {
		return fixed;
	}

	public void setFixed(String fixed) {
		this.fixed = fixed;
	}

	public boolean isAutowidth() {
		return autowidth;
	}

	public boolean isAutoSwapRow() {
		return autoSwapRow;
	}

	public void setAutoSwapRow(boolean autoSwapRow) {
		this.autoSwapRow = autoSwapRow;
	}

	public void setAutowidth(boolean autowidth) {
		this.autowidth = autowidth;
	}

	public Long getMaxlength() {
		return maxlength;
	}

	public void setMaxlength(Long maxlength) {
		this.maxlength = maxlength;
	}

	public Integer getSpanType() {
		return spanType;
	}

	public void setSpanType(Integer spanType) {
		this.spanType = spanType;
	}

	public String getSpanScript() {
		return spanScript;
	}

	public void setSpanScript(String spanScript) {
		this.spanScript = spanScript;
	}

	public boolean isRequired() {
		return required;
	}

	public void setRequired(boolean required) {
		this.required = required;
	}

	public boolean isShowPlaceholder() {
		return showPlaceholder;
	}

	public void setShowPlaceholder(boolean showPlaceholder) {
		this.showPlaceholder = showPlaceholder;
	}

	public String getComParams() {
		return comParams;
	}

	public void setComParams(String comParams) {
		this.comParams = comParams;
	}

	public Integer getDataStatusFlag() {
		return dataStatusFlag;
	}

	public void setDataStatusFlag(Integer dataStatusFlag) {
		this.dataStatusFlag = dataStatusFlag;
	}

	public Integer getIsExportRender() {
		return isExportRender;
	}

	public void setIsExportRender(Integer isExportRender) {
		this.isExportRender = isExportRender;
	}

	public Integer getIsShowAsNum() {
		return isShowAsNum;
	}

	public void setIsShowAsNum(Integer isShowAsNum) {
		this.isShowAsNum = isShowAsNum;
	}

	public String getInsertDefaultValue() {
		return insertDefaultValue;
	}

	public void setInsertDefaultValue(String insertDefaultValue) {
		this.insertDefaultValue = insertDefaultValue;
	}

}
