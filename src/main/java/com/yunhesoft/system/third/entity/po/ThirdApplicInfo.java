package com.yunhesoft.system.third.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @category 企业注册tm4通行token管理
 *
 *
 * <AUTHOR>
 * @since 2022-8-9 8:58:18
 */
@ApiModel("外部应用信息表")
@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@Table(name = "SYS_THIRD_APPLIC_INFO", uniqueConstraints = @UniqueConstraint(columnNames = { "APP_ID" }))
public class ThirdApplicInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	@Column(name = "APP_ID", length = 50)
	@ApiModelProperty("应用标识")
	private String appId;

	@Column(name = "APP_NAME")
	@ApiModelProperty("应用名称")
	private String appName;

	@Column(name = "APP_DESCRIPTION")
	@ApiModelProperty("应用描述")
	private String appDescription;

	@Column(name = "EXPIRATION")
	@ApiModelProperty("终止时间")
	private Date expiration;

	@Column(name = "ALLOW_IP", length = 1000)
	@ApiModelProperty("允许登录的IP地址,逗号分隔，支持*号替代")
	private String allowIps;

	@Column(name = "ENABLED")
	@ApiModelProperty("是否启用")
	private Integer enabled;
}
