package com.yunhesoft.system.tools.classExec.entry.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 目标传导公式条件
 * 
 * @Description:
 * <AUTHOR>
 * @date 2021年11月17日
 */
@Data
@ApiModel(value="目标传导公式DTO类",description="目标传导公式DTO类")
public class MtmFormulaTreeDto {
	@ApiModelProperty(value = "树形节点父ID,加载根节点时会传入机构代码用于辅助根节点的加载(为了扩展性，这里可能传入多个机构代码，用逗号分隔)")
	private String pId;
	@ApiModelProperty(value = "是否在加载根节点")
	private Boolean isRootLoad;
	@ApiModelProperty(value = "模块编码")
	private String moduleCode;
	@ApiModelProperty(value = "树形加载参数")
	private String treeParam;
}
