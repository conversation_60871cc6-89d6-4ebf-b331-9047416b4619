package com.yunhesoft.system.tools.classExec.entry.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 非系统包类执行配置
 * @category 非系统包类执行配置
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_CLASS_EXEC_CONFIG")
public class SYSClassExecConfig extends BaseEntity {

	private static final long serialVersionUID = 1L;

	 /** 模块编码 */
    @Column(name="MODULECODE", length=50)
    private String moduleCode;
    
    /** 模块名称 */
    @Column(name="MODULENAME", length=50)
    private String moduleName;
    
    /** 执行类型 */
    @Column(name="EXECTYPE", length=200)
    private String execType;
    
    /** 程序Class路径（需要继承工厂类） */
    @Column(name="CLASSPATH", length=500)
    private String classPath;
    
    /** 对象排序 */
    @Column(name="TMSORT")
    private Integer tmSort;
	
    /** 服务名 */
    @Column(name="SERVICE_NAME", length=500)
    private String serviceName;
    
    /** 树形获取服务 */
    @Column(name="GETFORMULATREE", length=500)
    private String getFormulaTree;
    
    /** 公式解析服务 */
    @Column(name="GETFORMULAVALUE", length=500)
    private String getFormulaValue;
    
    /** 获取json服务 */
    @Column(name="GETJSONDATA", length=500)
    private String getJsonData;
    
    /** 保存json服务 */
    @Column(name="SAVEJSONDATA", length=500)
    private String saveJsonData;

    /** 初始化公式服务 */
    @Column(name="INIT", length=500)
    private String init;

}
