package com.yunhesoft.system.tools.dbDictionary.service;

import java.util.List;

import com.yunhesoft.system.tools.dbDictionary.entity.dto.Dictionaries;
import com.yunhesoft.system.tools.dbDictionary.entity.dto.DictionariesFl;
import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesData;
import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesTypes;

/**
 * IDictionariesDataService
 *
 * <AUTHOR>
 * @date 2019/12/24
 */
public interface IDictionariesDataService {

	/**
	 * 返回数据字典分类
	 *
	 * @return List<DictionariesInfo>
	 */
	public List<DictionariesFl> getDictionariesInfo();

	/**
	 * 返回数据字典列信息
	 * 
	 * @param ptmuid 分类的tmuid
	 * @return List<DictionariesData>
	 */
	public List<DictionariesData> getDictionariesData(String ptmuid);

	/**
	 * 根据表id 返回分类，表，列 全部信息
	 * 
	 * @param tmuid
	 * @return
	 */
	public Dictionaries getDictionaries(String tmuid);

	/**
	 * 返回数据字典列类型
	 * 
	 * @return List<DictionariesTypes>
	 */
	public List<DictionariesTypes> getDictionariesTypes();

	/**
	 * 更新数据字典分类信息
	 * 
	 * @param tmuid       分类表id
	 * @param chineseName 分类名
	 * @return boolean
	 */
	public boolean updateInfo(String tmuid, String chineseName);

	/**
	 * 删除数据字典分类信息
	 * 
	 * @param tmuid        分类表id
	 * @param chinese_name 分类名
	 * @return boolean
	 */
	public boolean deleteInfo(String tmuid, String name);

	/**
	 * 拖动更新数据字典分类信息
	 * 
	 * @param tmuid  分类表id
	 * @param ptmuid 分类名
	 * @return boolean
	 */
	public boolean dragUpdateInfo(String tmuid, String ptmuid);

	/**
	 * 保存数据字典列信息
	 * 
	 * @param dis 接收前台json
	 * @return boolean
	 */
	public String saveData(Dictionaries dis);

	/**
	 * 删除数据字典分类信息
	 * 
	 * @param tmuid 是分类id
	 * @return boolean
	 */
	public boolean deleteDictionariesInfo(String tmuid);

	/**
	 * 删除数据字典列信息
	 * 
	 * @param tmuid 列数据id
	 * @return boolean
	 */
	public boolean deleteDictionariesData(String tmuid);
}
