package com.yunhesoft.system.tools.dbDictionary.service.impl;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.SysUserUtil;
import com.yunhesoft.system.tools.dbDictionary.entity.po.DictionariesData;
import com.yunhesoft.system.tools.dbDictionary.entity.vo.DictionariesDataVo;
import com.yunhesoft.system.tools.dbDictionary.service.IDataBaseOperationService;
import com.yunhesoft.system.tools.dbDictionary.service.IDictionariesColumn;

/**
 * <AUTHOR>
 * @Description: 数据库操作接口实现$
 * @date 2022/4/29
 */
@Service
public class DataBaseOperationServiceImpl implements IDataBaseOperationService {
	@Autowired
	IDataBaseOperationService idbos;
	private Connection conn = null;
	private ResultSet rs = null;
	private PreparedStatement ps = null;
	@Autowired
	public DataSource ds;
	@Autowired
	private IDictionariesColumn idc;

	@Autowired
	private EntityService dao;

	/**
	 * 数据库类型
	 */
	@Override
	public String getType() {
		return dao.getDatabaseType().toLowerCase();
	}

	/**
	 * 判断使用的是什么数据库
	 *
	 * @return
	 * <AUTHOR>
	 */

//	private static String whichDataBase() {
//		String type = "";
//		// 从系统配置文件 application.properties 中获取当前项目所用的数据库
//		Properties props = new Properties();
//		try {
//			props.load(
//					DataBaseOperationServiceImpl.class.getClassLoader().getResourceAsStream("application.properties"));
//			type = props.getProperty("spring.profiles.active");
//		} catch (IOException e) {
//			throw new RuntimeException(e);
//		}
//		return type;
//	}

	/**
	 * 创建表
	 *
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Boolean createTable(List<DictionariesDataVo> columnList, String tableName) {
		StringBuffer sql = new StringBuffer();
		sql.append("create table " + tableName.toUpperCase() + "(");
		if (StringUtils.isNotEmpty(columnList)) {
			for (int i = 0; i < columnList.size(); i++) {
				DictionariesData e = columnList.get(i);
				// 列名
				String columnName = e.getColumnName();
				// 类型
				String columnType = e.getColumnType();
				// 描述
				// String columnDescribes = e.getColumnDescribes();
				// 主键
				Integer keyable = e.getKeyable();
				String isKey = "";
				if (keyable == 1) {
					isKey = "primary key";
				}
				// 是否为空
				Integer notNullable = e.getNotNullable();
				String isNull = "";
				if (notNullable == 1) {
					isNull = "null";
				} else {
					isNull = "not null";
				}
				// 默认值
//					String columnDefault=e.getColumnDefault();
				// 标识
//					int columnMark =e.getColumnMark();
				sql.append(columnName + " " + columnType + " " + isKey + " " + isNull);
				if (i < columnList.size() - 1) {
					sql.append(",");
				}
			}
			sql.append(")");
		}
		execSql(sql);
		return true;
	}

	/**
	 * 删除表
	 *
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Boolean deleteTable(String tableName) {
		StringBuffer sql = new StringBuffer();
		sql.append("DROP TABLE " + tableName);
		// execSql(sql); //安全性太低了， 不允许这么操作 by x.zhong
		return true;
	}

	/**
	 * 修改表
	 *
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Boolean alertTable(List<DictionariesDataVo> columnList, String tableName) {
		// 修改表
		List<DictionariesDataVo> addList = columnList.stream().filter(i -> i.flag != null && i.flag == 0)
				.collect(Collectors.toList());
		List<DictionariesDataVo> upList = columnList.stream().filter(i -> i.flag != null && i.flag == 1)
				.collect(Collectors.toList());
		List<DictionariesDataVo> delList = columnList.stream().filter(i -> i.flag != null && i.flag == 2)
				.collect(Collectors.toList());
		List<StringBuffer> sqlList = new ArrayList<>();

		if (StringUtils.isNotEmpty(addList)) {
			// 添加列语句
			for (DictionariesDataVo elem : addList) {
				StringBuffer sql = new StringBuffer();
				sql.append("ALTER TABLE " + tableName + " ADD ");
				sql.append(elem.getColumnName() + " " + elem.getColumnType() + " ");
				String isnull = "";
				if (elem.getNotNullable() == 1) {
					isnull = "null";
				} else {
					isnull = "not null";
				}
				sql.append(isnull + "; ");
				sqlList.add(sql);
				StringBuffer sql1 = new StringBuffer();
				if (elem.getKeyable() == 1) {
					if (this.getType().contains("mysql")) {
						StringBuffer sql2 = new StringBuffer();
						// sql2.append("ALTER TABLE " + tableName + " DROP PRIMARY KEY; ");
						// sqlList.add(sql2);
						// sql1.append("ALTER TABLE " + tableName + " ADD PRIMARY KEY (" +
						// elem.getColumnName() + "); ");
					} else if (this.getType().contains("sqlserver")) {
//						String drop = "DECLARE @NAME SYSNAME\n" + "DECLARE @TB_NAME SYSNAME\n" + "\tSET @TB_NAME = '"
//								+ tableName + "'\n" + "\tSELECT TOP 1 @NAME = NAME FROM SYS.OBJECTS WITH(NOLOCK)\n"
//								+ "WHERE TYPE_DESC ='PRIMARY_KEY_CONSTRAINT' AND PARENT_OBJECT_ID = (SELECT OBJECT_ID FROM SYS.OBJECTS WITH(NOLOCK) WHERE NAME = @TB_NAME )\n"
//								+ "SELECT @NAME\n" + "\tDECLARE @ALTERSQL NVARCHAR(MAX)\n"
//								+ "SET @ALTERSQL=N'ALTER TABLE '+@TB_NAME+' DROP CONSTRAINT '+@NAME\n"
//								+ "\tEXEC SP_EXECUTESQL @ALTERSQL;\n" + " ";
//						sql1.append(drop);
//						String crkey = "ALTER TABLE [dbo].[" + tableName + "] ADD PRIMARY KEY (" + elem.getColumnName()
//								+ ")\n" + " ";
//						sql1.append(crkey);
					}
				}
				sqlList.add(sql1);
			}
		}
		if (StringUtils.isNotEmpty(upList)) {
			StringBuffer sql = new StringBuffer();
			// 更新列语句
			if (this.getType().contains("mysql")) {
				for (DictionariesDataVo elem : upList) {
					StringBuffer sql3 = new StringBuffer();
					sql3.append("ALTER TABLE " + tableName + " change ");
					sql3.append(
							elem.getOldColumnName() + " " + elem.getColumnName() + " " + elem.getColumnType() + " ");
					String isnull = "";
					if (elem.getNotNullable() == 1) {
						isnull = "null";
					} else {
						isnull = "not null ";
					}
					sql.append(isnull + "; ");
					sqlList.add(sql3);
					if (elem.getKeyable() == 1) {
//						StringBuffer sql1 = new StringBuffer();
//						sql1.append("ALTER TABLE " + tableName + " DROP PRIMARY KEY; ");
//						sqlList.add(sql1);
//						StringBuffer sql2 = new StringBuffer();
//						sql2.append("ALTER TABLE " + tableName + " ADD PRIMARY KEY (" + elem.getColumnName() + "); ");
//						sqlList.add(sql2);
					}
				}

			} else if (this.getType().contains("sqlserver")) {
				for (DictionariesDataVo elem : upList) {
					if (!elem.getOldColumnName().equals(elem.getColumnName())) {
						sql.append("EXEC sp_rename '" + tableName + ".[" + elem.getOldColumnName() + "]','"
								+ elem.getColumnName() + "','COLUMN'" + " ");
					}
					sql.append("alter table " + tableName + " alter column " + elem.getColumnName() + " "
							+ elem.getColumnType() + " ");
					String isnull = "";
					if (elem.getNotNullable() == 1) {
						isnull = "null";
					} else {
						isnull = "not null";
					}
					sql.append(isnull + " ");
					sqlList.add(sql);
					if (elem.getKeyable() == 1) {
						StringBuffer sql1 = new StringBuffer();
//						String drop = "DECLARE @NAME SYSNAME\n" + "DECLARE @TB_NAME SYSNAME\n" + "\tSET @TB_NAME = '"
//								+ tableName + "'\n" + "\tSELECT TOP 1 @NAME = NAME FROM SYS.OBJECTS WITH(NOLOCK)\n"
//								+ "WHERE TYPE_DESC ='PRIMARY_KEY_CONSTRAINT' AND PARENT_OBJECT_ID = (SELECT OBJECT_ID FROM SYS.OBJECTS WITH(NOLOCK) WHERE NAME = @TB_NAME )\n"
//								+ "SELECT @NAME\n" + "\tDECLARE @ALTERSQL NVARCHAR(MAX)\n"
//								+ "SET @ALTERSQL=N'ALTER TABLE '+@TB_NAME+' DROP CONSTRAINT '+@NAME\n"
//								+ "\tEXEC SP_EXECUTESQL @ALTERSQL;\n" + " ";
//						sql1.append(drop);
//						sqlList.add(sql1);
//						StringBuffer sql2 = new StringBuffer();
//						String notnull = "ALTER TABLE [dbo].[" + tableName.toUpperCase() + "] alter column "
//								+ elem.getColumnName() + " " + elem.getColumnType() + " NOT NULL;";
//						sql2.append(notnull);
//						sqlList.add(sql2);
//						StringBuffer sql3 = new StringBuffer();
//						String crkey = "ALTER TABLE [dbo].[" + tableName + "] ADD PRIMARY KEY (" + elem.getColumnName()
//								+ ")\n" + " ";
//						sql3.append(crkey);
//						sqlList.add(sql3);

					}

				}
			}

		}
		if (StringUtils.isNotEmpty(delList)) {
			StringBuffer sql = new StringBuffer();
			// 删除列
			for (DictionariesDataVo elem : delList) {
				// sql.append("ALTER TABLE " + tableName + " drop column " +
				// elem.getColumnName() + " ");
				sqlList.add(sql);
			}
		}
		if (StringUtils.isNotEmpty(sqlList)) {
			execSql(sqlList);
		}

		return null;
	}

	/**
	 * 识别创建操作还是修改 表操作
	 *
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public String creat0rAlert(List<DictionariesDataVo> columnList, String tableName) {
		Boolean isExist = verdictTableName(tableName);
		if (isExist) {
			// 存在此表格
			Boolean flag = alertTable(columnList, tableName);
		} else {
			// 不存在此表格
			Boolean flag = createTable(columnList, tableName);
		}
		return null;
	}

	/**
	 * 校验表是否已经存在
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private boolean verdictTableName(String tableName) {
		boolean bool = false;
		List<String> tablanameList = this.getAllTables();
		for (String name : tablanameList) {
			if (tableName.toLowerCase().equals(name.toLowerCase())) {
				return true;
			}
		}
		return bool;
	}

	/**
	 * 数据库 全部 表名
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private List<String> getAllTables() {
		List<String> tablanameList = new ArrayList<>();
		try {
			conn = ds.getConnection();
			DatabaseMetaData meta = conn.getMetaData();
			ResultSet rs = meta.getTables(null, null, null, new String[] { "TABLE" });
			while (rs.next()) {
				tablanameList.add(rs.getString(3));
			}
		} catch (SQLException e) {
			throw new RuntimeException(e);
		} finally {
			closeConn();
		}
		return tablanameList;
	}

	/**
	 * 获取数据库连接
	 *
	 * @param
	 * @return
	 */
	private ResultSet selectSql(String sql) {
		try {
			conn = ds.getConnection();
			System.out.println(sql);
			ps = conn.prepareStatement(sql);
			rs = ps.executeQuery();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return rs;
	}

	/**
	 * sql 执行
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	private void execSql(StringBuffer sql) {
		if (sql == null) {
			return;
		}
		try {
			conn = ds.getConnection();
			System.out.println(sql.toString());
			ps = conn.prepareStatement(sql.toString());
			ps.execute();
		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			closeConn();
		}
	}

	private void execSql(List<StringBuffer> sql) {
		Statement statement = null;
		try {
			conn = ds.getConnection();
			statement = conn.createStatement();
			for (StringBuffer str : sql) {
				if (ObjUtils.notEmpty(str)) {
					// System.out.println(str.toString());
					statement.addBatch(str.toString());
				}
			}
			statement.executeBatch();

		} catch (SQLException e) {
			e.printStackTrace();
		} finally {
			try {
				statement.close();
			} catch (SQLException e) {
				throw new RuntimeException(e);
			}
			closeConn();

		}
	}

	/**
	 * 关闭连接
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	public void closeConn() {
		try {
			if (rs != null) {
				rs.close();
			}
			if (ps != null) {
				ps.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

	}

	/**
	 * 同步数据库到数据字典中
	 *
	 * @param
	 * @return
	 * <AUTHOR>
	 */
	@Override
	public Boolean syncDataBase() {
		Boolean bool = idc.getFzSql();
		return bool;
	}

	/**
	 * 判断当前是不是管理员
	 *
	 * @return
	 */
	@Override
	public Boolean isAdmin(String tableName) {
		// 当前登录是不是超级管理员
		Boolean isadmin = SysUserUtil.isAdmin();
		if (isadmin) {
			return isadmin;
		}
		// 不是管理员

		// 当前表是否有数据
		StringBuffer sql = new StringBuffer();
		sql.append("select count(*) as c from ");
		sql.append(tableName);
		ResultSet rs = selectSql(sql.toString());
		Boolean hasData = false;
		try {

			if (rs != null && rs.next()) {
				Integer i = rs.getInt(1);
				if (i != null && i > 0) {
					return true;
				}
			}

//			if (ObjUtils.notEmpty(rs)) {
//				if (rs.next()) {
//					hasData = true;
//				}
//			}

		} catch (SQLException e) {
			throw new RuntimeException(e);
		} finally {
			closeConn();
		}
		return hasData;
	}
}
