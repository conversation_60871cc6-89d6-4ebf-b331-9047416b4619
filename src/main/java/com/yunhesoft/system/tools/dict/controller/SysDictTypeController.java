package com.yunhesoft.system.tools.dict.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.core.constants.UserConstants;
import com.yunhesoft.system.tools.dict.entity.SysDictType;
import com.yunhesoft.system.tools.dict.service.ISysDictTypeService;

import io.swagger.annotations.Api;

/**
 * 数据字典信息
 * 
 */
@Api(tags = "字典管理")
@RestController
@RequestMapping("/system/dict/type")
public class SysDictTypeController {
	@Autowired
	private ISysDictTypeService dictTypeService;

	@SuppressWarnings("unchecked")
	@GetMapping("/list")
	public Res<List<SysDictType>> list(SysDictType dictType) {
		List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
		return Res.OK(list);
	}

//	@GetMapping("/export")
//	public Res<?> export(SysDictType dictType) {
//		List<SysDictType> list = dictTypeService.selectDictTypeList(dictType);
//		ExcelUtil<SysDictType> util = new ExcelUtil<SysDictType>(SysDictType.class);
//		return util.exportExcel(list, "字典类型");
//	}

	/**
	 * 查询字典类型详细
	 */
	@GetMapping(value = "/{dictId}")
	public Res<?> getInfo(@PathVariable String dictId) {
		return Res.OK(dictTypeService.selectDictTypeById(dictId));
	}

	/**
	 * 新增字典类型
	 */
	@PostMapping("/add")
	public Res<?> add(@Validated @RequestBody SysDictType dict) {
		if (UserConstants.NOT_UNIQUE.equals(dictTypeService.checkDictTypeUnique(dict))) {
			return Res.FAIL("新增字典'" + dict.getDictName() + "'失败，字典类型已存在");
		}
//		dict.setCreateBy(SecurityUtils.getUsername());
		return Res.OK(dictTypeService.insertDictType(dict));
	}

	/**
	 * 修改字典类型
	 */
	@PostMapping("/edit")
	public Res<?> edit(@Validated @RequestBody SysDictType dict) {
		return Res.OK(dictTypeService.updateDictType(dict));
	}

	/**
	 * 删除字典类型
	 */
	@GetMapping("/deltype/{dictId}")
	public Res<?> remove(@PathVariable String dictId) {
		return Res.OK(dictTypeService.deleteDictTypeById(dictId));
	}

	/**
	 * 清空缓存
	 */
	@GetMapping("/clearCache")
	public Res<?> clearCache() {
		dictTypeService.clearCache();
		return Res.OK();
	}

	/**
	 * 获取字典选择框列表
	 */
	@GetMapping("/optionselect")
	public Res<?> optionselect() {
		List<SysDictType> dictTypes = dictTypeService.selectDictTypeAll();
		return Res.OK(dictTypes);
	}
}
