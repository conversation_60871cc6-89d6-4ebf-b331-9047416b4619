package com.yunhesoft.system.tools.eval.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 系统函数
 * 
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "SYS_CUSTOMFUN")
public class SysCustomfun extends BaseEntity {

	private static final long serialVersionUID = 1L;

	@Column(name = "FUNNAME", length = 50)
	private String funname;

	@Column(name = "FUNBODY", nullable = true, length = 2000)
	private String funbody;

	@Column(name = "FUNMEMO", nullable = true, length = 500)
	private String funmemo;

	@Column(name = "TMSORT")
	private Integer tmsort;

	@Column(name = "FUNTYPE", length = 50)
	private String funType;

}
