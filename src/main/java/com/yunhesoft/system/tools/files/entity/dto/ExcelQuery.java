package com.yunhesoft.system.tools.files.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 数据检索条件
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(value = "Excel导出", description = "Excel导出数据")
public class ExcelQuery {
	/** 当前流程ID */
    @ApiModelProperty(value = "businessKey")
	private String businessKey;
	/** 全流程ID */
    @ApiModelProperty(value = "businessKeys")
	private String businessKeys;
	/** 文件路径 */
    @ApiModelProperty(value = "fileAddress")
	private String fileAddress;
	/** 一个表单的id */
    @ApiModelProperty(value = "formId")
	private String formId;
}
