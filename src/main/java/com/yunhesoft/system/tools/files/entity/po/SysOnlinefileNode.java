package com.yunhesoft.system.tools.files.entity.po;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 在线文档大纲
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "SYS_ONLINEFILE_NODE")
public class SysOnlinefileNode extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 文件id（sys_file_manage） */
	@Column(name = "FILE_ID", length = 50)
	private String fileId;

	/** 版本ID，0模板id对应版本 */
	@Column(name = "VERSION_ID", length = 50)
	private String versionId;

	/** 添加时的版本ID */
	@Column(name = "ADD_VERSION", length = 50)
	private String addVersion;

	/** 添加时的版本排序 */
	@Column(name = "ADD_VERSION_NO")
	private Integer addVersionNo;

	/** 删除时的版本ID */
	@Column(name = "DEL_VERSION", length = 50)
	private String delVersion;

	/** 数据id，与fid对应，模板数据时与ID相同 */
	@Column(name = "DID", length = 50)
	private String did;

	/** 父ID */
	@Column(name = "FID", length = 50)
	private String fid;

	/** 名称 */
	@Column(name = "NODE_NAME", length = 800)
	private String nodeName;

	/** 修改标识 */
	@Column(name = "UPD_MARK")
	private Integer updMark;

	/** 大纲样式，保存原div信息，如果有多个，保存最后一个 */
	@Column(name = "NODE_CLS", length = 2000)
	private String nodeCls;

	/** 修改人姓名 */
	@Column(name = "LAST_UPD_USER", length = 50)
	private String lastUpdUser;

	/** 修改时间 */
	@Column(name = "LAST_UPD_TIME")
	private Date lastUpdTime;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 使用 */
	@Column(name = "TMUSED")
	private Integer tmused;

}
