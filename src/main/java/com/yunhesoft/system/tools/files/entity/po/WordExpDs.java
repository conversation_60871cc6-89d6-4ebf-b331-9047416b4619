package com.yunhesoft.system.tools.files.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * word导出数据源信息表
 * 
 * @category 数据源
 * <AUTHOR>
 *
 */
@Getter
@Setter
@Entity
@Table(name = "WORD_EXP_DS")
public class WordExpDs extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 对应模板ID */
	@Column(name = "TPL_ID", length = 50)
	private String tplId;

	/** 组件类型 */
	@Column(name = "COMPONENTTYPE", length = 50)
	private String componentType;

	/** 默认值 */
	@Column(name = "INITVALUESCRIPT", length = 2000)
	private String initvaluescript;

	/** 备选默认值 */
	@Column(name = "DEFAULTKEYSCRIPT", length = 1000)
	private String defaultKeyScript;

	/** 备选显示值 */
	@Column(name = "DEFAULTVALUESCRIPT", length = 1000)
	private String defaultValueScript;

	/** 参数别名 */
	@Column(name = "PARAALIAS", length = 200)
	private String paraAlias;

	/** 参数名称 */
	@Column(name = "PARANAME", length = 200)
	private String paraName;

	/** 是否应用 */
	@Column(name = "USED")
	private Integer used;

	/** 排序号 */
	@Column(name = "SORT_NO")
	private Integer sortNo;

	@Transient
	private String value;

}