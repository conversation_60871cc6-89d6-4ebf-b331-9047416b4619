package com.yunhesoft.system.tools.files.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 输入参数
 */
@Getter
@Setter
@Entity
@Table(name = "WORD_EXP_DS_INPARA")
public class WordExpDsInpara extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/** 关联参数ID */
	@Column(name = "PARA_ID", length = 50)
	private String paraId;

	/** 模板ID */
	@Column(name = "TPL_ID", length = 50)
	private String tplId;

	/** 数据源别名 */
	@Column(name = "TDSALIAS", length = 100)
	private String tdsalias;

	/** 数据源名称 */
	@Column(name = "TDSNAME", length = 200)
	private String tdsname;

	/** 类名 */
	@Column(name = "TYPENAME")
	private String typename;

	/** 是否应用 */
	@Column(name = "USED")
	private Integer used;

	/** 排序号 */
	@Column(name = "SORT_NO")
	private Integer sortNo;
}