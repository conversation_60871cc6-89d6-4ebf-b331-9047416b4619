package com.yunhesoft.system.tools.form.entity;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模板分类 SYS_FORM_TPL_TYPE
 * 
 */
@Entity
@Table(name = "SYS_TEMPLATE_RELATION")
@Data
public class SysTemplateRelation extends BaseEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	/** 模板分类名称 */
	@Column(name = "TPL_ID", length = 50)
	@ApiModelProperty(value = "模板id")
	private String tplId;
	
	/** 关联id */
	@Column(name = "RELATED_ID", length = 50)
	@ApiModelProperty(value = "关联id")
	private String relatedId;
	
	/** 关联类型 1:表单 2:文件 */
	@Column(name = "RELATED_TYPE")
	@ApiModelProperty(value = "关联类型")
	private Integer relatedType;
	
	/** 排序 */
	@Column(name = "SORT")
	private Long sort;
	
	
}
