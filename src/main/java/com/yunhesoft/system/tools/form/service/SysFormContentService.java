package com.yunhesoft.system.tools.form.service;

import java.util.List;

import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.system.tools.form.dto.SysFormDto;

/**
 * 自定义表单内容
 * <AUTHOR>
 *
 */
public interface SysFormContentService {
	
	/**
	 * 根据表单id和版本号（版本号暂时无用，预留以后）获取表单内容
	 * @param formId 表单id
	 * @param version(暂时无用，传null即可)
	 * @return
	 */
	SysFormDto getSysFormContent (String formId, String version);
	
	/**
	 * 根据内容id获取表单内容
	 * @param contentId 表单内容id
	 * @return
	 */
	SysFormDto getSysFormContentById (String contentId);
	
	/**
	 * 保存表单内容
	 * @param SysFormDto dto表单实体类
	 * @param user 操作用户
	 * @return
	 */
	Boolean saveSysFormContent (SysFormDto dto, SysUser user);
	
	/**
	 * 根据内容id删除表单内容
	 * @param contentId 表单内容id
	 * @param user 操作用户
	 * @return
	 */
	Boolean deleteSysFormContent (String contentId, SysUser user);
	
	/**
	 * 根据模板id获取所有表单内容
	 * @param tplId 模板id
	 * @return
	 */
	List<SysFormDto> getTplFormContents (String tplId);
}
