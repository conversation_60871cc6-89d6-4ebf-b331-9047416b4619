package com.yunhesoft.system.tools.form.service.impl;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.model.SysUser;
import com.yunhesoft.core.common.utils.DateTimeUtils;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.ObjUtils;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.kernel.service.model.Order;
import com.yunhesoft.system.kernel.service.model.Where;
import com.yunhesoft.system.tds.model.IDataSource;
import com.yunhesoft.system.tds.service.IDataSourceService;
import com.yunhesoft.system.tools.form.dto.SysFormDataDto;
import com.yunhesoft.system.tools.form.dto.SysFormDto;
import com.yunhesoft.system.tools.form.entity.SysFormDataIndex;
import com.yunhesoft.system.tools.form.service.SysFormContentService;
import com.yunhesoft.system.tools.form.service.SysFormDataService;
import com.yunhesoft.system.tools.form.service.SysFormService;

import lombok.extern.log4j.Log4j2;

@Log4j2
@Service
public class SysFormDataServiceImpl implements SysFormDataService {

	@Autowired
	private EntityService dao;

	@Autowired
	private SysFormService formSrc;
	
	@Autowired
	private SysFormContentService contentSrc;

	@Autowired
	private IDataSourceService tdsService;

	/**
     * 保存单个表单数据
     *
     * @param SysFormDto dto 传入参数实体类
     * @param user       操作用户
     * @return
     */
    @Override
    public synchronized SysFormDto getSysFormData(SysFormDto dto) {
        return this.getSysFormDataSync(dto);
    }
    /**
     * 获取单个表单数据
     *
     * @param SysFormDto dto 传入参数实体类
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = SQLException.class)
    public SysFormDto getSysFormDataSync(SysFormDto dto) {
		SysFormDto res = new SysFormDto();
		List<SysFormDataIndex> list = null;
		String formId = dto.getFormId();
		String dataId = dto.getDataId();
		if (dataId != null && dataId.length() > 0) {
			Where where = Where.create();
			where.eq(SysFormDataIndex::getFormId, formId);
			where.eq(SysFormDataIndex::getDataId, dataId);
			list = dao.rawQueryListByWhere(SysFormDataIndex.class, where);
		}
		SysFormDataIndex data = null;
		if (list == null || list.size() == 0) {
			SysFormDataIndex dataIndex = new SysFormDataIndex();
			dataIndex.setId(TMUID.getUID());
			if (dataId == null || dataId.length() == 0) {
				dataId = TMUID.getUID();
			}
			dataIndex.setDataId(dataId);
			dataIndex.setFormId(formId);
			dataIndex.setRootDataId(dto.getRootDataId());
			SysFormDto content = contentSrc.getSysFormContent(formId, null);
			dataIndex.setContentId(ObjUtils.isEmpty(content)?"":content.getFormContentId());
			dao.rawSave(dataIndex);
			data = dataIndex;
		}
//		log.info(list.size());
		
//		if (list != null && list.size() > 0) {
//			dao.queryList(clazz,Table.create("tpl"),Where.create("name=?",23),Order.create("id desc"),Select.create("id","name"));
//			SysFormDataIndex data = list.get(0);
			if (list != null && list.size() > 0) {
				data = list.get(0);
			}
			res.setDataIndexId(data.getId());	//索引表主键id
			res.setDataId(data.getDataId());	//数据统一dataId
			res.setRootDataId(data.getRootDataId());
			String tableNames = null;
			if (!ObjUtils.isEmpty(formId)) {
				SysFormDto form = formSrc.getSysFormById(formId);
				if (!ObjUtils.isEmpty(form)) {
					tableNames = form.getTableNames();
					res.setFormId(form.getFormId());
					res.setFormName(form.getFormName());
					res.setFormAlias(form.getFormAlias());
					res.setFormDesc(form.getFormDesc());
					res.setTableNames(tableNames);
				}
			}

			if (!ObjUtils.isEmpty(tableNames)) {
//				List<HashMap<String, String>> formData = new ArrayList<HashMap<String, String>>();
				List<SysFormDataDto> formData = new ArrayList<SysFormDataDto>();
				String[] tables = tableNames.split(",");
				for (String tableName : tables) {
					this.initFormDataColumn(tableName);
//					log.info();
					try {
					    List<Map<String, Object>> listdata = StringUtils.isEmpty(dto.getTableRowId()) ?
	                            dao.queryListMap("select * from " + tableName + " where FORM_DATA_INDEX_ID=?", data.getId())
	                                : dao.queryListMap("select * from " + tableName + " where ID=?", dto.getTableRowId());

//						log.info(listdata);
						if (listdata == null || listdata.size() == 0) {
							continue;
						}
						Map<String, Object> map = listdata.get(0);
						Set<String> keys = map.keySet();
						for (String key : keys) {
//						HashMap<String, String> c = new HashMap<String, String>();
//						c.put("tableName", tableName);
//						c.put("columnName", key);
//						String value = null;
//						try {
//							value = (String) map.get(key);
//						} catch (Exception e) {
//						}
//						c.put("value", value);
							SysFormDataDto c = new SysFormDataDto();
							c.setTableName(tableName);
							c.setColumnName(key);
							Object value = null;
							try {
								value = map.get(key);
							} catch (Exception e) {
							}
							if (StringUtils.isNotEmpty(dto.getTableRowId()) && "FORM_DATA_INDEX_ID".equals(key.toUpperCase()) && StringUtils.isEmpty((String)value)) {
                                //给定了行记录id但是form_data_index_id为空，需要将该字段补充
                                value = data.getId();
                                dao.rawUpdate("update " + tableName + " set FORM_DATA_INDEX_ID=? where ID=?", value, dto.getTableRowId());
                            }
							c.setValue(value);
							formData.add(c);
						}

					} catch (Exception e) {
						log.error("", e);
					}
//					SqlRowSet rowset = dao.rawQueryList("select * from "+tableName+" where FORM_DATA_INDEX_ID=?", data.getDataIndexId());
//					String[] colNames = rowset.getMetaData().getColumnNames();
//					log.info(rowset.getObject(1));
//					int k = 0;
//					while (rowset.next()) {
//						String colLabel = rowset.getMetaData().getColumnLabel(k);
//						String value = rowset.getString(k);
//						log.info(colLabel);
//						log.info(value);
//						k++;
//					}
					/*
					 * for (int i=0; i<rowset.getRow(); i++) { String colLabel =
					 * rowset.getMetaData().getColumnLabel(i); String value = rowset.getString(i);
					 * // Object obj = rowset.getObject(colName); // rowset.getMetaData().get
					 * log.info(colLabel); log.info(value); HashMap<String, String> map = new
					 * HashMap<String, String>(); map.put("tableName", tableName); //
					 * map.put("colName", colName); // map.put("value", rowset.getString(colName));
					 * tplData.add(map); }
					 */
				}
				res.setFormData(formData);
			}

//		}
		return res;
	}

	public boolean initFormDataColumn(String tableName) {
//		if (tableName == null || tableName.length() == 0) {
//			return false;
//		}
//		List<TableColumnInfo> columnList = dao.tableColumnList(tableName);
//		if (columnList != null && columnList.size() > 0) {
//			boolean addflag = true;
//			for (TableColumnInfo column : columnList) {
//				String columnName = column.getColumnName();
//				columnName = columnName.toUpperCase();
//				if ("FORM_DATA_INDEX_ID".equals(columnName)) {
//					addflag = false;
//					break;
//				}
//			}
//			if (addflag) {
//				dao.rawExcute("alter table "+ tableName + " add FORM_DATA_INDEX_ID varchar(50)");
//			}
//		}
		return true;
	}

	/**
	 * 保存单个表单数据
	 * 
	 * @param SysFormDto dto 传入参数实体类
	 * @param user       操作用户
	 * @return
	 */
	@Override
	public synchronized String saveSysFormData(SysFormDto dto, SysUser user) {
		return this.saveSysFormDataSync(dto, user);
	}

	/**
	 * 保存单个表单数据
	 * 
	 * @param SysFormDto dto 传入参数实体类
	 * @param user       操作用户
	 * @return
	 */
	@Transactional(readOnly = false, rollbackFor = SQLException.class)
	private String saveSysFormDataSync(SysFormDto dto, SysUser user) {
		String res = "";
		String formId = dto.getFormId();
		String dataId = dto.getDataId();
		SysFormDto form = formSrc.getSysFormById(formId);
		SysFormDataIndex dataIndex = null;
		if (dataId != null && dataId.length() > 0) {
			Where where = Where.create();
			where.eq(SysFormDataIndex::getFormId, formId);
			where.eq(SysFormDataIndex::getDataId, dataId);
			List<SysFormDataIndex> list = dao.rawQueryListByWhere(SysFormDataIndex.class, where);
			if (list != null && list.size() > 0) {
				dataIndex = list.get(0);
			}
		}
		if (form != null) {
			Date now = DateTimeUtils.getNDT();
			String userId = null;
			if (user != null) {
				userId = user.getId();
			}

			// 没有数据索引则新建
			if (dataIndex == null) {
				dataIndex = new SysFormDataIndex();
				dataIndex.setId(TMUID.getUID());
				if (dataId == null || dataId.length() == 0) {
					dataId = TMUID.getUID();
				}
				dataIndex.setDataId(dataId);
				dataIndex.setRootDataId(dto.getRootDataId());
				dataIndex.setFormId(formId);
				dataIndex.setContentId(dto.getFormContentId());
				dataIndex.setCreateBy(userId);
				dataIndex.setCreateTime(now);
			}
			dataIndex.setUpdateBy(userId);
			dataIndex.setUpdateTime(now);
			dao.rawSave(dataIndex);
//			String tableNames = tpl.getTableNames();
//			if (tableNames != null) {

			// 按表名分组
//				List<HashMap<String, String>> datalist =  dto.getFormData();
//				HashMap<String, List<HashMap<String, String>>> tableMap = new HashMap<String, List<HashMap<String, String>>>();
//				for (HashMap<String, String> map : datalist) {
//					String tableName = map.get("tableName");
//					List<HashMap<String, String>> table = tableMap.get(tableName);
//					if (table == null) {
//						table = new ArrayList<HashMap<String, String>>();
//					}
//					table.add(map);
//					tableMap.put(tableName, table);
//				}
			List<SysFormDataDto> datalist = dto.getFormData();
			HashMap<String, List<SysFormDataDto>> tableMap = new HashMap<String, List<SysFormDataDto>>();
			for (SysFormDataDto data : datalist) {
				String tableName = data.getTableName();
				List<SysFormDataDto> table = tableMap.get(tableName);
				if (table == null) {
					table = new ArrayList<SysFormDataDto>();
				}
				table.add(data);
				tableMap.put(tableName, table);
			}

			// 按表名遍历
			Set<String> keys = tableMap.keySet();
//				log.info(SysFormTplType.class.getName());
			for (String tableName : keys) {
				this.initFormDataColumn(tableName);
//					String clsname = ObjUtils.line2camel(tableName.toLowerCase());
//					clsname = "com.yunhesoft.system.tools.form.entity." + clsname.substring(0, 1).toUpperCase() + clsname.substring(1);
//					Class<?> clz = null;
//					try {
//						clz = Class.forName(clsname);
//					} catch (Exception e) {
//						log.error("",e);
//					}
//					if (clz == null) {
//						continue;
//					}
//					Field[] fields = clz.getFields();
//					for (Field field : fields) {
//						log.info(field);
//					}
//					Object bean = clz.newInstance();
//					long count = dao.rawCount("select count(*) from "+tableName+" where form_data_index_id=?", dataIndex.getDataIndexId());
//					SqlRowSet sqlset = null;
//					List<HashMap> list = null;
				long count = 0;
				try {
//						sqlset = dao.rawQuery("select * from "+tableName+" where FORM_DATA_INDEX_ID=?", dataIndex.getId());
					count = dao.rawCount("select count(*) from " + tableName + " where FORM_DATA_INDEX_ID=?",
							dataIndex.getId());
//						Where w = Where.create();
//						w.and("FORM_DATA_INDEX_ID=?", dataIndex.getId());
//						count = dao.queryObject(Long.class,Table.create(tableName),w);
//						log.info(count);
//						list = dao.rawQueryList(HashMap.class, "select * from "+tableName+" where FORM_DATA_INDEX_ID=?", dataIndex.getId());
				} catch (Exception e) {
					continue;
				}
//					log.info(count);
				if (count == 0) {
					dao.rawInsert("insert into " + tableName + " (ID,FORM_DATA_INDEX_ID) values (?,?)", TMUID.getUID(),
							dataIndex.getId());
				}
//					List<HashMap<String, String>> list = tableMap.get(tableName);
//					for (HashMap<String, String> map : list) {
//						String columnName = map.get("columnName");
//						String value = map.get("value");
////						Update.create(func, args)
//						dao.rawUpdate("update "+tableName+" set "+columnName+"=? where FORM_DATA_INDEX_ID=?", value, dataIndex.getId());
//					}
				List<SysFormDataDto> list = tableMap.get(tableName);
				for (SysFormDataDto data : list) {
					String columnName = data.getColumnName();
					Object value = data.getValue();
//						Update.create(func, args)

					// *****如果是JSON对象数组，转换成字符串在保存*****(zouh.20210619)
					if (value instanceof JSONArray) {
						value = value.toString();
					} else if (value instanceof JSONObject) {
						value = value.toString();
					} else if (value instanceof String) {
						String v = (String) value;
						if (v == null || v.length() == 0) {
							value = null;
						}
					}
					// *****如果是JSON对象数组，转换成字符串在保存*****

					dao.rawUpdate("update " + tableName + " set " + columnName + "=? where FORM_DATA_INDEX_ID=?", value,
							dataIndex.getId());
				}
			}
//			}
			res = dataIndex.getDataId();
		} else {

		}

		return res;
	}

	/**
	 * 批量保存表单数据
	 * 
	 * @param List<SysFormDto> 传入参数实体类集合
	 * @param user             操作用户
	 * @return
	 */
	@Override
	public Boolean saveSysFormDataBatch(List<SysFormDto> list, SysUser user) {
		if (list == null || list.size() == 0) {
			return false;
		}
		for (SysFormDto dto : list) {
			this.saveSysFormDataSync(dto, user);
		}
		return true;
	}

	/**
	 * 根据数据id获取模板下所有表单数据
	 * @category 根据数据id获取模板下所有表单数据
	 * @param List<String> dataId 数据id集合
	 * @param List<String> tableList 要过滤保留的表名集合，传入则只返回传入的表的数据，如不传则不过滤
	 * @return Map<String, Map<String, String>> ———— Map<数据id(dataId),
	 *         Map<表名.字段名(全大写), 字段值>>
	 */
	@Override
	public Map<String, Map<String, String>> getSysTplData(List<String> dataIdList, List<String> tableList) {

		Map<String, Map<String, String>> res = new HashMap<String, Map<String, String>>();
		Where where = Where.create();
		where.andIns(SysFormDataIndex::getDataId, dataIdList.toArray());
		List<SysFormDataIndex> dataIndexlist = dao.rawQueryListByWhere(SysFormDataIndex.class, where);

//		List<SysFormDto> formList = formSrc.getSysFormList(tplId);
		if (dataIndexlist != null && dataIndexlist.size() > 0) {
			for (SysFormDataIndex dataIndex : dataIndexlist) {
				String formId = dataIndex.getFormId();
				String dataId = dataIndex.getDataId();
				SysFormDto dto = new SysFormDto();
				dto.setFormId(formId);
				dto.setDataId(dataId);
				SysFormDto resData = null;
				try {
					resData = this.getSysFormData(dto);
				} catch (Exception e) {
					log.error("", e);
				}
				if (ObjUtils.isEmpty(resData)) {
					continue;
				}
				List<SysFormDataDto> list = resData.getFormData();
				if (ObjUtils.isEmpty(list)) {
					continue;
				}
				String formAlias = resData.getFormAlias();
				Map<String, String> map = new HashMap<String, String>();
				map.put("rootDataId", dataIndex.getRootDataId());
				for (SysFormDataDto data : list) {
					String tableName = data.getTableName();
					if (tableName != null && tableName.length() > 0) {
						tableName = tableName.toUpperCase();
					}
					String columnName = data.getColumnName();
					if (columnName != null && columnName.length() > 0) {
						columnName = columnName.toUpperCase();
					}
//						String value = (String) data.getValue();
					String value = null;
					if (data.getValue() != null) {
						value = String.valueOf(data.getValue());
					}
					if (tableList == null || tableList.size() == 0 || tableList.contains(tableName)) {
						map.put(tableName + "." + columnName + (ObjUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase()), value);
					}
				}
				Map<String, String> oldmap = res.get(dataId);
				if (oldmap == null) {
					res.put(dataId, map);
				} else {
					oldmap.putAll(map);
					res.put(dataId, oldmap);
				}
			}
		}

		return res;
	}


	/**
	 * 根据数据id获取全部表单数据
	 * 
	 * @param dataId 数据id，如多个则逗号隔开
	 * @return
	 */
	@Override
	public List<SysFormDataDto> getAllFormDataById(String dataIds) {
		List<SysFormDataDto> res = new ArrayList<SysFormDataDto>();
		List<SysFormDataIndex> dataIndexList = null;
		if (dataIds == null || dataIds.length() <= 0) {
			return res;
		}
		String[] ids = dataIds.split(",");

		for (String dataId : ids) {
			Where where = Where.create();
			where.eq(SysFormDataIndex::getDataId, dataId);
			Order order = Order.create();
			order.orderByAsc(SysFormDataIndex::getUpdateTime);
			order.orderByAsc(SysFormDataIndex::getId);
			dataIndexList = dao.rawQueryListByWhere(SysFormDataIndex.class, where, order);

			if (dataIndexList != null && dataIndexList.size() > 0) {

				for (SysFormDataIndex dataIndex : dataIndexList) {
					String formId = dataIndex.getFormId();

					String tableNames = null;
					if (formId != null && formId.length() > 0) {
						SysFormDto tpl = formSrc.getSysFormById(formId);
						tableNames = tpl.getTableNames();
					}

					if (tableNames != null && tableNames.length() > 0) {
//					List<SysFormDataDto> formData = new ArrayList<SysFormDataDto>();
						String[] tables = tableNames.split(",");
						for (String tableName : tables) {
							this.initFormDataColumn(tableName);
							List<Map<String, Object>> listdata = dao.queryListMap(
									"select * from " + tableName + " where FORM_DATA_INDEX_ID=?", dataIndex.getId());
							if (listdata == null || listdata.size() == 0) {
								continue;
							}
							Map<String, Object> map = listdata.get(0);
							Set<String> keys = map.keySet();
							for (String key : keys) {
								SysFormDataDto c = new SysFormDataDto();
								c.setTableName(tableName);
								c.setColumnName(key);
								Object value = null;
								try {
									value = map.get(key);
								} catch (Exception e) {
								}
								c.setValue(value);
								res.add(c);
							}
						}
//					res.setFormData(formData);
					}
				}
			}

		}

		return res;
	}
	
	
	/**
     * 根据数据id获取模板下所有数据源数据
     * @category 根据数据id获取模板下所有表单数据
     * @param List<String> dataId 数据id集合
     * @return Map<String, Map<String, Object>> ———— 
     * Map<数据id(dataId),Map<数据源别名（原始，不用转换大小写）.数据源输入参数(原始，不用转换大小写).表单别名（如果有，强制转大写）, 输出数据>>
     */
	@Override
    public Map<String, Map<String, Object>> getSysTplDataTds(List<String> dataIdList, List<String> formIdList) {

        Map<String, Map<String, Object>> res = new HashMap<>();
        Where where = Where.create();
        if (StringUtils.isNotEmpty(formIdList)) {
            where.in(SysFormDataIndex::getFormId, formIdList.toArray());
        }
        if (StringUtils.isNotEmpty(dataIdList)) {
            where.in(SysFormDataIndex::getDataId, dataIdList.toArray());
        }
        List<SysFormDataIndex> dataIndexlist = dao.rawQueryListByWhere(SysFormDataIndex.class, where);

        if (StringUtils.isEmpty(dataIndexlist)) {
            return res;
        }

        //遍历索引表，查找表单找出数据源组件
        for (SysFormDataIndex dataIndex : dataIndexlist) {
            String formIndexId = dataIndex.getId();
            String formId = dataIndex.getFormId();
            String dataId = dataIndex.getDataId();

            SysFormDto form = formSrc.getSysFormById(formId);
            String formAlias = form.getFormAlias();
            SysFormDto content = contentSrc.getSysFormContent(formId, null);
            String formContent = content.getFormContent();
            
            //根据表单内容，解析里面的数据源
            Map<String, Object> tdsRes = this.getTdsDataByFormContent(formIndexId, dataId, formId, formAlias, formContent);
            
            if (StringUtils.isEmpty(tdsRes)) {
                continue;
            }

            Map<String, Object> dataMap = res.get(dataId);
            if (dataMap == null) {
                dataMap = new HashMap<>();
            }
            dataMap.putAll(tdsRes);
            res.put(dataId, dataMap);
        }

        return res;
    }
    
    
    /**
     * 根据表单内容加载数据源数据
     * @param dataIndexId 索引id
     * @param dataId 数据id
     * @param formId 表单id
     * @param formAlias 表单别名
     * @param formContent 表单内容
     * @return
     */
    public Map<String, Object> getTdsDataByFormContent (String dataIndexId, String dataId, String formId, String formAlias,String formContent) {
        if (StringUtils.isEmpty(formContent)) {
            return null;
        }
        JSONObject contentObj = JSON.parseObject(formContent);
        List<JSONObject> resParam = findFormComponents(contentObj);
        if (StringUtils.isEmpty(resParam)) {
            return null;
        }
        Map<String, Object> dataMap = new HashMap<>();
        for (JSONObject obj : resParam) {
            String tdsAlias = obj.getString("tdsAlias");
            if (StringUtils.isEmpty(tdsAlias)) {
                //没有数据源别名直接返回
                continue;
            }
            //输入参数
            String tdsInParaAlias = obj.containsKey("tdsInParaAlias") ? obj.getString("tdsInParaAlias") : "";
            
            //工作流相关属性
            String dispatchBusinessKeys = obj.containsKey("dispatchBusinessKeys") ? obj.getString("dispatchBusinessKeys") : "";
            String dispatchUserIds = obj.containsKey("dispatchUserIds") ? obj.getString("dispatchUserIds") : "";
            String dispatchPostIds = obj.containsKey("dispatchPostIds") ? obj.getString("dispatchPostIds") : "";

            //key值用 数据源别名.输入参数（未替换变量前的原始参数）.表单别名（如果有别名）
            String key = tdsAlias + "." + tdsInParaAlias + (ObjUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());

            if (StringUtils.isNotEmpty(tdsInParaAlias)) {
                //替换变量，目前只支持索引id和数据id
                tdsInParaAlias = tdsInParaAlias.replace("@dataindexid", dataIndexId).replace("@dataid", dataId);
            }
            IDataSource ids = tdsService.getTDSData(tdsAlias, tdsInParaAlias);
            if (ids == null) {
                continue;
            }
//            IDataSource ids = tdsService.getTDSData("ppi_djz_xm", tdsInParaAlias);
            dataMap.put(key, ids.getJsonArrayData());
            dataMap.put(key+".flow.formId", formId);
            dataMap.put(key+".flow.dispatchBusinessKeys", dispatchBusinessKeys);
            dataMap.put(key+".flow.dispatchUserIds", dispatchUserIds);
            dataMap.put(key+".flow.dispatchPostIds", dispatchPostIds);
        }
//        System.out.println(dataMap);
        return dataMap;
    }
    
    
    private List<JSONObject> findFormComponents(JSONObject jobj) {
        List<JSONObject> list = new ArrayList<JSONObject>();
        if (ObjUtils.isEmpty(jobj)) {
            return list;
        }
        String component = jobj.getString("component");
        if ("tdsedit-table".equals(component)) {
            //是数据源组件
            Object params = jobj.get("params");
            if (!ObjUtils.isEmpty(params)) {
                list.add((JSONObject) params);
            }
        } else {
            //不是数据源，递归子属性
            for (String key : jobj.keySet()) {
                Object value = jobj.get(key);
                if (value != null && value instanceof JSONObject) {
                    // 判断是否是数据源
                    list.addAll(findFormComponents((JSONObject) value));
                } else if (value != null && value instanceof JSONArray) {
                    JSONArray values = (JSONArray) value;
                    for (Object jobj1 : values) {
                        if (jobj1 instanceof JSONObject) {
                            list.addAll(findFormComponents((JSONObject) jobj1));
                        }
                    }
                }
            }
        }
        return list;
    }
	
	
	/**
     * 获取工作流中表单数据
     * @category 获取工作流中表单数据
     * @param dataIdList 数据id的集合
     * @param formIdList 表单id的集合（传入则只查对应formId,不传则查全部）
     * @return 返回json对象  格式为 {dataId:{formId: [{dispatchBusinessKeys:[...],dispatchUserIds:[...],dispatchPostIds:[...],}]},...}
     */
    @Override
    public JSONObject getSysTplDataFlow(List<String> dataIdList, List<String> formIdList) {

        // 获取全部数据源变量
        Map<String, Map<String, Object>> tdsResData = this.getSysTplDataTds(dataIdList, formIdList);
        if (StringUtils.isEmpty(tdsResData)) {
            // 为空直接返回
            return null;
        }

        JSONObject res = new JSONObject();

        Set<String> keys = tdsResData.keySet();
        for (String dataId : keys) {
            Map<String, Object> tdsMap = tdsResData.get(dataId);
            if (StringUtils.isEmpty(tdsMap)) {
                continue;
            }

            Object dataIndexMap = res.get(dataId);
            JSONObject dataIndexObj = dataIndexMap == null ? new JSONObject() : (JSONObject) dataIndexMap;

            Set<String> mapkeys = tdsMap.keySet();
            for (String mapkey : mapkeys) {
                Object jsonObj = tdsMap.get(mapkey);
                if (jsonObj == null || !(jsonObj instanceof JSONArray)) {
                    // 无记录
                    continue;
                }
                JSONArray jarray = (JSONArray) jsonObj;

                // 表单id
                String formId = (String) tdsMap.get(mapkey + ".flow.formId");

                Object formObj = dataIndexObj.get(formId);
                JSONArray formArray = formObj == null ? new JSONArray() : (JSONArray) formObj;

                String dispatchBusinessKeys = (String) tdsMap.get(mapkey + ".flow.dispatchBusinessKeys");
                String dispatchUserIds = (String) tdsMap.get(mapkey + ".flow.dispatchUserIds");
                String dispatchPostIds = (String) tdsMap.get(mapkey + ".flow.dispatchPostIds");
                dispatchBusinessKeys = dispatchBusinessKeys == null ? "" : dispatchBusinessKeys;
                dispatchUserIds = dispatchUserIds == null ? "" : dispatchUserIds;
                dispatchPostIds = dispatchPostIds == null ? "" : dispatchPostIds;

                JSONArray array1 = new JSONArray();
                JSONArray array2 = new JSONArray();
                JSONArray array3 = new JSONArray();
                // 遍历数据源的数据，组装
                for (Object o : jarray) {
                    if (o instanceof JSONObject) {
                        JSONObject jobj = (JSONObject) o;
                        array1.add(jobj.containsKey(dispatchBusinessKeys) ? jobj.getString(dispatchBusinessKeys) : "");
                        array2.add(jobj.containsKey(dispatchUserIds) ? jobj.getString(dispatchUserIds) : "");
                        array3.add(jobj.containsKey(dispatchPostIds) ? jobj.getString(dispatchPostIds) : "");
                    }
                }
                JSONObject obj = new JSONObject();
                obj.put("dispatchBusinessKeys", array1);
                obj.put("dispatchUserIds", array2);
                obj.put("dispatchPostIds", array3);
                formArray.add(obj);
                dataIndexObj.put(formId, formArray);
            }

            res.put(dataId, dataIndexObj);
        }
        return res;
    }
    
    
    /**
     * 根据数据id获取formid和dataindexid的map
     * @category 根据数据id获取formid和dataindexid的map
     * @param dataId 数据id
     * @return 返回map对象  key为formId，value为dataIndexId
     */
    @Override
    public Map<String, String> getDataIndexIdMapByDataId(String dataId) {
        List<SysFormDataIndex> dataIndexList = dao.rawQueryListByWhere(SysFormDataIndex.class, Where.create().eq(SysFormDataIndex::getDataId, dataId));
        if (StringUtils.isEmpty(dataIndexList)) {
            return null;
        }
        return dataIndexList.stream().collect(Collectors.toMap(SysFormDataIndex::getFormId, v -> v.getId()));
    }
    
    /**
 	 * 根据数据id获取表单模板id
 	 * @category 根据数据id获取表单模板id
 	 * @param dataId 数据id
 	 * @return 模板id
 	 */
 	public String getTplIdByDataId(String dataId) {
 		List<SysFormDataIndex> dataIndexlist = dao.rawQueryListByWhere(SysFormDataIndex.class, Where.create().eq(SysFormDataIndex::getDataId, dataId));
 		if (StringUtils.isEmpty(dataIndexlist)) {
 			return null;
 		}
 		String formId = dataIndexlist.get(0).getFormId();
 		SysFormDto form = formSrc.getSysFormById(formId);
 		if (form == null) {
 			return null;
 		}
 		return form.getTplId();
 	}


	/**
	 * 根据数据id校验表单必填项
	 * @category 根据数据id校验表单必填项
	 * @param dataId 数据id
	 * @return
	 */
	@Override
	public List<JSONObject> validateFormDataRequiredTrue(String dataId, List<String> formIdList) {

		List<String> formIdResult = new ArrayList<>();
		if (StringUtils.isEmpty(formIdList)) {
			Where where = Where.create().eq(SysFormDataIndex::getDataId, dataId);
			List<SysFormDataIndex> dataIndexList = dao.queryData(SysFormDataIndex.class, where, null, null);
			if (StringUtils.isEmpty(dataIndexList)) {
				return null;
			}
			formIdResult = dataIndexList.stream().map(item -> item.getFormId()).collect(Collectors.toList());
		} else {
			formIdResult.addAll(formIdList);
		}

		List<JSONObject> requiredTemp = new ArrayList<>();

		// ZZVVQHSX09WGG7VART0183
		List<String> queryFormIdList = new ArrayList<>();
		for (String formId : formIdResult) {
//			String formId = "ZZVVQHSX09WGG7VART0183";
			queryFormIdList.add(formId);
			SysFormDto formContent = contentSrc.getSysFormContent(formId, null);
			String content = formContent.getFormContent();
			List<JSONObject> requiredList = StringUtils.isNotEmpty(content) ? findContentRequired(JSONObject.parseObject(content)) : null;
			if (StringUtils.isNotEmpty(requiredList)) {
				JSONObject formObj = new JSONObject();
				formObj.put("formId", formId);
				formObj.put("requiredJsonList", requiredList);
				requiredTemp.add(formObj);
			}
		}

		if (StringUtils.isEmpty(requiredTemp)) {
			//没有必填项
			return null;
		}

		List<SysFormDto> formList = formSrc.getSysFormByIds(StringUtils.join(queryFormIdList, ","));
		Map<String, SysFormDto> formMap = formList.stream().collect(Collectors.toMap(SysFormDto::getFormId, v -> v));

		Map<String, Map<String, String>> dataRes = getSysTplData(new ArrayList<>(Arrays.asList(dataId)), null);
		Map<String, String> dataMap = dataRes == null ? null : dataRes.get(dataId);

		List<JSONObject> res = new ArrayList<>();
		for (JSONObject obj : requiredTemp) {
			SysFormDto form = formMap.get(obj.getString("formId"));
			String formName = form != null ? form.getFormName() : "";
			String formAlias = form != null ? form.getFormAlias() : "";
			Object requiredJsonList = obj.get("requiredJsonList");
			if (requiredJsonList == null) {
				continue;
			}
			List<JSONObject> rlist = (List<JSONObject>) requiredJsonList;
			if (StringUtils.isEmpty(rlist)) {
				continue;
			}

			List<JSONObject> colList = new ArrayList<>();
			List<String> colNameList = new ArrayList<>();
			for (JSONObject col : rlist) {
				String label = col.getString("label");
				String sjk = col.getString("sjk");
				String sjkzd = col.getString("sjkzd");
				String key = sjk.toUpperCase()+"."+sjkzd.toUpperCase()+(StringUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());
				String value = StringUtils.isEmpty(dataMap) ? null : dataMap.get(key);
				if (StringUtils.isEmpty(value)) {
					//未填写
					colList.add(col);
					colNameList.add("【"+label+"】");
				}
			}
			if (StringUtils.isNotEmpty(colList)) {
				obj.put("formName", formName);
				obj.put("requiredJsonList", colList);
				obj.put("errorMsg", "必填项"+(StringUtils.join(colNameList, ""))+"未填写");
				res.add(obj);
			}
		}
		return res;
	}


	private List<JSONObject> findContentRequired(JSONObject jobj) {
		if (ObjUtils.isEmpty(jobj)) {
			return null;
		}
		List<JSONObject> result = new ArrayList<>();
		List<JSONObject> jsonList = new ArrayList<>();
		findContentJsonObject(jsonList, jobj);
		for (JSONObject obj : jsonList) {
			//递归
			if (!obj.containsKey("required") || obj.getBoolean("required") == null || !obj.getBoolean("required")) {
				//非必填项
				continue;
			}
			if (!obj.containsKey("display") || obj.getBoolean("display") == null || !obj.getBoolean("display")) {
				//不显示项
				continue;
			}
			String sjk = obj.getString("sjk");
			String sjkzd = obj.getString("sjkzd");
			if (StringUtils.isAnyEmpty(sjk, sjkzd)) {
				//数据库和字段信息有任何为空
				continue;
			}
			result.add(obj);
//			List<String> tableColList = result.computeIfAbsent(formId, v -> new ArrayList<>());
//			String key = sjk.toUpperCase()+"."+sjkzd.toUpperCase()+(StringUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());
//			if (!tableColList.contains(key)) {
//				tableColList.add(key);
//			}
		}

		return result;

		/*
		Object steps = jobj.get("steps");
		JSONArray stepsArray = (steps != null && steps instanceof JSONArray) ? (JSONArray) steps : null;
		if (stepsArray != null) {
			for (Object step : stepsArray) {
				if (step == null || !(step instanceof JSONObject)) {
					continue;
				}
				Object stepsColumn = (step != null && step instanceof JSONObject) ? ((JSONObject)step).get("column") : null;
				if (stepsColumn == null || !(stepsColumn instanceof JSONArray)) {
					continue;
				}
				JSONArray columnArray = (JSONArray) stepsColumn;
				for (Object obj : columnArray) {
					if (!(obj instanceof JSONObject)) {
						continue;
					}
					JSONObject colObj = (JSONObject) obj;
					//递归
					findContentRequre(formId, formAlias, result, colObj);

					Object group = colObj.get("group");
					JSONArray groupArray = (group != null && group instanceof JSONArray) ? (JSONArray) group : null;
					if (groupArray != null) {
						for (Object g : groupArray) {
							if (g == null || !(g instanceof JSONObject)) {
								continue;
							}
							Object groupColumn = (g != null && g instanceof JSONObject) ? ((JSONObject)g).get("column") : null;
							if (groupColumn == null || !(groupColumn instanceof JSONArray)) {
								continue;
							}
							JSONArray groupArr = (JSONArray) groupColumn;
							for (Object groupObj : groupArr) {
								if (!(groupObj instanceof JSONObject)) {
									continue;
								}
								JSONObject gobj = (JSONObject) groupObj;
								//递归
								findContentRequre(formId, formAlias, result, gobj);
								if (!colObj.containsKey("required") || colObj.getBoolean("required") == null || !colObj.getBoolean("required")) {
									//非必填项
									continue;
								}
								String sjk = colObj.getString("sjk");
								String sjkzd = colObj.getString("sjkzd");
								if (StringUtils.isAnyEmpty(sjk, sjkzd)) {
									//数据库和字段信息有任何为空
									continue;
								}
								List<String> tableColList = result.computeIfAbsent(formId, v -> new ArrayList<>());
								String key = sjk.toUpperCase()+"."+sjkzd.toUpperCase()+(StringUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());
								if (!tableColList.contains(key)) {
									tableColList.add(key);
								}
							}
						}
					}

					if (!colObj.containsKey("required") || colObj.getBoolean("required") == null || !colObj.getBoolean("required")) {
						//非必填项
						continue;
					}
					String sjk = colObj.getString("sjk");
					String sjkzd = colObj.getString("sjkzd");
					if (StringUtils.isAnyEmpty(sjk, sjkzd)) {
						//数据库和字段信息有任何为空
						continue;
					}
					List<String> tableColList = result.computeIfAbsent(formId, v -> new ArrayList<>());
					String key = sjk.toUpperCase()+"."+sjkzd.toUpperCase()+(StringUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());
					if (!tableColList.contains(key)) {
						tableColList.add(key);
					}
				}
			}
		}
		Object group = jobj.get("group");
		JSONArray groupArray = (group != null && group instanceof JSONArray) ? (JSONArray) group : null;
		if (groupArray != null) {
			for (Object g : groupArray) {
				if (g == null || !(g instanceof JSONObject)) {
					continue;
				}
				Object groupColumn = (g != null && g instanceof JSONObject) ? ((JSONObject)g).get("column") : null;
				if (groupColumn == null || !(groupColumn instanceof JSONArray)) {
					continue;
				}
				JSONArray columnArray = (JSONArray) groupColumn;
				for (Object obj : columnArray) {
					if (!(obj instanceof JSONObject)) {
						continue;
					}
					JSONObject colObj = (JSONObject) obj;
					//递归
					findContentRequre(formId, formAlias, result, colObj);
					if (!colObj.containsKey("required") || colObj.getBoolean("required") == null || !colObj.getBoolean("required")) {
						//非必填项
						continue;
					}
					String sjk = colObj.getString("sjk");
					String sjkzd = colObj.getString("sjkzd");
					if (StringUtils.isAnyEmpty(sjk, sjkzd)) {
						//数据库和字段信息有任何为空
						continue;
					}
					List<String> tableColList = result.computeIfAbsent(formId, v -> new ArrayList<>());
					String key = sjk.toUpperCase()+"."+sjkzd.toUpperCase()+(StringUtils.isEmpty(formAlias)?"":"."+formAlias.toUpperCase());
					if (!tableColList.contains(key)) {
						tableColList.add(key);
					}
				}
			}
		}
		*/

	}


	private void findContentJsonObject(List<JSONObject> result, JSONObject jobj) {
		if (ObjUtils.isEmpty(jobj)) {
			return;
		}
		Object steps = jobj.get("steps");
		JSONArray stepsArray = (steps != null && steps instanceof JSONArray) ? (JSONArray) steps : null;
		Object group = jobj.get("group");
		JSONArray groupArray = (group != null && group instanceof JSONArray) ? (JSONArray) group : null;
		JSONArray list = new JSONArray();
		if (stepsArray != null) {
			list.addAll(stepsArray);
		}
		if (groupArray != null) {
			list.addAll(groupArray);
		}
		if (StringUtils.isNotEmpty(list)) {
			for (Object o : list) {
				if (o == null || !(o instanceof JSONObject)) {
					continue;
				}
				Object columnArrayObj =  ((JSONObject)o).get("column");
//				if (columnArrayObj == null || !(columnArrayObj instanceof JSONArray)) {
//					continue;
//				}
				JSONArray columnArray = (columnArrayObj != null && columnArrayObj instanceof JSONArray) ? (JSONArray) columnArrayObj : null;
				Object columnGroup = ((JSONObject)o).get("group");
				JSONArray columnGroupArray = (columnGroup != null && columnGroup instanceof JSONArray) ? (JSONArray) columnGroup : null;

				JSONArray columnList = new JSONArray();
				if (columnArray != null) {
					columnList.addAll(columnArray);
				}
				if (columnGroupArray != null) {
					for (Object go : columnGroupArray) {
						if (!(go instanceof JSONObject)) {
							continue;
						}
						Object column = ((JSONObject) go).get("column");
						if (column != null && column instanceof JSONArray) {
							columnList.addAll((JSONArray) column);
						}
					}
				}
				for (Object obj : columnList) {
					if (!(obj instanceof JSONObject)) {
						continue;
					}
					result.add((JSONObject) obj);
					JSONObject colObj = (JSONObject) obj;
					//递归
					findContentJsonObject(result, colObj);
				}
			}
		}

	}

}
