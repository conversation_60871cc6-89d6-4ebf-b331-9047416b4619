package com.yunhesoft.system.tools.form.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.druid.util.StringUtils;
import com.yunhesoft.system.kernel.service.impl.EntityServiceImpl;
import com.yunhesoft.system.kernel.service.model.TableColumnInfo;
import com.yunhesoft.system.kernel.service.model.TableInfo;
import com.yunhesoft.system.tools.form.dto.SysTableColumnDto;
import com.yunhesoft.system.tools.form.dto.SysTableDto;
import com.yunhesoft.system.tools.form.service.SysTableService;

@Service
public class SysTableServiceImpl implements SysTableService {
	
	@Autowired
	private EntityServiceImpl dao;

	/**
	 * 获取数据库表列表
	 */
	@Override
	public List<SysTableDto> getTables() {
		List<SysTableDto> res = new ArrayList<SysTableDto>();
		List<TableInfo> tablelist = dao.tableList(null);
		
		if (tablelist != null && tablelist.size() > 0) {
			SysTableDto dto = new SysTableDto();
			dto.setModuleCode("defaultModule");
			dto.setModuleName("默认模块");
			List<Map<String, String>> tables = new ArrayList<Map<String, String>>();
			for (TableInfo table : tablelist) {
				String value = table.getTableName();
				String name = table.getTableComment();
				if (name != null && name.length() > 0) {
					name = name + "("+value+")";
				} else {
					name = value;
				}
				HashMap<String, String> map = new HashMap<String, String>();
				map.put("name", name);
				map.put("value", value);
				tables.add(map);
			}
			dto.setTables(tables);
			res.add(dto);
		}
		
		return res;
	}

	
	/**
	 * 获取数据库表字段列表
	 * @param tableNames 表名（多个逗号隔开）
	 */
	@Override
	public List<SysTableColumnDto> getTableColumns(String tableNames) {
		if (StringUtils.isEmpty(tableNames)) {
			throw new RuntimeException("传入参数有误");
		}
		List<SysTableColumnDto> res = new ArrayList<SysTableColumnDto>();
		String[] tableArray = tableNames.split(",");
		for (String tableName : tableArray) {
			List<TableInfo> tablelist = dao.tableList(tableName);
			if (tablelist == null || tablelist.size() <= 0) {
				continue;
			}
			TableInfo table = tablelist.get(0);
			List<TableColumnInfo> list = dao.tableColumnList(tableName);
			SysTableColumnDto vo = new SysTableColumnDto();
			String tableLabel = table.getTableComment();
			if (StringUtils.isEmpty(tableLabel)) {
				tableLabel = tableName;
			} else {
				tableLabel = tableLabel + "("+tableName+")";
			}
			vo.setTableName(table.getTableName());
			vo.setTableLabel(table.getTableComment());
			if (list != null && list.size() > 0) {
				List<HashMap<String, String>> columns = new ArrayList<HashMap<String, String>>();
				for (TableColumnInfo column : list) {
					String columnName = column.getColumnName();
					String columnLabel = column.getColumnComment();
					if (StringUtils.isEmpty(tableLabel)) {
						columnLabel = columnName;
					} else {
						columnLabel = columnLabel + "("+columnName+")";
					}
					HashMap<String, String> map = new HashMap<String, String>();
					map.put("name", columnName);
					map.put("label", columnLabel);
					map.put("tableName", tableName);
					columns.add(map);
				}
				vo.setColumnList(columns);
			}
			res.add(vo);
		}
		return res;
	}

}
