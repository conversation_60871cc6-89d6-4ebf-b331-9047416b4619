package com.yunhesoft.system.tools.formulaParam.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;
import com.yunhesoft.system.tools.formulaParam.entity.dto.FormulaParamQueryDto;
import com.yunhesoft.system.tools.formulaParam.service.IFormulaParamService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping("/system/tools/formulaParam/formulaParam")
@Api(tags = "公式参数相关")
public class FormulaParamController extends BaseRestController {

	@Autowired
	private IFormulaParamService serv;
	
	/**
	 * @category 获取基础公式树形
	 * @param param themesId属性
	 * @return
	 */
	@RequestMapping(value = "/getBasicFormulaTree", method = RequestMethod.POST)
	@ApiOperation("获取基础公式设置树形")
	public Res<?> getBasicFormulaTree(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.getBasicFormulaTree(param));
	}
	
	/**
	 * @category 获取动态公式树形
	 * @param param
	 * @return
	 */
	@RequestMapping(value = "/getDynamicFormulaTreeByPid", method = RequestMethod.POST)
	@ApiOperation("获取动态公式树形")
	public Res<?> getDynamicFormulaTreeByPid(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.getDynamicFormulaTreeByPid(param.getModuleCode(), param.getPid()));
	}
	
	/**
	 * @category 获取扩展公式设置树形
	 * @param param themesId属性
	 * @return
	 */
	@RequestMapping(value = "/getExtFormulaTree", method = RequestMethod.POST)
	@ApiOperation("获取扩展公式设置树形")
	public Res<?> getClassTreeData(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.getExtFormulaTree(param));
	}
	
	/**
	 * @category 保存扩展参数设置树形
	 * @param param MtmIndexFormulaTreeParam对象
	 * @return
	 */
	@RequestMapping(value = "/saveExtFormulaTreeNode", method = RequestMethod.POST)
	@ApiOperation("保存扩展参数设置树形")
	public Res<?> saveExtFormulaTreeNode(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.saveExtFormulaTreeNode(param));
	}
	
	/**
	 * @category 查询公式数据
	 * @param param MtmIndexFormulaTreeParam对象
	 * @return
	 */
	@RequestMapping(value = "/loadExtFormulaData", method = RequestMethod.POST)
	@ApiOperation("查询公式数据")
	public Res<?> loadExtFormulaData(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.loadExtFormulaData(param));
	}
	
	/**
	 * @category 保存参数公式
	 * @param param MtmIndexFormulaTreeParam对象
	 * @return
	 */
	@RequestMapping(value = "/saveExtFormulaData", method = RequestMethod.POST)
	@ApiOperation("保存参数公式")
	public Res<?> saveExtFormulaData(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.saveExtFormulaData(param));
	}
	/**
	 * @category 查询所有公式数据（当前主题及通用）
	 * @param param MtmIndexFormulaTreeParam对象
	 * @return
	 */
	@RequestMapping(value = "/getAllFormula", method = RequestMethod.POST)
	@ApiOperation("查询所有公式数据（当前主题及通用）")
	public Res<?> getAllFormula(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.getAllFormula(param));
	}
	
	/**
	 *	公式正确性校验
	 * @param formulaVo
	 * @return
	 */
	@RequestMapping(value = "/checkFormula", method = RequestMethod.POST)
	@ApiOperation("公式正确性校验")
	public Res<?> checkFormula(@RequestBody MtmFormulaTreeVo formulaVo) {
		Res<String> res = new Res<String>();
		String ret = serv.checkFormula(formulaVo);
		res.setResult(ret);
		return res;
	}
	
	/**
	 * @category 获取常用公式参数列表
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/getOftenUseFormulaParamList", method = RequestMethod.POST)
	@ApiOperation("获取常用公式参数列表")
	public Res<?> getOftenUseFormulaParamList(@RequestBody FormulaParamQueryDto param) {
		return Res.OK(serv.getOftenUseFormulaParamList(param));
	}
	
}
