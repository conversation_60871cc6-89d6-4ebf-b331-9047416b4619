package com.yunhesoft.system.tools.formulaParam.entity.vo;


import java.util.List;

import com.yunhesoft.system.tools.classExec.entry.vo.MtmFormulaTreeVo;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class FormulaParamTreeVo extends MtmFormulaTreeVo {

	private static final long serialVersionUID = 1L;
	
	String id;
	String pid;
	Integer tmsort;	
	Boolean leafNode;
	List<FormulaParamTreeVo> itemList;
	
	/** 树形路径信息，逗号分割 */
    private String pathInfo;
    
    /** 公式名称变量 */
    private String formulaLabel;
}
