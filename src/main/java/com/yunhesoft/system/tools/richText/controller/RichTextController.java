package com.yunhesoft.system.tools.richText.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.richText.service.IRichTextService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @Description: 用于测试富文本编辑器
 * <AUTHOR>
 * @date 2021-12-08
 */

@RestController
@Api(tags = "富文本编辑器")
@RequestMapping("/system/tools/richText")
public class RichTextController extends BaseRestController {

	@Autowired
	private IRichTextService irts;

	@ApiOperation(value = "图片上传")
	@RequestMapping(value = "/upImg", method = RequestMethod.POST)
	public Res<?> testFunc(@RequestParam("editormd-image-file") MultipartFile image,
			@RequestParam("moduleCode") String moduleCode) {
		return Res.OK(irts.uploadImg(image, moduleCode));
	}

	@ApiOperation(value = "文件上传")
	@RequestMapping(value = "/upFile", method = RequestMethod.POST)
	public Res<?> fileFunc(@RequestParam("file") MultipartFile image, @RequestParam("moduleCode") String moduleCode) {
		return Res.OK(irts.uploadFile(image, moduleCode));
	}
}
