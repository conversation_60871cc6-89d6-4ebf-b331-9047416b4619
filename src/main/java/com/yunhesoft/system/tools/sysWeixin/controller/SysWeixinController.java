package com.yunhesoft.system.tools.sysWeixin.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.component.SystemProperties;
import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.kernel.service.model.Pagination;
import com.yunhesoft.system.menu.service.ISysMenuLibInitService;
import com.yunhesoft.system.menu.service.ISysMenuLibService;
import com.yunhesoft.system.tools.sysWeixin.entity.SysWeixin;
import com.yunhesoft.system.tools.sysWeixin.service.ISysWeixinService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 系统配置Controller
 * 
 * <AUTHOR>
 * @date 2021-03-31
 */
@RestController
@Api(tags = "微信配置")
@RequestMapping("/system/weixin")
public class SysWeixinController extends BaseRestController {

	@Autowired
	private ISysWeixinService sysWeixinService;

	@Autowired
	private ISysMenuLibService sysMenuLibService;

	@Autowired
	private SystemProperties props;

	@Autowired
	private ISysMenuLibInitService sysInitService;

	@ApiOperation(value = "应用程序标题")
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/app-title", method = { RequestMethod.GET })
	public Res<String> appTitle() {
		String title = sysWeixinService.getSysWeixin("sys_appname");// 系统参数中获取程序名称
		if (title == null) {
			title = String.valueOf(props.getPropertyUtf8("app.application", "运和管理系统 "));
		}
		return Res.OK(title);
	}

	/**
	 * 查询系统配置列表
	 */
	@ApiOperation(value = "系统配置 列表")
	@GetMapping("/list")
	public Res<List<SysWeixin>> list(SysWeixin sysWeixin) {
		Pagination<?> page = getRequestPagination();
		return sysWeixinService.selectSysWeixinList(sysWeixin, page);
	}

	/**
	 * 获取系统配置详细信息
	 */
	@SuppressWarnings("unchecked")
	@ApiOperation(value = "获取系统配置 详细信息")
	@GetMapping(value = "/getInfo/{id}")
	public Res<SysWeixin> getInfo(@PathVariable("id") String id) {
		return Res.OK(sysWeixinService.selectSysWeixinById(id));
	}

	/**
	 * 新增系统配置
	 */
	@ApiOperation(value = "新增系统配置")
	@PostMapping(value = "/add")
	public Res<?> add(@RequestBody SysWeixin sysWeixin) {
		try {
			return Res.OK(sysWeixinService.insertSysWeixin(sysWeixin));
		} catch (Exception e) {
			return Res.FAIL(e.getMessage());
		}
	}

	/**
	 * 根据key修改系统参数值
	 */
	@ApiOperation(value = "根据key修改系统参数值")
	@PostMapping(value = "/setSysConfigByKey")
	public Res<?> updateByKey(@RequestBody SysWeixin sysWeixin) {
		try {
			return Res.OK(sysWeixinService.setSysWeixinValueByKey(sysWeixin));
		} catch (Exception e) {
			return Res.FAIL(e.getMessage());
		}
	}

	/**
	 * 修改系统配置
	 */
	@ApiOperation(value = "修改系统配置")
	@PostMapping(value = "/edit")
	public Res<?> edit(@RequestBody SysWeixin sysWeixin) {
		return Res.OK(sysWeixinService.updateSysWeixin(sysWeixin));
	}

	/**
	 * 删除系统配置
	 */
	
	  @ApiOperation(value = "删除系统配置")
	 
	  @GetMapping("/delete/{ids}") 
	  public Res<?> remove(@PathVariable String[] ids){ 
		  return Res.OK(sysWeixinService.deleteSysWeixinByIds(ids)); 
	  }
	 

	/**
	 * 获得模块列表
	 */
	@ApiOperation(value = "获得模块列表")
	@RequestMapping(value = "/getModuleList", method = { RequestMethod.GET })
	public Res<?> getModuleList() {
		return Res.OK(sysMenuLibService.getModuleList());
	}

	/**
	 * 获取系统配置值
	 */
	@ApiOperation(value = "获取系统配置值")
	@GetMapping(value = "/{key}")
	public Res<?> getSysConfigValue(@PathVariable("key") String key) {
		return Res.OK(sysWeixinService.getSysWeixin(key));
	}

	@ApiOperation(value = "帮助文件地址")
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/helpUrl", method = { RequestMethod.GET })
	public Res<String> getHelpUrl() {
		String url = sysWeixinService.getSysWeixin("sys_helpDocUrl");// 系统参数中获取程序名称
		if (url == null) {
			url = "";
		}
		return Res.OK(url);
	}

	/**
	 * 判断模块是否注册
	 */
	@ApiOperation(value = "判断模块是否注册")
	@RequestMapping(value = "/isModuleRegister", method = { RequestMethod.GET, RequestMethod.POST })
	public Res<?> isModuleRegister(@RequestParam String moduleCode) {
		return Res.OK(sysInitService.isModuleRegister(moduleCode));
	}

}
