package com.yunhesoft.system.tools.todo.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.yunhesoft.core.common.model.Res;
import com.yunhesoft.system.kernel.controller.BaseRestController;
import com.yunhesoft.system.tools.todo.service.TodoService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 系统待办服务
 * 
 * <AUTHOR>
 * @date 2021/10/10 10:36
 */
@RestController
@RequestMapping("/system/todo")
@Api(tags = "系统待办服务")
public class TodoController extends BaseRestController {

	@Autowired
	private TodoService srv; // 待办服务

	/**
	 * 获取当前用户待办数量
	 */
	@RequestMapping(value = "/getTodoCount", method = RequestMethod.POST)
	@ApiOperation(value = "获取当前用户待办数量")
	public Res<?> getTodoCount() {
		return Res.OK(srv.getTodoCount());
	}

	/**
	 * 根据登录账号获取用户待办数量
	 * 
	 * @return
	 */
	@ApiOperation(value = "根据登录账号获取用户待办数量")
	@GetMapping(value = "/getTodoCountById/{loginName}")
	public Res<?> getTodoCount(@PathVariable String loginName) {
		return Res.OK(srv.getTodoCount(loginName));
	}

	/**
	 * 获取当前用户未读数量
	 */
	@RequestMapping(value = "/getUnReadCount", method = RequestMethod.POST)
	@ApiOperation(value = "获取当前用户未读数量")
	public Res<?> getUnReadCount() {
		return Res.OK(srv.getUnReadCount());
	}

	/**
	 * 根据登录账号获取用户未读数量
	 * 
	 * @return
	 */
	@ApiOperation(value = "根据登录账号获取用户未读数量")
	@GetMapping(value = "/getUnReadCountById/{loginName}")
	public Res<?> getUnReadCount(@PathVariable String loginName) {
		return Res.OK(srv.getUnReadCount(loginName));
	}

	/**
	 * 获取当前用户待办列表
	 * 
	 * @return
	 */
	@RequestMapping(value = "/getTodoList", method = RequestMethod.POST)
	@ApiOperation(value = "获取当前用户待办列表")
	public Res<?> getTodoList() {
		return Res.OK(srv.getTodoList());
	}

	@RequestMapping(value = "/clearTodoCachedForUser", method = RequestMethod.GET)
	@ApiOperation(value = "从redis缓存中清理个人的待办信息")
	public Res<?> clearTodoCachedForUser(String loginName) {
		srv.clearTodoCachedForUser(loginName);
		return Res.OK("清理完成");
	}
	/**
	 * 测试代码
	 * 
	 * @return
	 */
	/*
	 * @RequestMapping(value = "/clearTodoTest", method = RequestMethod.POST)
	 * 
	 * @ApiOperation(value="清除待办项（test）") public Res<?> clearTodoTest() {
	 * //srv.clearTodoCached("system", "emp"); return Res.OK(); }
	 */

}