package com.yunhesoft.system.tools.toolbarLink.entity.po;

import com.yunhesoft.core.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Description: 系统工具栏链接$
 * @date 2022/3/17
 */
@ApiModel(value = "系统工具栏链接")
@Entity
@Data
@Table(name = "SYS_TOOLBAR_LINK")
public class SysToolBarLink extends BaseEntity {
    @ApiModelProperty(value = "链接名称")
    @Column(name = "NAME", length = 100)
    private String name;

    @ApiModelProperty(value = "图片地址")
    @Column(name = "IMGURL", length = 1000)
    private String imgUrl;

    /** 0系统组件(默认)，1外部连接 */
    @ApiModelProperty(value = "跳转类型")
    @Column(name = "TARGETURLTYPE", length = 100)
    private String targetUrlType;

    @ApiModelProperty(value = "跳转地址")
    @Column(name = "TARGETURL", length = 1000)
    private String targetUrl;
    
    @ApiModelProperty(value = "动画方式")
    @Column(name = "ANIMATIONTYPE", length = 1000)
    private String animationType;
    
    @ApiModelProperty(value = "颜色")
    @Column(name = "COLOR", length = 1000)
    private String color;
    
    @ApiModelProperty(value = "链接方式")
    @Column(name = "LINKTYPE", length = 200)
    private String linkType;
    
    
    /** 0弹出窗体（默认），1新页面打开 */
    @ApiModelProperty(value = "显示方式")
    @Column(name = "SHOWTYPE")
    private Integer showType;
    
    @ApiModelProperty(value = "悬浮提示内容")
    @Column(name = "TIPTEXT", length = 500)
    private String tipText;

    @ApiModelProperty(value = "备注信息")
    @Column(name = "MEMO", length = 1000)
    private String memo;

    /** 控制可见性 */
    @ApiModelProperty(value = "是否隐藏")
    @Column(name = "TMSHOW")
    private Integer tmShow;

    @ApiModelProperty(value = "是否启用")
    @Column(name = "TMUSED")
    private Integer  tmUsed;

    @ApiModelProperty(value = "排序")
    @Column(name = "TMSORT")
    private Integer tmSort;

    /** 所属机构id */
    @ApiModelProperty(value = "机构代码")
    @Column(name = "ORGDM", length = 50)
    private String orgDm;
}
