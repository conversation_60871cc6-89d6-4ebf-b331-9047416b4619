package com.yunhesoft.workbench.entity.po;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.yunhesoft.core.common.entity.BaseEntity;

import lombok.Getter;
import lombok.Setter;

/**
 * 班长工作台数据表
 * 
 */
@Entity
@Setter
@Getter
@Table(name = "WORKBENCH_DATA_INFO")
public class WorkbenchDataInfo extends BaseEntity {
	private static final long serialVersionUID = 1L;

	/** 月份 */
	@Column(name = "YF", length = 10)
	private String yf;

	/** 机构代码 */
	@Column(name = "ORGCODE", length = 50)
	private String orgcode;

	/** 班次代码 */
	@Column(name = "SHIFTCODE", length = 50)
	private String shiftcode;

	/** 班次名称 */
	@Column(name = "SHIFTNAME", length = 50)
	private String shiftname;

	/** 日期 */
	@Column(name = "RQ", length = 10)
	private String rq;

	/** 员工编码 */
	@Column(name = "EMPID", length = 50)
	private String empid;

	/** 当班岗位id */
	@Column(name = "POSTID", length = 50)
	private String postid;

	/** 当班岗位系数 */
	@Column(name = "POSTINDEX")
	private Double postindex;

	/** 状态id */
	@Column(name = "STATUSID", length = 50)
	private String statusid;

	/** 排序 */
	@Column(name = "TMSORT")
	private Integer tmsort;

	/** 原岗位id */
	@Column(name = "OLDPOSTID", length = 50)
	private String oldpostid;

	/** 原岗位系数 */
	@Column(name = "OLDPOSTINDEX")
	private Double oldpostindex;

	/** 主操作对象 */
	@Column(name = "MAINOPT", length = 1000)
	private String mainopt;

	/** 主操作对象名称 */
	@Column(name = "MAINOPTNAME", length = 2000)
	private String mainoptname;

	/** 子操作对象 */
	@Column(name = "SUBOPT", length = 1000)
	private String subopt;

	/** 子操作对象名称 */
	@Column(name = "SUBOPTNAME", length = 2000)
	private String suboptname;

	/** 工作小时 */
	@Column(name = "WORKHOUR")
	private Double workhour;
}
