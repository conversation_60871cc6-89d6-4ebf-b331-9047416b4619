server:
  port: 7900
spring:
  application:
    name: tm4-cloud-gateway
  cloud:
    gateway:
      routes:
        #TDI
        #        - id: tm4-paas-tdi
        #          uri: lb://TM4-PAAS-TDI
        #          predicates:
        #            - Path=/tm4main/system/tools/diy/pagelib/getCurrentUserPages,/tm4main/tdi/**
        #          filters:
        #            #- Striprefix=1
        #            - name: Retry
        #              args:
        #                retries: 3
        #                statuses: INTERNAL_SERVER_ERROR
        #                methods: GET,POST
        #奖金模块
        #        - id: tm4-paas-prize
        #          uri: lb://TM4-PAAS-PRIZE
        #          predicates:
        #            # 公共路径：
        ##                        - Path=/tm4main/report/**,/tm4main/system/**,/tm4main/accountForm/**,/tm4main/accountSyn/**,/tm4main/auth/**,/tm4main/auth-test/**,/tm4main/auth-systemskin/**,/tm4main/, /**,/tm4main/menu/**,/tm4main/menuLib/**,/tm4main/redis-queue/**,/tm4main/tds/**,/tm4main/tdsBtn/**,/tm4main/tdsEdit/**,/tm4main/calendar/**,/tm4main/dbtools/**,/tm4main/diyPageAnalysis/**,/tm4main/files/**,/tm4main/Path/**,/tm4main/userOnline/**,/tm4main/tool/**,/tm4main/sms/**,/tm4main/iamgeGalleryService/**
        #            # 奖金模块路径：
        #            #            - Path=/tm4main/itfc/**,/tm4main/prize/**
        #            # 公共+奖金模块路径：
        #            - Path=>
        #              /tm4main/itfc/**,
        #              /tm4main/prize/**,
        #
        #              /tm4main/report/**,
        #              /tm4main/system/**,
        #              /tm4main/accountForm/**,
        #              /tm4main/accountSyn/**,
        #              /tm4main/auth/**,
        #              /tm4main/auth-test/**,
        #              /tm4main/auth-systemskin/**,
        #              /tm4main/menu/**,
        #              /tm4main/menuLib/**,
        #              /tm4main/redis-queue/**,
        #              /tm4main/tds/**,
        #              /tm4main/tdsBtn/**,
        #              /tm4main/tdsEdit/**,
        #              /tm4main/calendar/**,
        #              /tm4main/dbtools/**,
        #              /tm4main/diyPageAnalysis/**,
        #              /tm4main/files/**,
        #              /tm4main/Path/**,
        #              /tm4main/userOnline/**,
        #              /tm4main/tool/**,
        #              /tm4main/sms/**,
        #              /tm4main/iamgeGalleryService/**,
        #              /tm4main/shift/**
        #          filters:
        #            #            - Striprefix=1
        #            - name: Retry
        #              args:
        #                retries: 3
        #                statuses: INTERNAL_SERVER_ERROR
        #                methods: GET,POST

#        #TLM（目标传导、日考核）
#        - id: tm4-paas-tlm
#          uri: lb://TM4-PAAS-TLM
#          predicates:
#            # 目标传导模块路径：
#            - Path=>
#              /tm4main/regime/**,
#              /tm4main/mtm/**,
#              /tm4main/mtmScoreRuleSet/**,
#              /tm4main/gdsh/**,
#              /tm4main/jobCheckPlan/**,
#              /tm4main/asscenter/**,
#              /tm4main/flow/**,
#              /tm4main/busi/**,
#              /tm4main/tmsf/**,
#              /tm4main/openserv/**,
#              /tm4main/wikin-workflow/**,
#              /tm4main/datav/**,
#              /tm4main/datavSetting/**,
#              /tm4main/datavLoginJump/**,
#              /tm4main/notice/**
#          filters:
#            #            - Striprefix=1
#            - name: Retry
#              args:
#                retries: 3
#                statuses: INTERNAL_SERVER_ERROR
#                methods: GET,POST

        #岗位工作清单模块
        - id: tm4-paas-joblist
          uri: lb://TM4-PAAS-JOBLIST
          predicates:
            # 岗位工作清单模块路径：
            - Path=>
              /tm4main/accountAbnormal/**,
              /tm4main/accountTools/**,
              /tm4main/bzjs/**,
              /tm4main/joblist/**,
              /tm4main/leanCosting/**,
              /tm4main/CostBgcsszb/**,
              /tm4main/costInputData/**,
              /tm4main/costItemLedger/**,
              /tm4main/costQuery/**,
              /tm4main/dayReportLr/**,
              /tm4main/installationMonthReport/**,
              /tm4main/shiftInstrumentWrite/**,
              /tm4main/stepReport/**,
              /tm4main/cost/**,
              /tm4main/mobile/**,
              /tm4main/workbench/**,
              /tm4main/aip/**,
              /tm4main/OcrErrorList/**,
              /tm4main/ocrManager/**,
              /tm4main/ocr/**,
              /tm4main/tmtask/**,
              /tm4main/task/**,
              /tm4main/assesssource/**,
              /tm4main/race/**,
              /tm4main/check/**,
              /tm4main/checkFlowSetting/**,
              /tm4main/checkAnalyData/**,
              /tm4main/tm4CheckModel/**,
              /tm4main/singleSignOn/**,
              /tm4main/tmsf/**,
              /tm4main/joblistQualityIndex/**,
              /tm4main/joblist*/**,
              /tm4main/operCard/**,
              /tm4main/evaluation/**
          filters:
            #            - Striprefix=1
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR
                methods: GET,POST

          #系统模块
        - id: tm4-system
          uri: lb://TM4-SYSTEM
          predicates:
            # 公共路径：
            #                        - Path=/tm4main/report/**,/tm4main/system/**,/tm4main/accountForm/**,/tm4main/accountSyn/**,/tm4main/auth/**,/tm4main/auth-test/**,/tm4main/auth-systemskin/**,/tm4main/, /**,/tm4main/menu/**,/tm4main/menuLib/**,/tm4main/redis-queue/**,/tm4main/tds/**,/tm4main/tdsBtn/**,/tm4main/tdsEdit/**,/tm4main/calendar/**,/tm4main/dbtools/**,/tm4main/diyPageAnalysis/**,/tm4main/files/**,/tm4main/Path/**,/tm4main/userOnline/**,/tm4main/tool/**,/tm4main/sms/**,/tm4main/iamgeGalleryService/**
            # 奖金模块路径：
            #            - Path=/tm4main/itfc/**,/tm4main/prize/**
            # 公共+奖金模块路径：
            - Path=>
              /tm4main/report/**,
              /tm4main/system/**,
              /tm4main/accountForm/**,
              /tm4main/accountSyn/**,
              /tm4main/auth/**,
              /tm4main/auth-test/**,
              /tm4main/auth-systemskin/**,
              /tm4main/menu/**,
              /tm4main/menuLib/**,
              /tm4main/redis-queue/**,
              /tm4main/tds/**,
              /tm4main/tdsBtn/**,
              /tm4main/tdsEdit/**,
              /tm4main/calendar/**,
              /tm4main/dbtools/**,
              /tm4main/diyPageAnalysis/**,
              /tm4main/files/**,
              /tm4main/Path/**,
              /tm4main/userOnline/**,
              /tm4main/tool/**,
              /tm4main/sms/**,
              /tm4main/iamgeGalleryService/**,
              /tm4main/shift/**
          filters:
            #            - Striprefix=1
            - name: Retry
              args:
                retries: 3
                statuses: INTERNAL_SERVER_ERROR
                methods: GET,POST

        #满意度模块（未用）
#        - id: tm4-paas-evaluation
#          uri: lb://TM4-PAAS-EVALUATION
#          predicates:
#             - Path=>
#               /tm4main/evaluation/**
#          filters:
#           #            - Striprefix=1
#            - name: Retry
#              args:
#                retries: 3
#                statuses: INTERNAL_SERVER_ERROR
#                methods: GET,POST


  boot:
    admin:
      client:
        url: http://127.0.0.1:9999
        #        instance.prefer-ip: true
        connect-timeout: 15000   # 连接超时设为15秒
        read-timeout: 20000      # 读取超时设为20秒
        instance:
          metadata:
            timeout: 30000       # 健康检查总超时30秒
eureka:
  client:
    service-url:
      #      defaultZone: http://************:30552/eureka/
      defaultZone: http://localhost:8761/eureka/

logging:
  level:
    org.springframework.cloud.gateway: DEBUG

# actuator
management:
  endpoints:
    enable-by-default: true
    web:
      #      base-path: /act
      exposure:
        #        include: health,info,env,metrics,httptrace,gateway,loggers
        include: "*"
    health:
      show-details: always
#  metrics:
#    tags:
#      application: "Metrics"   # 为指标添加应用标签
